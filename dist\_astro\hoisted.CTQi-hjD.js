function Mr(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function dl(l,e){l.prototype=Object.create(e.prototype),l.prototype.constructor=l,l.__proto__=e}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: <PERSON>, <EMAIL>
*/var Gt={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Ki={duration:.5,overwrite:!1,delay:0},Ho,lt,Ee,sr=1e8,Se=1/sr,ho=Math.PI*2,Gu=ho/4,qu=0,pl=Math.sqrt,Ku=Math.cos,Qu=Math.sin,rt=function(e){return typeof e=="string"},Ye=function(e){return typeof e=="function"},Nr=function(e){return typeof e=="number"},Vo=function(e){return typeof e>"u"},Pr=function(e){return typeof e=="object"},Et=function(e){return e!==!1},Uo=function(){return typeof window<"u"},jn=function(e){return Ye(e)||rt(e)},_l=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},mt=Array.isArray,po=/(?:-?\.?\d|\.)+/gi,gl=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,Ii=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Hs=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,ml=/[+-]=-?[.\d]+/,yl=/[^,'"\[\]\s]+/gi,ju=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,Ae,vr,_o,Go,qt={},Ss={},xl,vl=function(e){return(Ss=Qi(e,qt))&&Lt},qo=function(e,r){return console.warn("Invalid property",e,"set to",r,"Missing plugin? gsap.registerPlugin()")},In=function(e,r){return!r&&console.warn(e)},wl=function(e,r){return e&&(qt[e]=r)&&Ss&&(Ss[e]=r)||qt},zn=function(){return 0},Zu={suppressEvents:!0,isStart:!0,kill:!1},hs={suppressEvents:!0,kill:!1},Ju={suppressEvents:!0},Ko={},Qr=[],go={},bl,Bt={},Vs={},va=30,ds=[],Qo="",jo=function(e){var r=e[0],t,i;if(Pr(r)||Ye(r)||(e=[e]),!(t=(r._gsap||{}).harness)){for(i=ds.length;i--&&!ds[i].targetTest(r););t=ds[i]}for(i=e.length;i--;)e[i]&&(e[i]._gsap||(e[i]._gsap=new Ul(e[i],t)))||e.splice(i,1);return e},_i=function(e){return e._gsap||jo(or(e))[0]._gsap},Tl=function(e,r,t){return(t=e[r])&&Ye(t)?e[r]():Vo(t)&&e.getAttribute&&e.getAttribute(r)||t},Ot=function(e,r){return(e=e.split(",")).forEach(r)||e},$e=function(e){return Math.round(e*1e5)/1e5||0},qe=function(e){return Math.round(e*1e7)/1e7||0},Xi=function(e,r){var t=r.charAt(0),i=parseFloat(r.substr(2));return e=parseFloat(e),t==="+"?e+i:t==="-"?e-i:t==="*"?e*i:e/i},ef=function(e,r){for(var t=r.length,i=0;e.indexOf(r[i])<0&&++i<t;);return i<t},Cs=function(){var e=Qr.length,r=Qr.slice(0),t,i;for(go={},Qr.length=0,t=0;t<e;t++)i=r[t],i&&i._lazy&&(i.render(i._lazy[0],i._lazy[1],!0)._lazy=0)},Zo=function(e){return!!(e._initted||e._startAt||e.add)},Sl=function(e,r,t,i){Qr.length&&!lt&&Cs(),e.render(r,t,!!(lt&&r<0&&Zo(e))),Qr.length&&!lt&&Cs()},Cl=function(e){var r=parseFloat(e);return(r||r===0)&&(e+"").match(yl).length<2?r:rt(e)?e.trim():e},Pl=function(e){return e},Kt=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},tf=function(e){return function(r,t){for(var i in t)i in r||i==="duration"&&e||i==="ease"||(r[i]=t[i])}},Qi=function(e,r){for(var t in r)e[t]=r[t];return e},wa=function l(e,r){for(var t in r)t!=="__proto__"&&t!=="constructor"&&t!=="prototype"&&(e[t]=Pr(r[t])?l(e[t]||(e[t]={}),r[t]):r[t]);return e},Ps=function(e,r){var t={},i;for(i in e)i in r||(t[i]=e[i]);return t},bn=function(e){var r=e.parent||Ae,t=e.keyframes?tf(mt(e.keyframes)):Kt;if(Et(e.inherit))for(;r;)t(e,r.vars.defaults),r=r.parent||r._dp;return e},rf=function(e,r){for(var t=e.length,i=t===r.length;i&&t--&&e[t]===r[t];);return t<0},kl=function(e,r,t,i,n){var s=e[i],o;if(n)for(o=r[n];s&&s[n]>o;)s=s._prev;return s?(r._next=s._next,s._next=r):(r._next=e[t],e[t]=r),r._next?r._next._prev=r:e[i]=r,r._prev=s,r.parent=r._dp=e,r},zs=function(e,r,t,i){t===void 0&&(t="_first"),i===void 0&&(i="_last");var n=r._prev,s=r._next;n?n._next=s:e[t]===r&&(e[t]=s),s?s._prev=n:e[i]===r&&(e[i]=n),r._next=r._prev=r.parent=null},Jr=function(e,r){e.parent&&(!r||e.parent.autoRemoveChildren)&&e.parent.remove&&e.parent.remove(e),e._act=0},gi=function(e,r){if(e&&(!r||r._end>e._dur||r._start<0))for(var t=e;t;)t._dirty=1,t=t.parent;return e},nf=function(e){for(var r=e.parent;r&&r.parent;)r._dirty=1,r.totalDuration(),r=r.parent;return e},mo=function(e,r,t,i){return e._startAt&&(lt?e._startAt.revert(hs):e.vars.immediateRender&&!e.vars.autoRevert||e._startAt.render(r,!0,i))},sf=function l(e){return!e||e._ts&&l(e.parent)},ba=function(e){return e._repeat?ji(e._tTime,e=e.duration()+e._rDelay)*e:0},ji=function(e,r){var t=Math.floor(e=qe(e/r));return e&&t===e?t-1:t},ks=function(e,r){return(e-r._start)*r._ts+(r._ts>=0?0:r._dirty?r.totalDuration():r._tDur)},Ys=function(e){return e._end=qe(e._start+(e._tDur/Math.abs(e._ts||e._rts||Se)||0))},Xs=function(e,r){var t=e._dp;return t&&t.smoothChildTiming&&e._ts&&(e._start=qe(t._time-(e._ts>0?r/e._ts:((e._dirty?e.totalDuration():e._tDur)-r)/-e._ts)),Ys(e),t._dirty||gi(t,e)),e},Ml=function(e,r){var t;if((r._time||!r._dur&&r._initted||r._start<e._time&&(r._dur||!r.add))&&(t=ks(e.rawTime(),r),(!r._dur||qn(0,r.totalDuration(),t)-r._tTime>Se)&&r.render(t,!0)),gi(e,r)._dp&&e._initted&&e._time>=e._dur&&e._ts){if(e._dur<e.duration())for(t=e;t._dp;)t.rawTime()>=0&&t.totalTime(t._tTime),t=t._dp;e._zTime=-Se}},br=function(e,r,t,i){return r.parent&&Jr(r),r._start=qe((Nr(t)?t:t||e!==Ae?rr(e,t,r):e._time)+r._delay),r._end=qe(r._start+(r.totalDuration()/Math.abs(r.timeScale())||0)),kl(e,r,"_first","_last",e._sort?"_start":0),yo(r)||(e._recent=r),i||Ml(e,r),e._ts<0&&Xs(e,e._tTime),e},Dl=function(e,r){return(qt.ScrollTrigger||qo("scrollTrigger",r))&&qt.ScrollTrigger.create(r,e)},El=function(e,r,t,i,n){if(ea(e,r,n),!e._initted)return 1;if(!t&&e._pt&&!lt&&(e._dur&&e.vars.lazy!==!1||!e._dur&&e.vars.lazy)&&bl!==Wt.frame)return Qr.push(e),e._lazy=[n,i],1},of=function l(e){var r=e.parent;return r&&r._ts&&r._initted&&!r._lock&&(r.rawTime()<0||l(r))},yo=function(e){var r=e.data;return r==="isFromStart"||r==="isStart"},af=function(e,r,t,i){var n=e.ratio,s=r<0||!r&&(!e._start&&of(e)&&!(!e._initted&&yo(e))||(e._ts<0||e._dp._ts<0)&&!yo(e))?0:1,o=e._rDelay,u=0,f,c,h;if(o&&e._repeat&&(u=qn(0,e._tDur,r),c=ji(u,o),e._yoyo&&c&1&&(s=1-s),c!==ji(e._tTime,o)&&(n=1-s,e.vars.repeatRefresh&&e._initted&&e.invalidate())),s!==n||lt||i||e._zTime===Se||!r&&e._zTime){if(!e._initted&&El(e,r,i,t,u))return;for(h=e._zTime,e._zTime=r||(t?Se:0),t||(t=r&&!h),e.ratio=s,e._from&&(s=1-s),e._time=0,e._tTime=u,f=e._pt;f;)f.r(s,f.d),f=f._next;r<0&&mo(e,r,t,!0),e._onUpdate&&!t&&Ut(e,"onUpdate"),u&&e._repeat&&!t&&e.parent&&Ut(e,"onRepeat"),(r>=e._tDur||r<0)&&e.ratio===s&&(s&&Jr(e,1),!t&&!lt&&(Ut(e,s?"onComplete":"onReverseComplete",!0),e._prom&&e._prom()))}else e._zTime||(e._zTime=r)},lf=function(e,r,t){var i;if(t>r)for(i=e._first;i&&i._start<=t;){if(i.data==="isPause"&&i._start>r)return i;i=i._next}else for(i=e._last;i&&i._start>=t;){if(i.data==="isPause"&&i._start<r)return i;i=i._prev}},Zi=function(e,r,t,i){var n=e._repeat,s=qe(r)||0,o=e._tTime/e._tDur;return o&&!i&&(e._time*=s/e._dur),e._dur=s,e._tDur=n?n<0?1e10:qe(s*(n+1)+e._rDelay*n):s,o>0&&!i&&Xs(e,e._tTime=e._tDur*o),e.parent&&Ys(e),t||gi(e.parent,e),e},Ta=function(e){return e instanceof St?gi(e):Zi(e,e._dur)},uf={_start:0,endTime:zn,totalDuration:zn},rr=function l(e,r,t){var i=e.labels,n=e._recent||uf,s=e.duration()>=sr?n.endTime(!1):e._dur,o,u,f;return rt(r)&&(isNaN(r)||r in i)?(u=r.charAt(0),f=r.substr(-1)==="%",o=r.indexOf("="),u==="<"||u===">"?(o>=0&&(r=r.replace(/=/,"")),(u==="<"?n._start:n.endTime(n._repeat>=0))+(parseFloat(r.substr(1))||0)*(f?(o<0?n:t).totalDuration()/100:1)):o<0?(r in i||(i[r]=s),i[r]):(u=parseFloat(r.charAt(o-1)+r.substr(o+1)),f&&t&&(u=u/100*(mt(t)?t[0]:t).totalDuration()),o>1?l(e,r.substr(0,o-1),t)+u:s+u)):r==null?s:+r},Tn=function(e,r,t){var i=Nr(r[1]),n=(i?2:1)+(e<2?0:1),s=r[n],o,u;if(i&&(s.duration=r[1]),s.parent=t,e){for(o=s,u=t;u&&!("immediateRender"in o);)o=u.vars.defaults||{},u=Et(u.vars.inherit)&&u.parent;s.immediateRender=Et(o.immediateRender),e<2?s.runBackwards=1:s.startAt=r[n-1]}return new Ge(r[0],s,r[n+1])},ri=function(e,r){return e||e===0?r(e):r},qn=function(e,r,t){return t<e?e:t>r?r:t},_t=function(e,r){return!rt(e)||!(r=ju.exec(e))?"":r[1]},ff=function(e,r,t){return ri(t,function(i){return qn(e,r,i)})},xo=[].slice,Ol=function(e,r){return e&&Pr(e)&&"length"in e&&(!r&&!e.length||e.length-1 in e&&Pr(e[0]))&&!e.nodeType&&e!==vr},cf=function(e,r,t){return t===void 0&&(t=[]),e.forEach(function(i){var n;return rt(i)&&!r||Ol(i,1)?(n=t).push.apply(n,or(i)):t.push(i)})||t},or=function(e,r,t){return Ee&&!r&&Ee.selector?Ee.selector(e):rt(e)&&!t&&(_o||!Ji())?xo.call((r||Go).querySelectorAll(e),0):mt(e)?cf(e,t):Ol(e)?xo.call(e,0):e?[e]:[]},vo=function(e){return e=or(e)[0]||In("Invalid scope")||{},function(r){var t=e.current||e.nativeElement||e;return or(r,t.querySelectorAll?t:t===e?In("Invalid scope")||Go.createElement("div"):e)}},Rl=function(e){return e.sort(function(){return .5-Math.random()})},Al=function(e){if(Ye(e))return e;var r=Pr(e)?e:{each:e},t=mi(r.ease),i=r.from||0,n=parseFloat(r.base)||0,s={},o=i>0&&i<1,u=isNaN(i)||o,f=r.axis,c=i,h=i;return rt(i)?c=h={center:.5,edges:.5,end:1}[i]||0:!o&&u&&(c=i[0],h=i[1]),function(d,a,_){var p=(_||r).length,y=s[p],P,D,O,k,b,E,T,v,M;if(!y){if(M=r.grid==="auto"?0:(r.grid||[1,sr])[1],!M){for(T=-sr;T<(T=_[M++].getBoundingClientRect().left)&&M<p;);M<p&&M--}for(y=s[p]=[],P=u?Math.min(M,p)*c-.5:i%M,D=M===sr?0:u?p*h/M-.5:i/M|0,T=0,v=sr,E=0;E<p;E++)O=E%M-P,k=D-(E/M|0),y[E]=b=f?Math.abs(f==="y"?k:O):pl(O*O+k*k),b>T&&(T=b),b<v&&(v=b);i==="random"&&Rl(y),y.max=T-v,y.min=v,y.v=p=(parseFloat(r.amount)||parseFloat(r.each)*(M>p?p-1:f?f==="y"?p/M:M:Math.max(M,p/M))||0)*(i==="edges"?-1:1),y.b=p<0?n-p:n,y.u=_t(r.amount||r.each)||0,t=t&&p<0?Wl(t):t}return p=(y[d]-y.min)/y.max||0,qe(y.b+(t?t(p):p)*y.v)+y.u}},wo=function(e){var r=Math.pow(10,((e+"").split(".")[1]||"").length);return function(t){var i=qe(Math.round(parseFloat(t)/e)*e*r);return(i-i%1)/r+(Nr(t)?0:_t(t))}},Ll=function(e,r){var t=mt(e),i,n;return!t&&Pr(e)&&(i=t=e.radius||sr,e.values?(e=or(e.values),(n=!Nr(e[0]))&&(i*=i)):e=wo(e.increment)),ri(r,t?Ye(e)?function(s){return n=e(s),Math.abs(n-s)<=i?n:s}:function(s){for(var o=parseFloat(n?s.x:s),u=parseFloat(n?s.y:0),f=sr,c=0,h=e.length,d,a;h--;)n?(d=e[h].x-o,a=e[h].y-u,d=d*d+a*a):d=Math.abs(e[h]-o),d<f&&(f=d,c=h);return c=!i||f<=i?e[c]:s,n||c===s||Nr(s)?c:c+_t(s)}:wo(e))},Fl=function(e,r,t,i){return ri(mt(e)?!r:t===!0?!!(t=0):!i,function(){return mt(e)?e[~~(Math.random()*e.length)]:(t=t||1e-5)&&(i=t<1?Math.pow(10,(t+"").length-2):1)&&Math.floor(Math.round((e-t/2+Math.random()*(r-e+t*.99))/t)*t*i)/i})},hf=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(i){return r.reduce(function(n,s){return s(n)},i)}},df=function(e,r){return function(t){return e(parseFloat(t))+(r||_t(t))}},pf=function(e,r,t){return Il(e,r,0,1,t)},Nl=function(e,r,t){return ri(t,function(i){return e[~~r(i)]})},_f=function l(e,r,t){var i=r-e;return mt(e)?Nl(e,l(0,e.length),r):ri(t,function(n){return(i+(n-e)%i)%i+e})},gf=function l(e,r,t){var i=r-e,n=i*2;return mt(e)?Nl(e,l(0,e.length-1),r):ri(t,function(s){return s=(n+(s-e)%n)%n||0,e+(s>i?n-s:s)})},Yn=function(e){for(var r=0,t="",i,n,s,o;~(i=e.indexOf("random(",r));)s=e.indexOf(")",i),o=e.charAt(i+7)==="[",n=e.substr(i+7,s-i-7).match(o?yl:po),t+=e.substr(r,i-r)+Fl(o?n:+n[0],o?0:+n[1],+n[2]||1e-5),r=s+1;return t+e.substr(r,e.length-r)},Il=function(e,r,t,i,n){var s=r-e,o=i-t;return ri(n,function(u){return t+((u-e)/s*o||0)})},mf=function l(e,r,t,i){var n=isNaN(e+r)?0:function(a){return(1-a)*e+a*r};if(!n){var s=rt(e),o={},u,f,c,h,d;if(t===!0&&(i=1)&&(t=null),s)e={p:e},r={p:r};else if(mt(e)&&!mt(r)){for(c=[],h=e.length,d=h-2,f=1;f<h;f++)c.push(l(e[f-1],e[f]));h--,n=function(_){_*=h;var p=Math.min(d,~~_);return c[p](_-p)},t=r}else i||(e=Qi(mt(e)?[]:{},e));if(!c){for(u in r)Jo.call(o,e,u,"get",r[u]);n=function(_){return ia(_,o)||(s?e.p:e)}}}return ri(t,n)},Sa=function(e,r,t){var i=e.labels,n=sr,s,o,u;for(s in i)o=i[s]-r,o<0==!!t&&o&&n>(o=Math.abs(o))&&(u=s,n=o);return u},Ut=function(e,r,t){var i=e.vars,n=i[r],s=Ee,o=e._ctx,u,f,c;if(n)return u=i[r+"Params"],f=i.callbackScope||e,t&&Qr.length&&Cs(),o&&(Ee=o),c=u?n.apply(f,u):n.call(f),Ee=s,c},cn=function(e){return Jr(e),e.scrollTrigger&&e.scrollTrigger.kill(!!lt),e.progress()<1&&Ut(e,"onInterrupt"),e},zi,zl=[],Yl=function(e){if(e)if(e=!e.name&&e.default||e,Uo()||e.headless){var r=e.name,t=Ye(e),i=r&&!t&&e.init?function(){this._props=[]}:e,n={init:zn,render:ia,add:Jo,kill:Af,modifier:Rf,rawVars:0},s={targetTest:0,get:0,getSetter:ra,aliases:{},register:0};if(Ji(),e!==i){if(Bt[r])return;Kt(i,Kt(Ps(e,n),s)),Qi(i.prototype,Qi(n,Ps(e,s))),Bt[i.prop=r]=i,e.targetTest&&(ds.push(i),Ko[r]=1),r=(r==="css"?"CSS":r.charAt(0).toUpperCase()+r.substr(1))+"Plugin"}wl(r,i),e.register&&e.register(Lt,i,Rt)}else zl.push(e)},be=255,hn={aqua:[0,be,be],lime:[0,be,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,be],navy:[0,0,128],white:[be,be,be],olive:[128,128,0],yellow:[be,be,0],orange:[be,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[be,0,0],pink:[be,192,203],cyan:[0,be,be],transparent:[be,be,be,0]},Us=function(e,r,t){return e+=e<0?1:e>1?-1:0,(e*6<1?r+(t-r)*e*6:e<.5?t:e*3<2?r+(t-r)*(2/3-e)*6:r)*be+.5|0},Xl=function(e,r,t){var i=e?Nr(e)?[e>>16,e>>8&be,e&be]:0:hn.black,n,s,o,u,f,c,h,d,a,_;if(!i){if(e.substr(-1)===","&&(e=e.substr(0,e.length-1)),hn[e])i=hn[e];else if(e.charAt(0)==="#"){if(e.length<6&&(n=e.charAt(1),s=e.charAt(2),o=e.charAt(3),e="#"+n+n+s+s+o+o+(e.length===5?e.charAt(4)+e.charAt(4):"")),e.length===9)return i=parseInt(e.substr(1,6),16),[i>>16,i>>8&be,i&be,parseInt(e.substr(7),16)/255];e=parseInt(e.substr(1),16),i=[e>>16,e>>8&be,e&be]}else if(e.substr(0,3)==="hsl"){if(i=_=e.match(po),!r)u=+i[0]%360/360,f=+i[1]/100,c=+i[2]/100,s=c<=.5?c*(f+1):c+f-c*f,n=c*2-s,i.length>3&&(i[3]*=1),i[0]=Us(u+1/3,n,s),i[1]=Us(u,n,s),i[2]=Us(u-1/3,n,s);else if(~e.indexOf("="))return i=e.match(gl),t&&i.length<4&&(i[3]=1),i}else i=e.match(po)||hn.transparent;i=i.map(Number)}return r&&!_&&(n=i[0]/be,s=i[1]/be,o=i[2]/be,h=Math.max(n,s,o),d=Math.min(n,s,o),c=(h+d)/2,h===d?u=f=0:(a=h-d,f=c>.5?a/(2-h-d):a/(h+d),u=h===n?(s-o)/a+(s<o?6:0):h===s?(o-n)/a+2:(n-s)/a+4,u*=60),i[0]=~~(u+.5),i[1]=~~(f*100+.5),i[2]=~~(c*100+.5)),t&&i.length<4&&(i[3]=1),i},Bl=function(e){var r=[],t=[],i=-1;return e.split(jr).forEach(function(n){var s=n.match(Ii)||[];r.push.apply(r,s),t.push(i+=s.length+1)}),r.c=t,r},Ca=function(e,r,t){var i="",n=(e+i).match(jr),s=r?"hsla(":"rgba(",o=0,u,f,c,h;if(!n)return e;if(n=n.map(function(d){return(d=Xl(d,r,1))&&s+(r?d[0]+","+d[1]+"%,"+d[2]+"%,"+d[3]:d.join(","))+")"}),t&&(c=Bl(e),u=t.c,u.join(i)!==c.c.join(i)))for(f=e.replace(jr,"1").split(Ii),h=f.length-1;o<h;o++)i+=f[o]+(~u.indexOf(o)?n.shift()||s+"0,0,0,0)":(c.length?c:n.length?n:t).shift());if(!f)for(f=e.split(jr),h=f.length-1;o<h;o++)i+=f[o]+n[o];return i+f[h]},jr=function(){var l="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",e;for(e in hn)l+="|"+e+"\\b";return new RegExp(l+")","gi")}(),yf=/hsl[a]?\(/,$l=function(e){var r=e.join(" "),t;if(jr.lastIndex=0,jr.test(r))return t=yf.test(r),e[1]=Ca(e[1],t),e[0]=Ca(e[0],t,Bl(e[1])),!0},Xn,Wt=function(){var l=Date.now,e=500,r=33,t=l(),i=t,n=1e3/240,s=n,o=[],u,f,c,h,d,a,_=function p(y){var P=l()-i,D=y===!0,O,k,b,E;if((P>e||P<0)&&(t+=P-r),i+=P,b=i-t,O=b-s,(O>0||D)&&(E=++h.frame,d=b-h.time*1e3,h.time=b=b/1e3,s+=O+(O>=n?4:n-O),k=1),D||(u=f(p)),k)for(a=0;a<o.length;a++)o[a](b,d,E,y)};return h={time:0,frame:0,tick:function(){_(!0)},deltaRatio:function(y){return d/(1e3/(y||60))},wake:function(){xl&&(!_o&&Uo()&&(vr=_o=window,Go=vr.document||{},qt.gsap=Lt,(vr.gsapVersions||(vr.gsapVersions=[])).push(Lt.version),vl(Ss||vr.GreenSockGlobals||!vr.gsap&&vr||{}),zl.forEach(Yl)),c=typeof requestAnimationFrame<"u"&&requestAnimationFrame,u&&h.sleep(),f=c||function(y){return setTimeout(y,s-h.time*1e3+1|0)},Xn=1,_(2))},sleep:function(){(c?cancelAnimationFrame:clearTimeout)(u),Xn=0,f=zn},lagSmoothing:function(y,P){e=y||1/0,r=Math.min(P||33,e)},fps:function(y){n=1e3/(y||240),s=h.time*1e3+n},add:function(y,P,D){var O=P?function(k,b,E,T){y(k,b,E,T),h.remove(O)}:y;return h.remove(y),o[D?"unshift":"push"](O),Ji(),O},remove:function(y,P){~(P=o.indexOf(y))&&o.splice(P,1)&&a>=P&&a--},_listeners:o},h}(),Ji=function(){return!Xn&&Wt.wake()},he={},xf=/^[\d.\-M][\d.\-,\s]/,vf=/["']/g,wf=function(e){for(var r={},t=e.substr(1,e.length-3).split(":"),i=t[0],n=1,s=t.length,o,u,f;n<s;n++)u=t[n],o=n!==s-1?u.lastIndexOf(","):u.length,f=u.substr(0,o),r[i]=isNaN(f)?f.replace(vf,"").trim():+f,i=u.substr(o+1).trim();return r},bf=function(e){var r=e.indexOf("(")+1,t=e.indexOf(")"),i=e.indexOf("(",r);return e.substring(r,~i&&i<t?e.indexOf(")",t+1):t)},Tf=function(e){var r=(e+"").split("("),t=he[r[0]];return t&&r.length>1&&t.config?t.config.apply(null,~e.indexOf("{")?[wf(r[1])]:bf(e).split(",").map(Cl)):he._CE&&xf.test(e)?he._CE("",e):t},Wl=function(e){return function(r){return 1-e(1-r)}},Hl=function l(e,r){for(var t=e._first,i;t;)t instanceof St?l(t,r):t.vars.yoyoEase&&(!t._yoyo||!t._repeat)&&t._yoyo!==r&&(t.timeline?l(t.timeline,r):(i=t._ease,t._ease=t._yEase,t._yEase=i,t._yoyo=r)),t=t._next},mi=function(e,r){return e&&(Ye(e)?e:he[e]||Tf(e))||r},Di=function(e,r,t,i){t===void 0&&(t=function(u){return 1-r(1-u)}),i===void 0&&(i=function(u){return u<.5?r(u*2)/2:1-r((1-u)*2)/2});var n={easeIn:r,easeOut:t,easeInOut:i},s;return Ot(e,function(o){he[o]=qt[o]=n,he[s=o.toLowerCase()]=t;for(var u in n)he[s+(u==="easeIn"?".in":u==="easeOut"?".out":".inOut")]=he[o+"."+u]=n[u]}),n},Vl=function(e){return function(r){return r<.5?(1-e(1-r*2))/2:.5+e((r-.5)*2)/2}},Gs=function l(e,r,t){var i=r>=1?r:1,n=(t||(e?.3:.45))/(r<1?r:1),s=n/ho*(Math.asin(1/i)||0),o=function(c){return c===1?1:i*Math.pow(2,-10*c)*Qu((c-s)*n)+1},u=e==="out"?o:e==="in"?function(f){return 1-o(1-f)}:Vl(o);return n=ho/n,u.config=function(f,c){return l(e,f,c)},u},qs=function l(e,r){r===void 0&&(r=1.70158);var t=function(s){return s?--s*s*((r+1)*s+r)+1:0},i=e==="out"?t:e==="in"?function(n){return 1-t(1-n)}:Vl(t);return i.config=function(n){return l(e,n)},i};Ot("Linear,Quad,Cubic,Quart,Quint,Strong",function(l,e){var r=e<5?e+1:e;Di(l+",Power"+(r-1),e?function(t){return Math.pow(t,r)}:function(t){return t},function(t){return 1-Math.pow(1-t,r)},function(t){return t<.5?Math.pow(t*2,r)/2:1-Math.pow((1-t)*2,r)/2})});he.Linear.easeNone=he.none=he.Linear.easeIn;Di("Elastic",Gs("in"),Gs("out"),Gs());(function(l,e){var r=1/e,t=2*r,i=2.5*r,n=function(o){return o<r?l*o*o:o<t?l*Math.pow(o-1.5/e,2)+.75:o<i?l*(o-=2.25/e)*o+.9375:l*Math.pow(o-2.625/e,2)+.984375};Di("Bounce",function(s){return 1-n(1-s)},n)})(7.5625,2.75);Di("Expo",function(l){return Math.pow(2,10*(l-1))*l+l*l*l*l*l*l*(1-l)});Di("Circ",function(l){return-(pl(1-l*l)-1)});Di("Sine",function(l){return l===1?1:-Ku(l*Gu)+1});Di("Back",qs("in"),qs("out"),qs());he.SteppedEase=he.steps=qt.SteppedEase={config:function(e,r){e===void 0&&(e=1);var t=1/e,i=e+(r?0:1),n=r?1:0,s=1-Se;return function(o){return((i*qn(0,s,o)|0)+n)*t}}};Ki.ease=he["quad.out"];Ot("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(l){return Qo+=l+","+l+"Params,"});var Ul=function(e,r){this.id=qu++,e._gsap=this,this.target=e,this.harness=r,this.get=r?r.get:Tl,this.set=r?r.getSetter:ra},Bn=function(){function l(r){this.vars=r,this._delay=+r.delay||0,(this._repeat=r.repeat===1/0?-2:r.repeat||0)&&(this._rDelay=r.repeatDelay||0,this._yoyo=!!r.yoyo||!!r.yoyoEase),this._ts=1,Zi(this,+r.duration,1,1),this.data=r.data,Ee&&(this._ctx=Ee,Ee.data.push(this)),Xn||Wt.wake()}var e=l.prototype;return e.delay=function(t){return t||t===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+t-this._delay),this._delay=t,this):this._delay},e.duration=function(t){return arguments.length?this.totalDuration(this._repeat>0?t+(t+this._rDelay)*this._repeat:t):this.totalDuration()&&this._dur},e.totalDuration=function(t){return arguments.length?(this._dirty=0,Zi(this,this._repeat<0?t:(t-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(t,i){if(Ji(),!arguments.length)return this._tTime;var n=this._dp;if(n&&n.smoothChildTiming&&this._ts){for(Xs(this,t),!n._dp||n.parent||Ml(n,this);n&&n.parent;)n.parent._time!==n._start+(n._ts>=0?n._tTime/n._ts:(n.totalDuration()-n._tTime)/-n._ts)&&n.totalTime(n._tTime,!0),n=n.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&t<this._tDur||this._ts<0&&t>0||!this._tDur&&!t)&&br(this._dp,this,this._start-this._delay)}return(this._tTime!==t||!this._dur&&!i||this._initted&&Math.abs(this._zTime)===Se||!t&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=t),Sl(this,t,i)),this},e.time=function(t,i){return arguments.length?this.totalTime(Math.min(this.totalDuration(),t+ba(this))%(this._dur+this._rDelay)||(t?this._dur:0),i):this._time},e.totalProgress=function(t,i){return arguments.length?this.totalTime(this.totalDuration()*t,i):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},e.progress=function(t,i){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-t:t)+ba(this),i):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(t,i){var n=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(t-1)*n,i):this._repeat?ji(this._tTime,n)+1:1},e.timeScale=function(t,i){if(!arguments.length)return this._rts===-Se?0:this._rts;if(this._rts===t)return this;var n=this.parent&&this._ts?ks(this.parent._time,this):this._tTime;return this._rts=+t||0,this._ts=this._ps||t===-Se?0:this._rts,this.totalTime(qn(-Math.abs(this._delay),this.totalDuration(),n),i!==!1),Ys(this),nf(this)},e.paused=function(t){return arguments.length?(this._ps!==t&&(this._ps=t,t?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Ji(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==Se&&(this._tTime-=Se)))),this):this._ps},e.startTime=function(t){if(arguments.length){this._start=t;var i=this.parent||this._dp;return i&&(i._sort||!this.parent)&&br(i,this,t-this._delay),this}return this._start},e.endTime=function(t){return this._start+(Et(t)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(t){var i=this.parent||this._dp;return i?t&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?ks(i.rawTime(t),this):this._tTime:this._tTime},e.revert=function(t){t===void 0&&(t=Ju);var i=lt;return lt=t,Zo(this)&&(this.timeline&&this.timeline.revert(t),this.totalTime(-.01,t.suppressEvents)),this.data!=="nested"&&t.kill!==!1&&this.kill(),lt=i,this},e.globalTime=function(t){for(var i=this,n=arguments.length?t:i.rawTime();i;)n=i._start+n/(Math.abs(i._ts)||1),i=i._dp;return!this.parent&&this._sat?this._sat.globalTime(t):n},e.repeat=function(t){return arguments.length?(this._repeat=t===1/0?-2:t,Ta(this)):this._repeat===-2?1/0:this._repeat},e.repeatDelay=function(t){if(arguments.length){var i=this._time;return this._rDelay=t,Ta(this),i?this.time(i):this}return this._rDelay},e.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},e.seek=function(t,i){return this.totalTime(rr(this,t),Et(i))},e.restart=function(t,i){return this.play().totalTime(t?-this._delay:0,Et(i)),this._dur||(this._zTime=-Se),this},e.play=function(t,i){return t!=null&&this.seek(t,i),this.reversed(!1).paused(!1)},e.reverse=function(t,i){return t!=null&&this.seek(t||this.totalDuration(),i),this.reversed(!0).paused(!1)},e.pause=function(t,i){return t!=null&&this.seek(t,i),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(t){return arguments.length?(!!t!==this.reversed()&&this.timeScale(-this._rts||(t?-Se:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-Se,this},e.isActive=function(){var t=this.parent||this._dp,i=this._start,n;return!!(!t||this._ts&&this._initted&&t.isActive()&&(n=t.rawTime(!0))>=i&&n<this.endTime(!0)-Se)},e.eventCallback=function(t,i,n){var s=this.vars;return arguments.length>1?(i?(s[t]=i,n&&(s[t+"Params"]=n),t==="onUpdate"&&(this._onUpdate=i)):delete s[t],this):s[t]},e.then=function(t){var i=this;return new Promise(function(n){var s=Ye(t)?t:Pl,o=function(){var f=i.then;i.then=null,Ye(s)&&(s=s(i))&&(s.then||s===i)&&(i.then=f),n(s),i.then=f};i._initted&&i.totalProgress()===1&&i._ts>=0||!i._tTime&&i._ts<0?o():i._prom=o})},e.kill=function(){cn(this)},l}();Kt(Bn.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-Se,_prom:0,_ps:!1,_rts:1});var St=function(l){dl(e,l);function e(t,i){var n;return t===void 0&&(t={}),n=l.call(this,t)||this,n.labels={},n.smoothChildTiming=!!t.smoothChildTiming,n.autoRemoveChildren=!!t.autoRemoveChildren,n._sort=Et(t.sortChildren),Ae&&br(t.parent||Ae,Mr(n),i),t.reversed&&n.reverse(),t.paused&&n.paused(!0),t.scrollTrigger&&Dl(Mr(n),t.scrollTrigger),n}var r=e.prototype;return r.to=function(i,n,s){return Tn(0,arguments,this),this},r.from=function(i,n,s){return Tn(1,arguments,this),this},r.fromTo=function(i,n,s,o){return Tn(2,arguments,this),this},r.set=function(i,n,s){return n.duration=0,n.parent=this,bn(n).repeatDelay||(n.repeat=0),n.immediateRender=!!n.immediateRender,new Ge(i,n,rr(this,s),1),this},r.call=function(i,n,s){return br(this,Ge.delayedCall(0,i,n),s)},r.staggerTo=function(i,n,s,o,u,f,c){return s.duration=n,s.stagger=s.stagger||o,s.onComplete=f,s.onCompleteParams=c,s.parent=this,new Ge(i,s,rr(this,u)),this},r.staggerFrom=function(i,n,s,o,u,f,c){return s.runBackwards=1,bn(s).immediateRender=Et(s.immediateRender),this.staggerTo(i,n,s,o,u,f,c)},r.staggerFromTo=function(i,n,s,o,u,f,c,h){return o.startAt=s,bn(o).immediateRender=Et(o.immediateRender),this.staggerTo(i,n,o,u,f,c,h)},r.render=function(i,n,s){var o=this._time,u=this._dirty?this.totalDuration():this._tDur,f=this._dur,c=i<=0?0:qe(i),h=this._zTime<0!=i<0&&(this._initted||!f),d,a,_,p,y,P,D,O,k,b,E,T;if(this!==Ae&&c>u&&i>=0&&(c=u),c!==this._tTime||s||h){if(o!==this._time&&f&&(c+=this._time-o,i+=this._time-o),d=c,k=this._start,O=this._ts,P=!O,h&&(f||(o=this._zTime),(i||!n)&&(this._zTime=i)),this._repeat){if(E=this._yoyo,y=f+this._rDelay,this._repeat<-1&&i<0)return this.totalTime(y*100+i,n,s);if(d=qe(c%y),c===u?(p=this._repeat,d=f):(b=qe(c/y),p=~~b,p&&p===b&&(d=f,p--),d>f&&(d=f)),b=ji(this._tTime,y),!o&&this._tTime&&b!==p&&this._tTime-b*y-this._dur<=0&&(b=p),E&&p&1&&(d=f-d,T=1),p!==b&&!this._lock){var v=E&&b&1,M=v===(E&&p&1);if(p<b&&(v=!v),o=v?0:c%f?f:c,this._lock=1,this.render(o||(T?0:qe(p*y)),n,!f)._lock=0,this._tTime=c,!n&&this.parent&&Ut(this,"onRepeat"),this.vars.repeatRefresh&&!T&&(this.invalidate()._lock=1),o&&o!==this._time||P!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(f=this._dur,u=this._tDur,M&&(this._lock=2,o=v?f:-1e-4,this.render(o,!0),this.vars.repeatRefresh&&!T&&this.invalidate()),this._lock=0,!this._ts&&!P)return this;Hl(this,T)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(D=lf(this,qe(o),qe(d)),D&&(c-=d-(d=D._start))),this._tTime=c,this._time=d,this._act=!O,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=i,o=0),!o&&c&&!n&&!b&&(Ut(this,"onStart"),this._tTime!==c))return this;if(d>=o&&i>=0)for(a=this._first;a;){if(_=a._next,(a._act||d>=a._start)&&a._ts&&D!==a){if(a.parent!==this)return this.render(i,n,s);if(a.render(a._ts>0?(d-a._start)*a._ts:(a._dirty?a.totalDuration():a._tDur)+(d-a._start)*a._ts,n,s),d!==this._time||!this._ts&&!P){D=0,_&&(c+=this._zTime=-Se);break}}a=_}else{a=this._last;for(var R=i<0?i:d;a;){if(_=a._prev,(a._act||R<=a._end)&&a._ts&&D!==a){if(a.parent!==this)return this.render(i,n,s);if(a.render(a._ts>0?(R-a._start)*a._ts:(a._dirty?a.totalDuration():a._tDur)+(R-a._start)*a._ts,n,s||lt&&Zo(a)),d!==this._time||!this._ts&&!P){D=0,_&&(c+=this._zTime=R?-Se:Se);break}}a=_}}if(D&&!n&&(this.pause(),D.render(d>=o?0:-Se)._zTime=d>=o?1:-1,this._ts))return this._start=k,Ys(this),this.render(i,n,s);this._onUpdate&&!n&&Ut(this,"onUpdate",!0),(c===u&&this._tTime>=this.totalDuration()||!c&&o)&&(k===this._start||Math.abs(O)!==Math.abs(this._ts))&&(this._lock||((i||!f)&&(c===u&&this._ts>0||!c&&this._ts<0)&&Jr(this,1),!n&&!(i<0&&!o)&&(c||o||!u)&&(Ut(this,c===u&&i>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(c<u&&this.timeScale()>0)&&this._prom())))}return this},r.add=function(i,n){var s=this;if(Nr(n)||(n=rr(this,n,i)),!(i instanceof Bn)){if(mt(i))return i.forEach(function(o){return s.add(o,n)}),this;if(rt(i))return this.addLabel(i,n);if(Ye(i))i=Ge.delayedCall(0,i);else return this}return this!==i?br(this,i,n):this},r.getChildren=function(i,n,s,o){i===void 0&&(i=!0),n===void 0&&(n=!0),s===void 0&&(s=!0),o===void 0&&(o=-sr);for(var u=[],f=this._first;f;)f._start>=o&&(f instanceof Ge?n&&u.push(f):(s&&u.push(f),i&&u.push.apply(u,f.getChildren(!0,n,s)))),f=f._next;return u},r.getById=function(i){for(var n=this.getChildren(1,1,1),s=n.length;s--;)if(n[s].vars.id===i)return n[s]},r.remove=function(i){return rt(i)?this.removeLabel(i):Ye(i)?this.killTweensOf(i):(i.parent===this&&zs(this,i),i===this._recent&&(this._recent=this._last),gi(this))},r.totalTime=function(i,n){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=qe(Wt.time-(this._ts>0?i/this._ts:(this.totalDuration()-i)/-this._ts))),l.prototype.totalTime.call(this,i,n),this._forcing=0,this):this._tTime},r.addLabel=function(i,n){return this.labels[i]=rr(this,n),this},r.removeLabel=function(i){return delete this.labels[i],this},r.addPause=function(i,n,s){var o=Ge.delayedCall(0,n||zn,s);return o.data="isPause",this._hasPause=1,br(this,o,rr(this,i))},r.removePause=function(i){var n=this._first;for(i=rr(this,i);n;)n._start===i&&n.data==="isPause"&&Jr(n),n=n._next},r.killTweensOf=function(i,n,s){for(var o=this.getTweensOf(i,s),u=o.length;u--;)Hr!==o[u]&&o[u].kill(i,n);return this},r.getTweensOf=function(i,n){for(var s=[],o=or(i),u=this._first,f=Nr(n),c;u;)u instanceof Ge?ef(u._targets,o)&&(f?(!Hr||u._initted&&u._ts)&&u.globalTime(0)<=n&&u.globalTime(u.totalDuration())>n:!n||u.isActive())&&s.push(u):(c=u.getTweensOf(o,n)).length&&s.push.apply(s,c),u=u._next;return s},r.tweenTo=function(i,n){n=n||{};var s=this,o=rr(s,i),u=n,f=u.startAt,c=u.onStart,h=u.onStartParams,d=u.immediateRender,a,_=Ge.to(s,Kt({ease:n.ease||"none",lazy:!1,immediateRender:!1,time:o,overwrite:"auto",duration:n.duration||Math.abs((o-(f&&"time"in f?f.time:s._time))/s.timeScale())||Se,onStart:function(){if(s.pause(),!a){var y=n.duration||Math.abs((o-(f&&"time"in f?f.time:s._time))/s.timeScale());_._dur!==y&&Zi(_,y,0,1).render(_._time,!0,!0),a=1}c&&c.apply(_,h||[])}},n));return d?_.render(0):_},r.tweenFromTo=function(i,n,s){return this.tweenTo(n,Kt({startAt:{time:rr(this,i)}},s))},r.recent=function(){return this._recent},r.nextLabel=function(i){return i===void 0&&(i=this._time),Sa(this,rr(this,i))},r.previousLabel=function(i){return i===void 0&&(i=this._time),Sa(this,rr(this,i),1)},r.currentLabel=function(i){return arguments.length?this.seek(i,!0):this.previousLabel(this._time+Se)},r.shiftChildren=function(i,n,s){s===void 0&&(s=0);for(var o=this._first,u=this.labels,f;o;)o._start>=s&&(o._start+=i,o._end+=i),o=o._next;if(n)for(f in u)u[f]>=s&&(u[f]+=i);return gi(this)},r.invalidate=function(i){var n=this._first;for(this._lock=0;n;)n.invalidate(i),n=n._next;return l.prototype.invalidate.call(this,i)},r.clear=function(i){i===void 0&&(i=!0);for(var n=this._first,s;n;)s=n._next,this.remove(n),n=s;return this._dp&&(this._time=this._tTime=this._pTime=0),i&&(this.labels={}),gi(this)},r.totalDuration=function(i){var n=0,s=this,o=s._last,u=sr,f,c,h;if(arguments.length)return s.timeScale((s._repeat<0?s.duration():s.totalDuration())/(s.reversed()?-i:i));if(s._dirty){for(h=s.parent;o;)f=o._prev,o._dirty&&o.totalDuration(),c=o._start,c>u&&s._sort&&o._ts&&!s._lock?(s._lock=1,br(s,o,c-o._delay,1)._lock=0):u=c,c<0&&o._ts&&(n-=c,(!h&&!s._dp||h&&h.smoothChildTiming)&&(s._start+=c/s._ts,s._time-=c,s._tTime-=c),s.shiftChildren(-c,!1,-1/0),u=0),o._end>n&&o._ts&&(n=o._end),o=f;Zi(s,s===Ae&&s._time>n?s._time:n,1,1),s._dirty=0}return s._tDur},e.updateRoot=function(i){if(Ae._ts&&(Sl(Ae,ks(i,Ae)),bl=Wt.frame),Wt.frame>=va){va+=Gt.autoSleep||120;var n=Ae._first;if((!n||!n._ts)&&Gt.autoSleep&&Wt._listeners.length<2){for(;n&&!n._ts;)n=n._next;n||Wt.sleep()}}},e}(Bn);Kt(St.prototype,{_lock:0,_hasPause:0,_forcing:0});var Sf=function(e,r,t,i,n,s,o){var u=new Rt(this._pt,e,r,0,1,Zl,null,n),f=0,c=0,h,d,a,_,p,y,P,D;for(u.b=t,u.e=i,t+="",i+="",(P=~i.indexOf("random("))&&(i=Yn(i)),s&&(D=[t,i],s(D,e,r),t=D[0],i=D[1]),d=t.match(Hs)||[];h=Hs.exec(i);)_=h[0],p=i.substring(f,h.index),a?a=(a+1)%5:p.substr(-5)==="rgba("&&(a=1),_!==d[c++]&&(y=parseFloat(d[c-1])||0,u._pt={_next:u._pt,p:p||c===1?p:",",s:y,c:_.charAt(1)==="="?Xi(y,_)-y:parseFloat(_)-y,m:a&&a<4?Math.round:0},f=Hs.lastIndex);return u.c=f<i.length?i.substring(f,i.length):"",u.fp=o,(ml.test(i)||P)&&(u.e=0),this._pt=u,u},Jo=function(e,r,t,i,n,s,o,u,f,c){Ye(i)&&(i=i(n||0,e,s));var h=e[r],d=t!=="get"?t:Ye(h)?f?e[r.indexOf("set")||!Ye(e["get"+r.substr(3)])?r:"get"+r.substr(3)](f):e[r]():h,a=Ye(h)?f?Df:Ql:ta,_;if(rt(i)&&(~i.indexOf("random(")&&(i=Yn(i)),i.charAt(1)==="="&&(_=Xi(d,i)+(_t(d)||0),(_||_===0)&&(i=_))),!c||d!==i||bo)return!isNaN(d*i)&&i!==""?(_=new Rt(this._pt,e,r,+d||0,i-(d||0),typeof h=="boolean"?Of:jl,0,a),f&&(_.fp=f),o&&_.modifier(o,this,e),this._pt=_):(!h&&!(r in e)&&qo(r,i),Sf.call(this,e,r,d,i,a,u||Gt.stringFilter,f))},Cf=function(e,r,t,i,n){if(Ye(e)&&(e=Sn(e,n,r,t,i)),!Pr(e)||e.style&&e.nodeType||mt(e)||_l(e))return rt(e)?Sn(e,n,r,t,i):e;var s={},o;for(o in e)s[o]=Sn(e[o],n,r,t,i);return s},Gl=function(e,r,t,i,n,s){var o,u,f,c;if(Bt[e]&&(o=new Bt[e]).init(n,o.rawVars?r[e]:Cf(r[e],i,n,s,t),t,i,s)!==!1&&(t._pt=u=new Rt(t._pt,n,e,0,1,o.render,o,0,o.priority),t!==zi))for(f=t._ptLookup[t._targets.indexOf(n)],c=o._props.length;c--;)f[o._props[c]]=u;return o},Hr,bo,ea=function l(e,r,t){var i=e.vars,n=i.ease,s=i.startAt,o=i.immediateRender,u=i.lazy,f=i.onUpdate,c=i.runBackwards,h=i.yoyoEase,d=i.keyframes,a=i.autoRevert,_=e._dur,p=e._startAt,y=e._targets,P=e.parent,D=P&&P.data==="nested"?P.vars.targets:y,O=e._overwrite==="auto"&&!Ho,k=e.timeline,b,E,T,v,M,R,I,N,Y,G,K,V,B;if(k&&(!d||!n)&&(n="none"),e._ease=mi(n,Ki.ease),e._yEase=h?Wl(mi(h===!0?n:h,Ki.ease)):0,h&&e._yoyo&&!e._repeat&&(h=e._yEase,e._yEase=e._ease,e._ease=h),e._from=!k&&!!i.runBackwards,!k||d&&!i.stagger){if(N=y[0]?_i(y[0]).harness:0,V=N&&i[N.prop],b=Ps(i,Ko),p&&(p._zTime<0&&p.progress(1),r<0&&c&&o&&!a?p.render(-1,!0):p.revert(c&&_?hs:Zu),p._lazy=0),s){if(Jr(e._startAt=Ge.set(y,Kt({data:"isStart",overwrite:!1,parent:P,immediateRender:!0,lazy:!p&&Et(u),startAt:null,delay:0,onUpdate:f&&function(){return Ut(e,"onUpdate")},stagger:0},s))),e._startAt._dp=0,e._startAt._sat=e,r<0&&(lt||!o&&!a)&&e._startAt.revert(hs),o&&_&&r<=0&&t<=0){r&&(e._zTime=r);return}}else if(c&&_&&!p){if(r&&(o=!1),T=Kt({overwrite:!1,data:"isFromStart",lazy:o&&!p&&Et(u),immediateRender:o,stagger:0,parent:P},b),V&&(T[N.prop]=V),Jr(e._startAt=Ge.set(y,T)),e._startAt._dp=0,e._startAt._sat=e,r<0&&(lt?e._startAt.revert(hs):e._startAt.render(-1,!0)),e._zTime=r,!o)l(e._startAt,Se,Se);else if(!r)return}for(e._pt=e._ptCache=0,u=_&&Et(u)||u&&!_,E=0;E<y.length;E++){if(M=y[E],I=M._gsap||jo(y)[E]._gsap,e._ptLookup[E]=G={},go[I.id]&&Qr.length&&Cs(),K=D===y?E:D.indexOf(M),N&&(Y=new N).init(M,V||b,e,K,D)!==!1&&(e._pt=v=new Rt(e._pt,M,Y.name,0,1,Y.render,Y,0,Y.priority),Y._props.forEach(function(te){G[te]=v}),Y.priority&&(R=1)),!N||V)for(T in b)Bt[T]&&(Y=Gl(T,b,e,K,M,D))?Y.priority&&(R=1):G[T]=v=Jo.call(e,M,T,"get",b[T],K,D,0,i.stringFilter);e._op&&e._op[E]&&e.kill(M,e._op[E]),O&&e._pt&&(Hr=e,Ae.killTweensOf(M,G,e.globalTime(r)),B=!e.parent,Hr=0),e._pt&&u&&(go[I.id]=1)}R&&Jl(e),e._onInit&&e._onInit(e)}e._onUpdate=f,e._initted=(!e._op||e._pt)&&!B,d&&r<=0&&k.render(sr,!0,!0)},Pf=function(e,r,t,i,n,s,o,u){var f=(e._pt&&e._ptCache||(e._ptCache={}))[r],c,h,d,a;if(!f)for(f=e._ptCache[r]=[],d=e._ptLookup,a=e._targets.length;a--;){if(c=d[a][r],c&&c.d&&c.d._pt)for(c=c.d._pt;c&&c.p!==r&&c.fp!==r;)c=c._next;if(!c)return bo=1,e.vars[r]="+=0",ea(e,o),bo=0,u?In(r+" not eligible for reset"):1;f.push(c)}for(a=f.length;a--;)h=f[a],c=h._pt||h,c.s=(i||i===0)&&!n?i:c.s+(i||0)+s*c.c,c.c=t-c.s,h.e&&(h.e=$e(t)+_t(h.e)),h.b&&(h.b=c.s+_t(h.b))},kf=function(e,r){var t=e[0]?_i(e[0]).harness:0,i=t&&t.aliases,n,s,o,u;if(!i)return r;n=Qi({},r);for(s in i)if(s in n)for(u=i[s].split(","),o=u.length;o--;)n[u[o]]=n[s];return n},Mf=function(e,r,t,i){var n=r.ease||i||"power1.inOut",s,o;if(mt(r))o=t[e]||(t[e]=[]),r.forEach(function(u,f){return o.push({t:f/(r.length-1)*100,v:u,e:n})});else for(s in r)o=t[s]||(t[s]=[]),s==="ease"||o.push({t:parseFloat(e),v:r[s],e:n})},Sn=function(e,r,t,i,n){return Ye(e)?e.call(r,t,i,n):rt(e)&&~e.indexOf("random(")?Yn(e):e},ql=Qo+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Kl={};Ot(ql+",id,stagger,delay,duration,paused,scrollTrigger",function(l){return Kl[l]=1});var Ge=function(l){dl(e,l);function e(t,i,n,s){var o;typeof i=="number"&&(n.duration=i,i=n,n=null),o=l.call(this,s?i:bn(i))||this;var u=o.vars,f=u.duration,c=u.delay,h=u.immediateRender,d=u.stagger,a=u.overwrite,_=u.keyframes,p=u.defaults,y=u.scrollTrigger,P=u.yoyoEase,D=i.parent||Ae,O=(mt(t)||_l(t)?Nr(t[0]):"length"in i)?[t]:or(t),k,b,E,T,v,M,R,I;if(o._targets=O.length?jo(O):In("GSAP target "+t+" not found. https://gsap.com",!Gt.nullTargetWarn)||[],o._ptLookup=[],o._overwrite=a,_||d||jn(f)||jn(c)){if(i=o.vars,k=o.timeline=new St({data:"nested",defaults:p||{},targets:D&&D.data==="nested"?D.vars.targets:O}),k.kill(),k.parent=k._dp=Mr(o),k._start=0,d||jn(f)||jn(c)){if(T=O.length,R=d&&Al(d),Pr(d))for(v in d)~ql.indexOf(v)&&(I||(I={}),I[v]=d[v]);for(b=0;b<T;b++)E=Ps(i,Kl),E.stagger=0,P&&(E.yoyoEase=P),I&&Qi(E,I),M=O[b],E.duration=+Sn(f,Mr(o),b,M,O),E.delay=(+Sn(c,Mr(o),b,M,O)||0)-o._delay,!d&&T===1&&E.delay&&(o._delay=c=E.delay,o._start+=c,E.delay=0),k.to(M,E,R?R(b,M,O):0),k._ease=he.none;k.duration()?f=c=0:o.timeline=0}else if(_){bn(Kt(k.vars.defaults,{ease:"none"})),k._ease=mi(_.ease||i.ease||"none");var N=0,Y,G,K;if(mt(_))_.forEach(function(V){return k.to(O,V,">")}),k.duration();else{E={};for(v in _)v==="ease"||v==="easeEach"||Mf(v,_[v],E,_.easeEach);for(v in E)for(Y=E[v].sort(function(V,B){return V.t-B.t}),N=0,b=0;b<Y.length;b++)G=Y[b],K={ease:G.e,duration:(G.t-(b?Y[b-1].t:0))/100*f},K[v]=G.v,k.to(O,K,N),N+=K.duration;k.duration()<f&&k.to({},{duration:f-k.duration()})}}f||o.duration(f=k.duration())}else o.timeline=0;return a===!0&&!Ho&&(Hr=Mr(o),Ae.killTweensOf(O),Hr=0),br(D,Mr(o),n),i.reversed&&o.reverse(),i.paused&&o.paused(!0),(h||!f&&!_&&o._start===qe(D._time)&&Et(h)&&sf(Mr(o))&&D.data!=="nested")&&(o._tTime=-Se,o.render(Math.max(0,-c)||0)),y&&Dl(Mr(o),y),o}var r=e.prototype;return r.render=function(i,n,s){var o=this._time,u=this._tDur,f=this._dur,c=i<0,h=i>u-Se&&!c?u:i<Se?0:i,d,a,_,p,y,P,D,O,k;if(!f)af(this,i,n,s);else if(h!==this._tTime||!i||s||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==c||this._lazy){if(d=h,O=this.timeline,this._repeat){if(p=f+this._rDelay,this._repeat<-1&&c)return this.totalTime(p*100+i,n,s);if(d=qe(h%p),h===u?(_=this._repeat,d=f):(y=qe(h/p),_=~~y,_&&_===y?(d=f,_--):d>f&&(d=f)),P=this._yoyo&&_&1,P&&(k=this._yEase,d=f-d),y=ji(this._tTime,p),d===o&&!s&&this._initted&&_===y)return this._tTime=h,this;_!==y&&(O&&this._yEase&&Hl(O,P),this.vars.repeatRefresh&&!P&&!this._lock&&d!==p&&this._initted&&(this._lock=s=1,this.render(qe(p*_),!0).invalidate()._lock=0))}if(!this._initted){if(El(this,c?i:d,s,n,h))return this._tTime=0,this;if(o!==this._time&&!(s&&this.vars.repeatRefresh&&_!==y))return this;if(f!==this._dur)return this.render(i,n,s)}if(this._tTime=h,this._time=d,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=D=(k||this._ease)(d/f),this._from&&(this.ratio=D=1-D),!o&&h&&!n&&!y&&(Ut(this,"onStart"),this._tTime!==h))return this;for(a=this._pt;a;)a.r(D,a.d),a=a._next;O&&O.render(i<0?i:O._dur*O._ease(d/this._dur),n,s)||this._startAt&&(this._zTime=i),this._onUpdate&&!n&&(c&&mo(this,i,n,s),Ut(this,"onUpdate")),this._repeat&&_!==y&&this.vars.onRepeat&&!n&&this.parent&&Ut(this,"onRepeat"),(h===this._tDur||!h)&&this._tTime===h&&(c&&!this._onUpdate&&mo(this,i,!0,!0),(i||!f)&&(h===this._tDur&&this._ts>0||!h&&this._ts<0)&&Jr(this,1),!n&&!(c&&!o)&&(h||o||P)&&(Ut(this,h===u?"onComplete":"onReverseComplete",!0),this._prom&&!(h<u&&this.timeScale()>0)&&this._prom()))}return this},r.targets=function(){return this._targets},r.invalidate=function(i){return(!i||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(i),l.prototype.invalidate.call(this,i)},r.resetTo=function(i,n,s,o,u){Xn||Wt.wake(),this._ts||this.play();var f=Math.min(this._dur,(this._dp._time-this._start)*this._ts),c;return this._initted||ea(this,f),c=this._ease(f/this._dur),Pf(this,i,n,s,o,c,f,u)?this.resetTo(i,n,s,o,1):(Xs(this,0),this.parent||kl(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},r.kill=function(i,n){if(n===void 0&&(n="all"),!i&&(!n||n==="all"))return this._lazy=this._pt=0,this.parent?cn(this):this.scrollTrigger&&this.scrollTrigger.kill(!!lt),this;if(this.timeline){var s=this.timeline.totalDuration();return this.timeline.killTweensOf(i,n,Hr&&Hr.vars.overwrite!==!0)._first||cn(this),this.parent&&s!==this.timeline.totalDuration()&&Zi(this,this._dur*this.timeline._tDur/s,0,1),this}var o=this._targets,u=i?or(i):o,f=this._ptLookup,c=this._pt,h,d,a,_,p,y,P;if((!n||n==="all")&&rf(o,u))return n==="all"&&(this._pt=0),cn(this);for(h=this._op=this._op||[],n!=="all"&&(rt(n)&&(p={},Ot(n,function(D){return p[D]=1}),n=p),n=kf(o,n)),P=o.length;P--;)if(~u.indexOf(o[P])){d=f[P],n==="all"?(h[P]=n,_=d,a={}):(a=h[P]=h[P]||{},_=n);for(p in _)y=d&&d[p],y&&((!("kill"in y.d)||y.d.kill(p)===!0)&&zs(this,y,"_pt"),delete d[p]),a!=="all"&&(a[p]=1)}return this._initted&&!this._pt&&c&&cn(this),this},e.to=function(i,n){return new e(i,n,arguments[2])},e.from=function(i,n){return Tn(1,arguments)},e.delayedCall=function(i,n,s,o){return new e(n,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:i,onComplete:n,onReverseComplete:n,onCompleteParams:s,onReverseCompleteParams:s,callbackScope:o})},e.fromTo=function(i,n,s){return Tn(2,arguments)},e.set=function(i,n){return n.duration=0,n.repeatDelay||(n.repeat=0),new e(i,n)},e.killTweensOf=function(i,n,s){return Ae.killTweensOf(i,n,s)},e}(Bn);Kt(Ge.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});Ot("staggerTo,staggerFrom,staggerFromTo",function(l){Ge[l]=function(){var e=new St,r=xo.call(arguments,0);return r.splice(l==="staggerFromTo"?5:4,0,0),e[l].apply(e,r)}});var ta=function(e,r,t){return e[r]=t},Ql=function(e,r,t){return e[r](t)},Df=function(e,r,t,i){return e[r](i.fp,t)},Ef=function(e,r,t){return e.setAttribute(r,t)},ra=function(e,r){return Ye(e[r])?Ql:Vo(e[r])&&e.setAttribute?Ef:ta},jl=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e6)/1e6,r)},Of=function(e,r){return r.set(r.t,r.p,!!(r.s+r.c*e),r)},Zl=function(e,r){var t=r._pt,i="";if(!e&&r.b)i=r.b;else if(e===1&&r.e)i=r.e;else{for(;t;)i=t.p+(t.m?t.m(t.s+t.c*e):Math.round((t.s+t.c*e)*1e4)/1e4)+i,t=t._next;i+=r.c}r.set(r.t,r.p,i,r)},ia=function(e,r){for(var t=r._pt;t;)t.r(e,t.d),t=t._next},Rf=function(e,r,t,i){for(var n=this._pt,s;n;)s=n._next,n.p===i&&n.modifier(e,r,t),n=s},Af=function(e){for(var r=this._pt,t,i;r;)i=r._next,r.p===e&&!r.op||r.op===e?zs(this,r,"_pt"):r.dep||(t=1),r=i;return!t},Lf=function(e,r,t,i){i.mSet(e,r,i.m.call(i.tween,t,i.mt),i)},Jl=function(e){for(var r=e._pt,t,i,n,s;r;){for(t=r._next,i=n;i&&i.pr>r.pr;)i=i._next;(r._prev=i?i._prev:s)?r._prev._next=r:n=r,(r._next=i)?i._prev=r:s=r,r=t}e._pt=n},Rt=function(){function l(r,t,i,n,s,o,u,f,c){this.t=t,this.s=n,this.c=s,this.p=i,this.r=o||jl,this.d=u||this,this.set=f||ta,this.pr=c||0,this._next=r,r&&(r._prev=this)}var e=l.prototype;return e.modifier=function(t,i,n){this.mSet=this.mSet||this.set,this.set=Lf,this.m=t,this.mt=n,this.tween=i},l}();Ot(Qo+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(l){return Ko[l]=1});qt.TweenMax=qt.TweenLite=Ge;qt.TimelineLite=qt.TimelineMax=St;Ae=new St({sortChildren:!1,defaults:Ki,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});Gt.stringFilter=$l;var yi=[],ps={},Ff=[],Pa=0,Nf=0,Ks=function(e){return(ps[e]||Ff).map(function(r){return r()})},To=function(){var e=Date.now(),r=[];e-Pa>2&&(Ks("matchMediaInit"),yi.forEach(function(t){var i=t.queries,n=t.conditions,s,o,u,f;for(o in i)s=vr.matchMedia(i[o]).matches,s&&(u=1),s!==n[o]&&(n[o]=s,f=1);f&&(t.revert(),u&&r.push(t))}),Ks("matchMediaRevert"),r.forEach(function(t){return t.onMatch(t,function(i){return t.add(null,i)})}),Pa=e,Ks("matchMedia"))},eu=function(){function l(r,t){this.selector=t&&vo(t),this.data=[],this._r=[],this.isReverted=!1,this.id=Nf++,r&&this.add(r)}var e=l.prototype;return e.add=function(t,i,n){Ye(t)&&(n=i,i=t,t=Ye);var s=this,o=function(){var f=Ee,c=s.selector,h;return f&&f!==s&&f.data.push(s),n&&(s.selector=vo(n)),Ee=s,h=i.apply(s,arguments),Ye(h)&&s._r.push(h),Ee=f,s.selector=c,s.isReverted=!1,h};return s.last=o,t===Ye?o(s,function(u){return s.add(null,u)}):t?s[t]=o:o},e.ignore=function(t){var i=Ee;Ee=null,t(this),Ee=i},e.getTweens=function(){var t=[];return this.data.forEach(function(i){return i instanceof l?t.push.apply(t,i.getTweens()):i instanceof Ge&&!(i.parent&&i.parent.data==="nested")&&t.push(i)}),t},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(t,i){var n=this;if(t?function(){for(var o=n.getTweens(),u=n.data.length,f;u--;)f=n.data[u],f.data==="isFlip"&&(f.revert(),f.getChildren(!0,!0,!1).forEach(function(c){return o.splice(o.indexOf(c),1)}));for(o.map(function(c){return{g:c._dur||c._delay||c._sat&&!c._sat.vars.immediateRender?c.globalTime(0):-1/0,t:c}}).sort(function(c,h){return h.g-c.g||-1/0}).forEach(function(c){return c.t.revert(t)}),u=n.data.length;u--;)f=n.data[u],f instanceof St?f.data!=="nested"&&(f.scrollTrigger&&f.scrollTrigger.revert(),f.kill()):!(f instanceof Ge)&&f.revert&&f.revert(t);n._r.forEach(function(c){return c(t,n)}),n.isReverted=!0}():this.data.forEach(function(o){return o.kill&&o.kill()}),this.clear(),i)for(var s=yi.length;s--;)yi[s].id===this.id&&yi.splice(s,1)},e.revert=function(t){this.kill(t||{})},l}(),If=function(){function l(r){this.contexts=[],this.scope=r,Ee&&Ee.data.push(this)}var e=l.prototype;return e.add=function(t,i,n){Pr(t)||(t={matches:t});var s=new eu(0,n||this.scope),o=s.conditions={},u,f,c;Ee&&!s.selector&&(s.selector=Ee.selector),this.contexts.push(s),i=s.add("onMatch",i),s.queries=t;for(f in t)f==="all"?c=1:(u=vr.matchMedia(t[f]),u&&(yi.indexOf(s)<0&&yi.push(s),(o[f]=u.matches)&&(c=1),u.addListener?u.addListener(To):u.addEventListener("change",To)));return c&&i(s,function(h){return s.add(null,h)}),this},e.revert=function(t){this.kill(t||{})},e.kill=function(t){this.contexts.forEach(function(i){return i.kill(t,!0)})},l}(),Ms={registerPlugin:function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];r.forEach(function(i){return Yl(i)})},timeline:function(e){return new St(e)},getTweensOf:function(e,r){return Ae.getTweensOf(e,r)},getProperty:function(e,r,t,i){rt(e)&&(e=or(e)[0]);var n=_i(e||{}).get,s=t?Pl:Cl;return t==="native"&&(t=""),e&&(r?s((Bt[r]&&Bt[r].get||n)(e,r,t,i)):function(o,u,f){return s((Bt[o]&&Bt[o].get||n)(e,o,u,f))})},quickSetter:function(e,r,t){if(e=or(e),e.length>1){var i=e.map(function(c){return Lt.quickSetter(c,r,t)}),n=i.length;return function(c){for(var h=n;h--;)i[h](c)}}e=e[0]||{};var s=Bt[r],o=_i(e),u=o.harness&&(o.harness.aliases||{})[r]||r,f=s?function(c){var h=new s;zi._pt=0,h.init(e,t?c+t:c,zi,0,[e]),h.render(1,h),zi._pt&&ia(1,zi)}:o.set(e,u);return s?f:function(c){return f(e,u,t?c+t:c,o,1)}},quickTo:function(e,r,t){var i,n=Lt.to(e,Kt((i={},i[r]="+=0.1",i.paused=!0,i.stagger=0,i),t||{})),s=function(u,f,c){return n.resetTo(r,u,f,c)};return s.tween=n,s},isTweening:function(e){return Ae.getTweensOf(e,!0).length>0},defaults:function(e){return e&&e.ease&&(e.ease=mi(e.ease,Ki.ease)),wa(Ki,e||{})},config:function(e){return wa(Gt,e||{})},registerEffect:function(e){var r=e.name,t=e.effect,i=e.plugins,n=e.defaults,s=e.extendTimeline;(i||"").split(",").forEach(function(o){return o&&!Bt[o]&&!qt[o]&&In(r+" effect requires "+o+" plugin.")}),Vs[r]=function(o,u,f){return t(or(o),Kt(u||{},n),f)},s&&(St.prototype[r]=function(o,u,f){return this.add(Vs[r](o,Pr(u)?u:(f=u)&&{},this),f)})},registerEase:function(e,r){he[e]=mi(r)},parseEase:function(e,r){return arguments.length?mi(e,r):he},getById:function(e){return Ae.getById(e)},exportRoot:function(e,r){e===void 0&&(e={});var t=new St(e),i,n;for(t.smoothChildTiming=Et(e.smoothChildTiming),Ae.remove(t),t._dp=0,t._time=t._tTime=Ae._time,i=Ae._first;i;)n=i._next,(r||!(!i._dur&&i instanceof Ge&&i.vars.onComplete===i._targets[0]))&&br(t,i,i._start-i._delay),i=n;return br(Ae,t,0),t},context:function(e,r){return e?new eu(e,r):Ee},matchMedia:function(e){return new If(e)},matchMediaRefresh:function(){return yi.forEach(function(e){var r=e.conditions,t,i;for(i in r)r[i]&&(r[i]=!1,t=1);t&&e.revert()})||To()},addEventListener:function(e,r){var t=ps[e]||(ps[e]=[]);~t.indexOf(r)||t.push(r)},removeEventListener:function(e,r){var t=ps[e],i=t&&t.indexOf(r);i>=0&&t.splice(i,1)},utils:{wrap:_f,wrapYoyo:gf,distribute:Al,random:Fl,snap:Ll,normalize:pf,getUnit:_t,clamp:ff,splitColor:Xl,toArray:or,selector:vo,mapRange:Il,pipe:hf,unitize:df,interpolate:mf,shuffle:Rl},install:vl,effects:Vs,ticker:Wt,updateRoot:St.updateRoot,plugins:Bt,globalTimeline:Ae,core:{PropTween:Rt,globals:wl,Tween:Ge,Timeline:St,Animation:Bn,getCache:_i,_removeLinkedListItem:zs,reverting:function(){return lt},context:function(e){return e&&Ee&&(Ee.data.push(e),e._ctx=Ee),Ee},suppressOverwrites:function(e){return Ho=e}}};Ot("to,from,fromTo,delayedCall,set,killTweensOf",function(l){return Ms[l]=Ge[l]});Wt.add(St.updateRoot);zi=Ms.to({},{duration:0});var zf=function(e,r){for(var t=e._pt;t&&t.p!==r&&t.op!==r&&t.fp!==r;)t=t._next;return t},Yf=function(e,r){var t=e._targets,i,n,s;for(i in r)for(n=t.length;n--;)s=e._ptLookup[n][i],s&&(s=s.d)&&(s._pt&&(s=zf(s,i)),s&&s.modifier&&s.modifier(r[i],e,t[n],i))},Qs=function(e,r){return{name:e,headless:1,rawVars:1,init:function(i,n,s){s._onInit=function(o){var u,f;if(rt(n)&&(u={},Ot(n,function(c){return u[c]=1}),n=u),r){u={};for(f in n)u[f]=r(n[f]);n=u}Yf(o,n)}}}},Lt=Ms.registerPlugin({name:"attr",init:function(e,r,t,i,n){var s,o,u;this.tween=t;for(s in r)u=e.getAttribute(s)||"",o=this.add(e,"setAttribute",(u||0)+"",r[s],i,n,0,0,s),o.op=s,o.b=u,this._props.push(s)},render:function(e,r){for(var t=r._pt;t;)lt?t.set(t.t,t.p,t.b,t):t.r(e,t.d),t=t._next}},{name:"endArray",headless:1,init:function(e,r){for(var t=r.length;t--;)this.add(e,t,e[t]||0,r[t],0,0,0,0,0,1)}},Qs("roundProps",wo),Qs("modifiers"),Qs("snap",Ll))||Ms;Ge.version=St.version=Lt.version="3.13.0";xl=1;Uo()&&Ji();he.Power0;he.Power1;he.Power2;he.Power3;he.Power4;he.Linear;he.Quad;he.Cubic;he.Quart;he.Quint;he.Strong;he.Elastic;he.Back;he.SteppedEase;he.Bounce;he.Sine;he.Expo;he.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var ka,Vr,Bi,na,di,Ma,sa,Xf=function(){return typeof window<"u"},Ir={},ui=180/Math.PI,$i=Math.PI/180,Ei=Math.atan2,Da=1e8,oa=/([A-Z])/g,Bf=/(left|right|width|margin|padding|x)/i,$f=/[\s,\(]\S/,Tr={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},So=function(e,r){return r.set(r.t,r.p,Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},Wf=function(e,r){return r.set(r.t,r.p,e===1?r.e:Math.round((r.s+r.c*e)*1e4)/1e4+r.u,r)},Hf=function(e,r){return r.set(r.t,r.p,e?Math.round((r.s+r.c*e)*1e4)/1e4+r.u:r.b,r)},Vf=function(e,r){var t=r.s+r.c*e;r.set(r.t,r.p,~~(t+(t<0?-.5:.5))+r.u,r)},tu=function(e,r){return r.set(r.t,r.p,e?r.e:r.b,r)},ru=function(e,r){return r.set(r.t,r.p,e!==1?r.b:r.e,r)},Uf=function(e,r,t){return e.style[r]=t},Gf=function(e,r,t){return e.style.setProperty(r,t)},qf=function(e,r,t){return e._gsap[r]=t},Kf=function(e,r,t){return e._gsap.scaleX=e._gsap.scaleY=t},Qf=function(e,r,t,i,n){var s=e._gsap;s.scaleX=s.scaleY=t,s.renderTransform(n,s)},jf=function(e,r,t,i,n){var s=e._gsap;s[r]=t,s.renderTransform(n,s)},Le="transform",At=Le+"Origin",Zf=function l(e,r){var t=this,i=this.target,n=i.style,s=i._gsap;if(e in Ir&&n){if(this.tfm=this.tfm||{},e!=="transform")e=Tr[e]||e,~e.indexOf(",")?e.split(",").forEach(function(o){return t.tfm[o]=Dr(i,o)}):this.tfm[e]=s.x?s[e]:Dr(i,e),e===At&&(this.tfm.zOrigin=s.zOrigin);else return Tr.transform.split(",").forEach(function(o){return l.call(t,o,r)});if(this.props.indexOf(Le)>=0)return;s.svg&&(this.svgo=i.getAttribute("data-svg-origin"),this.props.push(At,r,"")),e=Le}(n||r)&&this.props.push(e,r,n[e])},iu=function(e){e.translate&&(e.removeProperty("translate"),e.removeProperty("scale"),e.removeProperty("rotate"))},Jf=function(){var e=this.props,r=this.target,t=r.style,i=r._gsap,n,s;for(n=0;n<e.length;n+=3)e[n+1]?e[n+1]===2?r[e[n]](e[n+2]):r[e[n]]=e[n+2]:e[n+2]?t[e[n]]=e[n+2]:t.removeProperty(e[n].substr(0,2)==="--"?e[n]:e[n].replace(oa,"-$1").toLowerCase());if(this.tfm){for(s in this.tfm)i[s]=this.tfm[s];i.svg&&(i.renderTransform(),r.setAttribute("data-svg-origin",this.svgo||"")),n=sa(),(!n||!n.isStart)&&!t[Le]&&(iu(t),i.zOrigin&&t[At]&&(t[At]+=" "+i.zOrigin+"px",i.zOrigin=0,i.renderTransform()),i.uncache=1)}},nu=function(e,r){var t={target:e,props:[],revert:Jf,save:Zf};return e._gsap||Lt.core.getCache(e),r&&e.style&&e.nodeType&&r.split(",").forEach(function(i){return t.save(i)}),t},su,Co=function(e,r){var t=Vr.createElementNS?Vr.createElementNS((r||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):Vr.createElement(e);return t&&t.style?t:Vr.createElement(e)},ar=function l(e,r,t){var i=getComputedStyle(e);return i[r]||i.getPropertyValue(r.replace(oa,"-$1").toLowerCase())||i.getPropertyValue(r)||!t&&l(e,en(r)||r,1)||""},Ea="O,Moz,ms,Ms,Webkit".split(","),en=function(e,r,t){var i=r||di,n=i.style,s=5;if(e in n&&!t)return e;for(e=e.charAt(0).toUpperCase()+e.substr(1);s--&&!(Ea[s]+e in n););return s<0?null:(s===3?"ms":s>=0?Ea[s]:"")+e},Po=function(){Xf()&&window.document&&(ka=window,Vr=ka.document,Bi=Vr.documentElement,di=Co("div")||{style:{}},Co("div"),Le=en(Le),At=Le+"Origin",di.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",su=!!en("perspective"),sa=Lt.core.reverting,na=1)},Oa=function(e){var r=e.ownerSVGElement,t=Co("svg",r&&r.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),i=e.cloneNode(!0),n;i.style.display="block",t.appendChild(i),Bi.appendChild(t);try{n=i.getBBox()}catch{}return t.removeChild(i),Bi.removeChild(t),n},Ra=function(e,r){for(var t=r.length;t--;)if(e.hasAttribute(r[t]))return e.getAttribute(r[t])},ou=function(e){var r,t;try{r=e.getBBox()}catch{r=Oa(e),t=1}return r&&(r.width||r.height)||t||(r=Oa(e)),r&&!r.width&&!r.x&&!r.y?{x:+Ra(e,["x","cx","x1"])||0,y:+Ra(e,["y","cy","y1"])||0,width:0,height:0}:r},au=function(e){return!!(e.getCTM&&(!e.parentNode||e.ownerSVGElement)&&ou(e))},Si=function(e,r){if(r){var t=e.style,i;r in Ir&&r!==At&&(r=Le),t.removeProperty?(i=r.substr(0,2),(i==="ms"||r.substr(0,6)==="webkit")&&(r="-"+r),t.removeProperty(i==="--"?r:r.replace(oa,"-$1").toLowerCase())):t.removeAttribute(r)}},Ur=function(e,r,t,i,n,s){var o=new Rt(e._pt,r,t,0,1,s?ru:tu);return e._pt=o,o.b=i,o.e=n,e._props.push(t),o},Aa={deg:1,rad:1,turn:1},ec={grid:1,flex:1},ei=function l(e,r,t,i){var n=parseFloat(t)||0,s=(t+"").trim().substr((n+"").length)||"px",o=di.style,u=Bf.test(r),f=e.tagName.toLowerCase()==="svg",c=(f?"client":"offset")+(u?"Width":"Height"),h=100,d=i==="px",a=i==="%",_,p,y,P;if(i===s||!n||Aa[i]||Aa[s])return n;if(s!=="px"&&!d&&(n=l(e,r,t,"px")),P=e.getCTM&&au(e),(a||s==="%")&&(Ir[r]||~r.indexOf("adius")))return _=P?e.getBBox()[u?"width":"height"]:e[c],$e(a?n/_*h:n/100*_);if(o[u?"width":"height"]=h+(d?s:i),p=i!=="rem"&&~r.indexOf("adius")||i==="em"&&e.appendChild&&!f?e:e.parentNode,P&&(p=(e.ownerSVGElement||{}).parentNode),(!p||p===Vr||!p.appendChild)&&(p=Vr.body),y=p._gsap,y&&a&&y.width&&u&&y.time===Wt.time&&!y.uncache)return $e(n/y.width*h);if(a&&(r==="height"||r==="width")){var D=e.style[r];e.style[r]=h+i,_=e[c],D?e.style[r]=D:Si(e,r)}else(a||s==="%")&&!ec[ar(p,"display")]&&(o.position=ar(e,"position")),p===e&&(o.position="static"),p.appendChild(di),_=di[c],p.removeChild(di),o.position="absolute";return u&&a&&(y=_i(p),y.time=Wt.time,y.width=p[c]),$e(d?_*n/h:_&&n?h/_*n:0)},Dr=function(e,r,t,i){var n;return na||Po(),r in Tr&&r!=="transform"&&(r=Tr[r],~r.indexOf(",")&&(r=r.split(",")[0])),Ir[r]&&r!=="transform"?(n=Wn(e,i),n=r!=="transformOrigin"?n[r]:n.svg?n.origin:Es(ar(e,At))+" "+n.zOrigin+"px"):(n=e.style[r],(!n||n==="auto"||i||~(n+"").indexOf("calc("))&&(n=Ds[r]&&Ds[r](e,r,t)||ar(e,r)||Tl(e,r)||(r==="opacity"?1:0))),t&&!~(n+"").trim().indexOf(" ")?ei(e,r,n,t)+t:n},tc=function(e,r,t,i){if(!t||t==="none"){var n=en(r,e,1),s=n&&ar(e,n,1);s&&s!==t?(r=n,t=s):r==="borderColor"&&(t=ar(e,"borderTopColor"))}var o=new Rt(this._pt,e.style,r,0,1,Zl),u=0,f=0,c,h,d,a,_,p,y,P,D,O,k,b;if(o.b=t,o.e=i,t+="",i+="",i.substring(0,6)==="var(--"&&(i=ar(e,i.substring(4,i.indexOf(")")))),i==="auto"&&(p=e.style[r],e.style[r]=i,i=ar(e,r)||i,p?e.style[r]=p:Si(e,r)),c=[t,i],$l(c),t=c[0],i=c[1],d=t.match(Ii)||[],b=i.match(Ii)||[],b.length){for(;h=Ii.exec(i);)y=h[0],D=i.substring(u,h.index),_?_=(_+1)%5:(D.substr(-5)==="rgba("||D.substr(-5)==="hsla(")&&(_=1),y!==(p=d[f++]||"")&&(a=parseFloat(p)||0,k=p.substr((a+"").length),y.charAt(1)==="="&&(y=Xi(a,y)+k),P=parseFloat(y),O=y.substr((P+"").length),u=Ii.lastIndex-O.length,O||(O=O||Gt.units[r]||k,u===i.length&&(i+=O,o.e+=O)),k!==O&&(a=ei(e,r,p,O)||0),o._pt={_next:o._pt,p:D||f===1?D:",",s:a,c:P-a,m:_&&_<4||r==="zIndex"?Math.round:0});o.c=u<i.length?i.substring(u,i.length):""}else o.r=r==="display"&&i==="none"?ru:tu;return ml.test(i)&&(o.e=0),this._pt=o,o},La={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},rc=function(e){var r=e.split(" "),t=r[0],i=r[1]||"50%";return(t==="top"||t==="bottom"||i==="left"||i==="right")&&(e=t,t=i,i=e),r[0]=La[t]||t,r[1]=La[i]||i,r.join(" ")},ic=function(e,r){if(r.tween&&r.tween._time===r.tween._dur){var t=r.t,i=t.style,n=r.u,s=t._gsap,o,u,f;if(n==="all"||n===!0)i.cssText="",u=1;else for(n=n.split(","),f=n.length;--f>-1;)o=n[f],Ir[o]&&(u=1,o=o==="transformOrigin"?At:Le),Si(t,o);u&&(Si(t,Le),s&&(s.svg&&t.removeAttribute("transform"),i.scale=i.rotate=i.translate="none",Wn(t,1),s.uncache=1,iu(i)))}},Ds={clearProps:function(e,r,t,i,n){if(n.data!=="isFromStart"){var s=e._pt=new Rt(e._pt,r,t,0,0,ic);return s.u=i,s.pr=-10,s.tween=n,e._props.push(t),1}}},$n=[1,0,0,1,0,0],lu={},uu=function(e){return e==="matrix(1, 0, 0, 1, 0, 0)"||e==="none"||!e},Fa=function(e){var r=ar(e,Le);return uu(r)?$n:r.substr(7).match(gl).map($e)},aa=function(e,r){var t=e._gsap||_i(e),i=e.style,n=Fa(e),s,o,u,f;return t.svg&&e.getAttribute("transform")?(u=e.transform.baseVal.consolidate().matrix,n=[u.a,u.b,u.c,u.d,u.e,u.f],n.join(",")==="1,0,0,1,0,0"?$n:n):(n===$n&&!e.offsetParent&&e!==Bi&&!t.svg&&(u=i.display,i.display="block",s=e.parentNode,(!s||!e.offsetParent&&!e.getBoundingClientRect().width)&&(f=1,o=e.nextElementSibling,Bi.appendChild(e)),n=Fa(e),u?i.display=u:Si(e,"display"),f&&(o?s.insertBefore(e,o):s?s.appendChild(e):Bi.removeChild(e))),r&&n.length>6?[n[0],n[1],n[4],n[5],n[12],n[13]]:n)},ko=function(e,r,t,i,n,s){var o=e._gsap,u=n||aa(e,!0),f=o.xOrigin||0,c=o.yOrigin||0,h=o.xOffset||0,d=o.yOffset||0,a=u[0],_=u[1],p=u[2],y=u[3],P=u[4],D=u[5],O=r.split(" "),k=parseFloat(O[0])||0,b=parseFloat(O[1])||0,E,T,v,M;t?u!==$n&&(T=a*y-_*p)&&(v=k*(y/T)+b*(-p/T)+(p*D-y*P)/T,M=k*(-_/T)+b*(a/T)-(a*D-_*P)/T,k=v,b=M):(E=ou(e),k=E.x+(~O[0].indexOf("%")?k/100*E.width:k),b=E.y+(~(O[1]||O[0]).indexOf("%")?b/100*E.height:b)),i||i!==!1&&o.smooth?(P=k-f,D=b-c,o.xOffset=h+(P*a+D*p)-P,o.yOffset=d+(P*_+D*y)-D):o.xOffset=o.yOffset=0,o.xOrigin=k,o.yOrigin=b,o.smooth=!!i,o.origin=r,o.originIsAbsolute=!!t,e.style[At]="0px 0px",s&&(Ur(s,o,"xOrigin",f,k),Ur(s,o,"yOrigin",c,b),Ur(s,o,"xOffset",h,o.xOffset),Ur(s,o,"yOffset",d,o.yOffset)),e.setAttribute("data-svg-origin",k+" "+b)},Wn=function(e,r){var t=e._gsap||new Ul(e);if("x"in t&&!r&&!t.uncache)return t;var i=e.style,n=t.scaleX<0,s="px",o="deg",u=getComputedStyle(e),f=ar(e,At)||"0",c,h,d,a,_,p,y,P,D,O,k,b,E,T,v,M,R,I,N,Y,G,K,V,B,te,we,x,q,re,ge,J,Fe;return c=h=d=p=y=P=D=O=k=0,a=_=1,t.svg=!!(e.getCTM&&au(e)),u.translate&&((u.translate!=="none"||u.scale!=="none"||u.rotate!=="none")&&(i[Le]=(u.translate!=="none"?"translate3d("+(u.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(u.rotate!=="none"?"rotate("+u.rotate+") ":"")+(u.scale!=="none"?"scale("+u.scale.split(" ").join(",")+") ":"")+(u[Le]!=="none"?u[Le]:"")),i.scale=i.rotate=i.translate="none"),T=aa(e,t.svg),t.svg&&(t.uncache?(te=e.getBBox(),f=t.xOrigin-te.x+"px "+(t.yOrigin-te.y)+"px",B=""):B=!r&&e.getAttribute("data-svg-origin"),ko(e,B||f,!!B||t.originIsAbsolute,t.smooth!==!1,T)),b=t.xOrigin||0,E=t.yOrigin||0,T!==$n&&(I=T[0],N=T[1],Y=T[2],G=T[3],c=K=T[4],h=V=T[5],T.length===6?(a=Math.sqrt(I*I+N*N),_=Math.sqrt(G*G+Y*Y),p=I||N?Ei(N,I)*ui:0,D=Y||G?Ei(Y,G)*ui+p:0,D&&(_*=Math.abs(Math.cos(D*$i))),t.svg&&(c-=b-(b*I+E*Y),h-=E-(b*N+E*G))):(Fe=T[6],ge=T[7],x=T[8],q=T[9],re=T[10],J=T[11],c=T[12],h=T[13],d=T[14],v=Ei(Fe,re),y=v*ui,v&&(M=Math.cos(-v),R=Math.sin(-v),B=K*M+x*R,te=V*M+q*R,we=Fe*M+re*R,x=K*-R+x*M,q=V*-R+q*M,re=Fe*-R+re*M,J=ge*-R+J*M,K=B,V=te,Fe=we),v=Ei(-Y,re),P=v*ui,v&&(M=Math.cos(-v),R=Math.sin(-v),B=I*M-x*R,te=N*M-q*R,we=Y*M-re*R,J=G*R+J*M,I=B,N=te,Y=we),v=Ei(N,I),p=v*ui,v&&(M=Math.cos(v),R=Math.sin(v),B=I*M+N*R,te=K*M+V*R,N=N*M-I*R,V=V*M-K*R,I=B,K=te),y&&Math.abs(y)+Math.abs(p)>359.9&&(y=p=0,P=180-P),a=$e(Math.sqrt(I*I+N*N+Y*Y)),_=$e(Math.sqrt(V*V+Fe*Fe)),v=Ei(K,V),D=Math.abs(v)>2e-4?v*ui:0,k=J?1/(J<0?-J:J):0),t.svg&&(B=e.getAttribute("transform"),t.forceCSS=e.setAttribute("transform","")||!uu(ar(e,Le)),B&&e.setAttribute("transform",B))),Math.abs(D)>90&&Math.abs(D)<270&&(n?(a*=-1,D+=p<=0?180:-180,p+=p<=0?180:-180):(_*=-1,D+=D<=0?180:-180)),r=r||t.uncache,t.x=c-((t.xPercent=c&&(!r&&t.xPercent||(Math.round(e.offsetWidth/2)===Math.round(-c)?-50:0)))?e.offsetWidth*t.xPercent/100:0)+s,t.y=h-((t.yPercent=h&&(!r&&t.yPercent||(Math.round(e.offsetHeight/2)===Math.round(-h)?-50:0)))?e.offsetHeight*t.yPercent/100:0)+s,t.z=d+s,t.scaleX=$e(a),t.scaleY=$e(_),t.rotation=$e(p)+o,t.rotationX=$e(y)+o,t.rotationY=$e(P)+o,t.skewX=D+o,t.skewY=O+o,t.transformPerspective=k+s,(t.zOrigin=parseFloat(f.split(" ")[2])||!r&&t.zOrigin||0)&&(i[At]=Es(f)),t.xOffset=t.yOffset=0,t.force3D=Gt.force3D,t.renderTransform=t.svg?sc:su?fu:nc,t.uncache=0,t},Es=function(e){return(e=e.split(" "))[0]+" "+e[1]},js=function(e,r,t){var i=_t(r);return $e(parseFloat(r)+parseFloat(ei(e,"x",t+"px",i)))+i},nc=function(e,r){r.z="0px",r.rotationY=r.rotationX="0deg",r.force3D=0,fu(e,r)},si="0deg",an="0px",oi=") ",fu=function(e,r){var t=r||this,i=t.xPercent,n=t.yPercent,s=t.x,o=t.y,u=t.z,f=t.rotation,c=t.rotationY,h=t.rotationX,d=t.skewX,a=t.skewY,_=t.scaleX,p=t.scaleY,y=t.transformPerspective,P=t.force3D,D=t.target,O=t.zOrigin,k="",b=P==="auto"&&e&&e!==1||P===!0;if(O&&(h!==si||c!==si)){var E=parseFloat(c)*$i,T=Math.sin(E),v=Math.cos(E),M;E=parseFloat(h)*$i,M=Math.cos(E),s=js(D,s,T*M*-O),o=js(D,o,-Math.sin(E)*-O),u=js(D,u,v*M*-O+O)}y!==an&&(k+="perspective("+y+oi),(i||n)&&(k+="translate("+i+"%, "+n+"%) "),(b||s!==an||o!==an||u!==an)&&(k+=u!==an||b?"translate3d("+s+", "+o+", "+u+") ":"translate("+s+", "+o+oi),f!==si&&(k+="rotate("+f+oi),c!==si&&(k+="rotateY("+c+oi),h!==si&&(k+="rotateX("+h+oi),(d!==si||a!==si)&&(k+="skew("+d+", "+a+oi),(_!==1||p!==1)&&(k+="scale("+_+", "+p+oi),D.style[Le]=k||"translate(0, 0)"},sc=function(e,r){var t=r||this,i=t.xPercent,n=t.yPercent,s=t.x,o=t.y,u=t.rotation,f=t.skewX,c=t.skewY,h=t.scaleX,d=t.scaleY,a=t.target,_=t.xOrigin,p=t.yOrigin,y=t.xOffset,P=t.yOffset,D=t.forceCSS,O=parseFloat(s),k=parseFloat(o),b,E,T,v,M;u=parseFloat(u),f=parseFloat(f),c=parseFloat(c),c&&(c=parseFloat(c),f+=c,u+=c),u||f?(u*=$i,f*=$i,b=Math.cos(u)*h,E=Math.sin(u)*h,T=Math.sin(u-f)*-d,v=Math.cos(u-f)*d,f&&(c*=$i,M=Math.tan(f-c),M=Math.sqrt(1+M*M),T*=M,v*=M,c&&(M=Math.tan(c),M=Math.sqrt(1+M*M),b*=M,E*=M)),b=$e(b),E=$e(E),T=$e(T),v=$e(v)):(b=h,v=d,E=T=0),(O&&!~(s+"").indexOf("px")||k&&!~(o+"").indexOf("px"))&&(O=ei(a,"x",s,"px"),k=ei(a,"y",o,"px")),(_||p||y||P)&&(O=$e(O+_-(_*b+p*T)+y),k=$e(k+p-(_*E+p*v)+P)),(i||n)&&(M=a.getBBox(),O=$e(O+i/100*M.width),k=$e(k+n/100*M.height)),M="matrix("+b+","+E+","+T+","+v+","+O+","+k+")",a.setAttribute("transform",M),D&&(a.style[Le]=M)},oc=function(e,r,t,i,n){var s=360,o=rt(n),u=parseFloat(n)*(o&&~n.indexOf("rad")?ui:1),f=u-i,c=i+f+"deg",h,d;return o&&(h=n.split("_")[1],h==="short"&&(f%=s,f!==f%(s/2)&&(f+=f<0?s:-s)),h==="cw"&&f<0?f=(f+s*Da)%s-~~(f/s)*s:h==="ccw"&&f>0&&(f=(f-s*Da)%s-~~(f/s)*s)),e._pt=d=new Rt(e._pt,r,t,i,f,Wf),d.e=c,d.u="deg",e._props.push(t),d},Na=function(e,r){for(var t in r)e[t]=r[t];return e},ac=function(e,r,t){var i=Na({},t._gsap),n="perspective,force3D,transformOrigin,svgOrigin",s=t.style,o,u,f,c,h,d,a,_;i.svg?(f=t.getAttribute("transform"),t.setAttribute("transform",""),s[Le]=r,o=Wn(t,1),Si(t,Le),t.setAttribute("transform",f)):(f=getComputedStyle(t)[Le],s[Le]=r,o=Wn(t,1),s[Le]=f);for(u in Ir)f=i[u],c=o[u],f!==c&&n.indexOf(u)<0&&(a=_t(f),_=_t(c),h=a!==_?ei(t,u,f,_):parseFloat(f),d=parseFloat(c),e._pt=new Rt(e._pt,o,u,h,d-h,So),e._pt.u=_||0,e._props.push(u));Na(o,i)};Ot("padding,margin,Width,Radius",function(l,e){var r="Top",t="Right",i="Bottom",n="Left",s=(e<3?[r,t,i,n]:[r+n,r+t,i+t,i+n]).map(function(o){return e<2?l+o:"border"+o+l});Ds[e>1?"border"+l:l]=function(o,u,f,c,h){var d,a;if(arguments.length<4)return d=s.map(function(_){return Dr(o,_,f)}),a=d.join(" "),a.split(d[0]).length===5?d[0]:a;d=(c+"").split(" "),a={},s.forEach(function(_,p){return a[_]=d[p]=d[p]||d[(p-1)/2|0]}),o.init(u,a,h)}});var cu={name:"css",register:Po,targetTest:function(e){return e.style&&e.nodeType},init:function(e,r,t,i,n){var s=this._props,o=e.style,u=t.vars.startAt,f,c,h,d,a,_,p,y,P,D,O,k,b,E,T,v;na||Po(),this.styles=this.styles||nu(e),v=this.styles.props,this.tween=t;for(p in r)if(p!=="autoRound"&&(c=r[p],!(Bt[p]&&Gl(p,r,t,i,e,n)))){if(a=typeof c,_=Ds[p],a==="function"&&(c=c.call(t,i,e,n),a=typeof c),a==="string"&&~c.indexOf("random(")&&(c=Yn(c)),_)_(this,e,p,c,t)&&(T=1);else if(p.substr(0,2)==="--")f=(getComputedStyle(e).getPropertyValue(p)+"").trim(),c+="",jr.lastIndex=0,jr.test(f)||(y=_t(f),P=_t(c)),P?y!==P&&(f=ei(e,p,f,P)+P):y&&(c+=y),this.add(o,"setProperty",f,c,i,n,0,0,p),s.push(p),v.push(p,0,o[p]);else if(a!=="undefined"){if(u&&p in u?(f=typeof u[p]=="function"?u[p].call(t,i,e,n):u[p],rt(f)&&~f.indexOf("random(")&&(f=Yn(f)),_t(f+"")||f==="auto"||(f+=Gt.units[p]||_t(Dr(e,p))||""),(f+"").charAt(1)==="="&&(f=Dr(e,p))):f=Dr(e,p),d=parseFloat(f),D=a==="string"&&c.charAt(1)==="="&&c.substr(0,2),D&&(c=c.substr(2)),h=parseFloat(c),p in Tr&&(p==="autoAlpha"&&(d===1&&Dr(e,"visibility")==="hidden"&&h&&(d=0),v.push("visibility",0,o.visibility),Ur(this,o,"visibility",d?"inherit":"hidden",h?"inherit":"hidden",!h)),p!=="scale"&&p!=="transform"&&(p=Tr[p],~p.indexOf(",")&&(p=p.split(",")[0]))),O=p in Ir,O){if(this.styles.save(p),a==="string"&&c.substring(0,6)==="var(--"&&(c=ar(e,c.substring(4,c.indexOf(")"))),h=parseFloat(c)),k||(b=e._gsap,b.renderTransform&&!r.parseTransform||Wn(e,r.parseTransform),E=r.smoothOrigin!==!1&&b.smooth,k=this._pt=new Rt(this._pt,o,Le,0,1,b.renderTransform,b,0,-1),k.dep=1),p==="scale")this._pt=new Rt(this._pt,b,"scaleY",b.scaleY,(D?Xi(b.scaleY,D+h):h)-b.scaleY||0,So),this._pt.u=0,s.push("scaleY",p),p+="X";else if(p==="transformOrigin"){v.push(At,0,o[At]),c=rc(c),b.svg?ko(e,c,0,E,0,this):(P=parseFloat(c.split(" ")[2])||0,P!==b.zOrigin&&Ur(this,b,"zOrigin",b.zOrigin,P),Ur(this,o,p,Es(f),Es(c)));continue}else if(p==="svgOrigin"){ko(e,c,1,E,0,this);continue}else if(p in lu){oc(this,b,p,d,D?Xi(d,D+c):c);continue}else if(p==="smoothOrigin"){Ur(this,b,"smooth",b.smooth,c);continue}else if(p==="force3D"){b[p]=c;continue}else if(p==="transform"){ac(this,c,e);continue}}else p in o||(p=en(p)||p);if(O||(h||h===0)&&(d||d===0)&&!$f.test(c)&&p in o)y=(f+"").substr((d+"").length),h||(h=0),P=_t(c)||(p in Gt.units?Gt.units[p]:y),y!==P&&(d=ei(e,p,f,P)),this._pt=new Rt(this._pt,O?b:o,p,d,(D?Xi(d,D+h):h)-d,!O&&(P==="px"||p==="zIndex")&&r.autoRound!==!1?Vf:So),this._pt.u=P||0,y!==P&&P!=="%"&&(this._pt.b=f,this._pt.r=Hf);else if(p in o)tc.call(this,e,p,f,D?D+c:c);else if(p in e)this.add(e,p,f||e[p],D?D+c:c,i,n);else if(p!=="parseTransform"){qo(p,c);continue}O||(p in o?v.push(p,0,o[p]):typeof e[p]=="function"?v.push(p,2,e[p]()):v.push(p,1,f||e[p])),s.push(p)}}T&&Jl(this)},render:function(e,r){if(r.tween._time||!sa())for(var t=r._pt;t;)t.r(e,t.d),t=t._next;else r.styles.revert()},get:Dr,aliases:Tr,getSetter:function(e,r,t){var i=Tr[r];return i&&i.indexOf(",")<0&&(r=i),r in Ir&&r!==At&&(e._gsap.x||Dr(e,"x"))?t&&Ma===t?r==="scale"?Kf:qf:(Ma=t||{})&&(r==="scale"?Qf:jf):e.style&&!Vo(e.style[r])?Uf:~r.indexOf("-")?Gf:ra(e,r)},core:{_removeProperty:Si,_getMatrix:aa}};Lt.utils.checkPrefix=en;Lt.core.getStyleSaver=nu;(function(l,e,r,t){var i=Ot(l+","+e+","+r,function(n){Ir[n]=1});Ot(e,function(n){Gt.units[n]="deg",lu[n]=1}),Tr[i[13]]=l+","+e,Ot(t,function(n){var s=n.split(":");Tr[s[1]]=i[s[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");Ot("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(l){Gt.units[l]="px"});Lt.registerPlugin(cu);var Mt=Lt.registerPlugin(cu)||Lt;Mt.core.Tween;function lc(l,e){for(var r=0;r<e.length;r++){var t=e[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(l,t.key,t)}}function uc(l,e,r){return e&&lc(l.prototype,e),l}/*!
 * Observer 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var at,_s,Ht,Gr,qr,Wi,hu,fi,Cn,du,Rr,cr,pu,_u=function(){return at||typeof window<"u"&&(at=window.gsap)&&at.registerPlugin&&at},gu=1,Yi=[],ae=[],Cr=[],Pn=Date.now,Mo=function(e,r){return r},fc=function(){var e=Cn.core,r=e.bridge||{},t=e._scrollers,i=e._proxies;t.push.apply(t,ae),i.push.apply(i,Cr),ae=t,Cr=i,Mo=function(s,o){return r[s](o)}},Zr=function(e,r){return~Cr.indexOf(e)&&Cr[Cr.indexOf(e)+1][r]},kn=function(e){return!!~du.indexOf(e)},vt=function(e,r,t,i,n){return e.addEventListener(r,t,{passive:i!==!1,capture:!!n})},xt=function(e,r,t,i){return e.removeEventListener(r,t,!!i)},Zn="scrollLeft",Jn="scrollTop",Do=function(){return Rr&&Rr.isPressed||ae.cache++},Os=function(e,r){var t=function i(n){if(n||n===0){gu&&(Ht.history.scrollRestoration="manual");var s=Rr&&Rr.isPressed;n=i.v=Math.round(n)||(Rr&&Rr.iOS?1:0),e(n),i.cacheID=ae.cache,s&&Mo("ss",n)}else(r||ae.cache!==i.cacheID||Mo("ref"))&&(i.cacheID=ae.cache,i.v=e());return i.v+i.offset};return t.offset=0,e&&t},Ct={s:Zn,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:Os(function(l){return arguments.length?Ht.scrollTo(l,Je.sc()):Ht.pageXOffset||Gr[Zn]||qr[Zn]||Wi[Zn]||0})},Je={s:Jn,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Ct,sc:Os(function(l){return arguments.length?Ht.scrollTo(Ct.sc(),l):Ht.pageYOffset||Gr[Jn]||qr[Jn]||Wi[Jn]||0})},Dt=function(e,r){return(r&&r._ctx&&r._ctx.selector||at.utils.toArray)(e)[0]||(typeof e=="string"&&at.config().nullTargetWarn!==!1?console.warn("Element not found:",e):null)},cc=function(e,r){for(var t=r.length;t--;)if(r[t]===e||r[t].contains(e))return!0;return!1},ti=function(e,r){var t=r.s,i=r.sc;kn(e)&&(e=Gr.scrollingElement||qr);var n=ae.indexOf(e),s=i===Je.sc?1:2;!~n&&(n=ae.push(e)-1),ae[n+s]||vt(e,"scroll",Do);var o=ae[n+s],u=o||(ae[n+s]=Os(Zr(e,t),!0)||(kn(e)?i:Os(function(f){return arguments.length?e[t]=f:e[t]})));return u.target=e,o||(u.smooth=at.getProperty(e,"scrollBehavior")==="smooth"),u},Eo=function(e,r,t){var i=e,n=e,s=Pn(),o=s,u=r||50,f=Math.max(500,u*3),c=function(_,p){var y=Pn();p||y-s>u?(n=i,i=_,o=s,s=y):t?i+=_:i=n+(_-n)/(y-o)*(s-o)},h=function(){n=i=t?0:i,o=s=0},d=function(_){var p=o,y=n,P=Pn();return(_||_===0)&&_!==i&&c(_),s===o||P-o>f?0:(i+(t?y:-y))/((t?P:s)-p)*1e3};return{update:c,reset:h,getVelocity:d}},ln=function(e,r){return r&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},Ia=function(e){var r=Math.max.apply(Math,e),t=Math.min.apply(Math,e);return Math.abs(r)>=Math.abs(t)?r:t},mu=function(){Cn=at.core.globals().ScrollTrigger,Cn&&Cn.core&&fc()},yu=function(e){return at=e||_u(),!_s&&at&&typeof document<"u"&&document.body&&(Ht=window,Gr=document,qr=Gr.documentElement,Wi=Gr.body,du=[Ht,Gr,qr,Wi],at.utils.clamp,pu=at.core.context||function(){},fi="onpointerenter"in Wi?"pointer":"mouse",hu=We.isTouch=Ht.matchMedia&&Ht.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Ht||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,cr=We.eventTypes=("ontouchstart"in qr?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in qr?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return gu=0},500),mu(),_s=1),_s};Ct.op=Je;ae.cache=0;var We=function(){function l(r){this.init(r)}var e=l.prototype;return e.init=function(t){_s||yu(at)||console.warn("Please gsap.registerPlugin(Observer)"),Cn||mu();var i=t.tolerance,n=t.dragMinimum,s=t.type,o=t.target,u=t.lineHeight,f=t.debounce,c=t.preventDefault,h=t.onStop,d=t.onStopDelay,a=t.ignore,_=t.wheelSpeed,p=t.event,y=t.onDragStart,P=t.onDragEnd,D=t.onDrag,O=t.onPress,k=t.onRelease,b=t.onRight,E=t.onLeft,T=t.onUp,v=t.onDown,M=t.onChangeX,R=t.onChangeY,I=t.onChange,N=t.onToggleX,Y=t.onToggleY,G=t.onHover,K=t.onHoverEnd,V=t.onMove,B=t.ignoreCheck,te=t.isNormalizer,we=t.onGestureStart,x=t.onGestureEnd,q=t.onWheel,re=t.onEnable,ge=t.onDisable,J=t.onClick,Fe=t.scrollSpeed,ye=t.capture,Ce=t.allowClicks,de=t.lockAxis,He=t.onLockAxis;this.target=o=Dt(o)||qr,this.vars=t,a&&(a=at.utils.toArray(a)),i=i||1e-9,n=n||0,_=_||1,Fe=Fe||1,s=s||"wheel,touch,pointer",f=f!==!1,u||(u=parseFloat(Ht.getComputedStyle(Wi).lineHeight)||22);var it,De,Ne,ie,ne,j,ut,w=this,ke=0,Ft=0,Qt=t.passive||!c&&t.passive!==!1,Me=ti(o,Ct),Oe=ti(o,Je),ur=Me(),jt=Oe(),Xe=~s.indexOf("touch")&&!~s.indexOf("pointer")&&cr[0]==="pointerdown",Ke=kn(o),xe=o.ownerDocument||Gr,yt=[0,0,0],Pt=[0,0,0],Nt=0,ii=function(){return Nt=Pn()},Ie=function(S,L){return(w.event=S)&&a&&cc(S.target,a)||L&&Xe&&S.pointerType!=="touch"||B&&B(S,L)},zr=function(){w._vx.reset(),w._vy.reset(),De.pause(),h&&h(w)},Zt=function(){var S=w.deltaX=Ia(yt),L=w.deltaY=Ia(Pt),C=Math.abs(S)>=i,z=Math.abs(L)>=i;I&&(C||z)&&I(w,S,L,yt,Pt),C&&(b&&w.deltaX>0&&b(w),E&&w.deltaX<0&&E(w),M&&M(w),N&&w.deltaX<0!=ke<0&&N(w),ke=w.deltaX,yt[0]=yt[1]=yt[2]=0),z&&(v&&w.deltaY>0&&v(w),T&&w.deltaY<0&&T(w),R&&R(w),Y&&w.deltaY<0!=Ft<0&&Y(w),Ft=w.deltaY,Pt[0]=Pt[1]=Pt[2]=0),(ie||Ne)&&(V&&V(w),Ne&&(y&&Ne===1&&y(w),D&&D(w),Ne=0),ie=!1),j&&!(j=!1)&&He&&He(w),ne&&(q(w),ne=!1),it=0},fr=function(S,L,C){yt[C]+=S,Pt[C]+=L,w._vx.update(S),w._vy.update(L),f?it||(it=requestAnimationFrame(Zt)):Zt()},Jt=function(S,L){de&&!ut&&(w.axis=ut=Math.abs(S)>Math.abs(L)?"x":"y",j=!0),ut!=="y"&&(yt[2]+=S,w._vx.update(S,!0)),ut!=="x"&&(Pt[2]+=L,w._vy.update(L,!0)),f?it||(it=requestAnimationFrame(Zt)):Zt()},gr=function(S){if(!Ie(S,1)){S=ln(S,c);var L=S.clientX,C=S.clientY,z=L-w.x,A=C-w.y,X=w.isDragging;w.x=L,w.y=C,(X||(z||A)&&(Math.abs(w.startX-L)>=n||Math.abs(w.startY-C)>=n))&&(Ne=X?2:1,X||(w.isDragging=!0),Jt(z,A))}},It=w.onPress=function(m){Ie(m,1)||m&&m.button||(w.axis=ut=null,De.pause(),w.isPressed=!0,m=ln(m),ke=Ft=0,w.startX=w.x=m.clientX,w.startY=w.y=m.clientY,w._vx.reset(),w._vy.reset(),vt(te?o:xe,cr[1],gr,Qt,!0),w.deltaX=w.deltaY=0,O&&O(w))},Q=w.onRelease=function(m){if(!Ie(m,1)){xt(te?o:xe,cr[1],gr,!0);var S=!isNaN(w.y-w.startY),L=w.isDragging,C=L&&(Math.abs(w.x-w.startX)>3||Math.abs(w.y-w.startY)>3),z=ln(m);!C&&S&&(w._vx.reset(),w._vy.reset(),c&&Ce&&at.delayedCall(.08,function(){if(Pn()-Nt>300&&!m.defaultPrevented){if(m.target.click)m.target.click();else if(xe.createEvent){var A=xe.createEvent("MouseEvents");A.initMouseEvent("click",!0,!0,Ht,1,z.screenX,z.screenY,z.clientX,z.clientY,!1,!1,!1,!1,0,null),m.target.dispatchEvent(A)}}})),w.isDragging=w.isGesturing=w.isPressed=!1,h&&L&&!te&&De.restart(!0),Ne&&Zt(),P&&L&&P(w),k&&k(w,C)}},zt=function(S){return S.touches&&S.touches.length>1&&(w.isGesturing=!0)&&we(S,w.isDragging)},Re=function(){return(w.isGesturing=!1)||x(w)},ft=function(S){if(!Ie(S)){var L=Me(),C=Oe();fr((L-ur)*Fe,(C-jt)*Fe,1),ur=L,jt=C,h&&De.restart(!0)}},kt=function(S){if(!Ie(S)){S=ln(S,c),q&&(ne=!0);var L=(S.deltaMode===1?u:S.deltaMode===2?Ht.innerHeight:1)*_;fr(S.deltaX*L,S.deltaY*L,0),h&&!te&&De.restart(!0)}},mr=function(S){if(!Ie(S)){var L=S.clientX,C=S.clientY,z=L-w.x,A=C-w.y;w.x=L,w.y=C,ie=!0,h&&De.restart(!0),(z||A)&&Jt(z,A)}},H=function(S){w.event=S,G(w)},g=function(S){w.event=S,K(w)},F=function(S){return Ie(S)||ln(S,c)&&J(w)};De=w._dc=at.delayedCall(d||.25,zr).pause(),w.deltaX=w.deltaY=0,w._vx=Eo(0,50,!0),w._vy=Eo(0,50,!0),w.scrollX=Me,w.scrollY=Oe,w.isDragging=w.isGesturing=w.isPressed=!1,pu(this),w.enable=function(m){return w.isEnabled||(vt(Ke?xe:o,"scroll",Do),s.indexOf("scroll")>=0&&vt(Ke?xe:o,"scroll",ft,Qt,ye),s.indexOf("wheel")>=0&&vt(o,"wheel",kt,Qt,ye),(s.indexOf("touch")>=0&&hu||s.indexOf("pointer")>=0)&&(vt(o,cr[0],It,Qt,ye),vt(xe,cr[2],Q),vt(xe,cr[3],Q),Ce&&vt(o,"click",ii,!0,!0),J&&vt(o,"click",F),we&&vt(xe,"gesturestart",zt),x&&vt(xe,"gestureend",Re),G&&vt(o,fi+"enter",H),K&&vt(o,fi+"leave",g),V&&vt(o,fi+"move",mr)),w.isEnabled=!0,w.isDragging=w.isGesturing=w.isPressed=ie=Ne=!1,w._vx.reset(),w._vy.reset(),ur=Me(),jt=Oe(),m&&m.type&&It(m),re&&re(w)),w},w.disable=function(){w.isEnabled&&(Yi.filter(function(m){return m!==w&&kn(m.target)}).length||xt(Ke?xe:o,"scroll",Do),w.isPressed&&(w._vx.reset(),w._vy.reset(),xt(te?o:xe,cr[1],gr,!0)),xt(Ke?xe:o,"scroll",ft,ye),xt(o,"wheel",kt,ye),xt(o,cr[0],It,ye),xt(xe,cr[2],Q),xt(xe,cr[3],Q),xt(o,"click",ii,!0),xt(o,"click",F),xt(xe,"gesturestart",zt),xt(xe,"gestureend",Re),xt(o,fi+"enter",H),xt(o,fi+"leave",g),xt(o,fi+"move",mr),w.isEnabled=w.isPressed=w.isDragging=!1,ge&&ge(w))},w.kill=w.revert=function(){w.disable();var m=Yi.indexOf(w);m>=0&&Yi.splice(m,1),Rr===w&&(Rr=0)},Yi.push(w),te&&kn(o)&&(Rr=w),w.enable(p)},uc(l,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),l}();We.version="3.13.0";We.create=function(l){return new We(l)};We.register=yu;We.getAll=function(){return Yi.slice()};We.getById=function(l){return Yi.filter(function(e){return e.vars.id===l})[0]};_u()&&at.registerPlugin(We);/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var $,Fi,oe,Pe,$t,me,la,Rs,Hn,Mn,dn,es,dt,Bs,Oo,bt,za,Ya,Ni,xu,Zs,vu,wt,Ro,wu,bu,$r,Ao,ua,Hi,fa,As,Lo,Js,ts=1,pt=Date.now,eo=pt(),lr=0,pn=0,Xa=function(e,r,t){var i=Xt(e)&&(e.substr(0,6)==="clamp("||e.indexOf("max")>-1);return t["_"+r+"Clamp"]=i,i?e.substr(6,e.length-7):e},Ba=function(e,r){return r&&(!Xt(e)||e.substr(0,6)!=="clamp(")?"clamp("+e+")":e},hc=function l(){return pn&&requestAnimationFrame(l)},$a=function(){return Bs=1},Wa=function(){return Bs=0},wr=function(e){return e},_n=function(e){return Math.round(e*1e5)/1e5||0},Tu=function(){return typeof window<"u"},Su=function(){return $||Tu()&&($=window.gsap)&&$.registerPlugin&&$},Ci=function(e){return!!~la.indexOf(e)},Cu=function(e){return(e==="Height"?fa:oe["inner"+e])||$t["client"+e]||me["client"+e]},Pu=function(e){return Zr(e,"getBoundingClientRect")||(Ci(e)?function(){return vs.width=oe.innerWidth,vs.height=fa,vs}:function(){return Or(e)})},dc=function(e,r,t){var i=t.d,n=t.d2,s=t.a;return(s=Zr(e,"getBoundingClientRect"))?function(){return s()[i]}:function(){return(r?Cu(n):e["client"+n])||0}},pc=function(e,r){return!r||~Cr.indexOf(e)?Pu(e):function(){return vs}},Sr=function(e,r){var t=r.s,i=r.d2,n=r.d,s=r.a;return Math.max(0,(t="scroll"+i)&&(s=Zr(e,t))?s()-Pu(e)()[n]:Ci(e)?($t[t]||me[t])-Cu(i):e[t]-e["offset"+i])},rs=function(e,r){for(var t=0;t<Ni.length;t+=3)(!r||~r.indexOf(Ni[t+1]))&&e(Ni[t],Ni[t+1],Ni[t+2])},Xt=function(e){return typeof e=="string"},gt=function(e){return typeof e=="function"},gn=function(e){return typeof e=="number"},ci=function(e){return typeof e=="object"},un=function(e,r,t){return e&&e.progress(r?0:1)&&t&&e.pause()},to=function(e,r){if(e.enabled){var t=e._ctx?e._ctx.add(function(){return r(e)}):r(e);t&&t.totalTime&&(e.callbackAnimation=t)}},Oi=Math.abs,ku="left",Mu="top",ca="right",ha="bottom",xi="width",vi="height",Dn="Right",En="Left",On="Top",Rn="Bottom",Ue="padding",ir="margin",tn="Width",da="Height",je="px",nr=function(e){return oe.getComputedStyle(e)},_c=function(e){var r=nr(e).position;e.style.position=r==="absolute"||r==="fixed"?r:"relative"},Ha=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},Or=function(e,r){var t=r&&nr(e)[Oo]!=="matrix(1, 0, 0, 1, 0, 0)"&&$.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=e.getBoundingClientRect();return t&&t.progress(0).kill(),i},Ls=function(e,r){var t=r.d2;return e["offset"+t]||e["client"+t]||0},Du=function(e){var r=[],t=e.labels,i=e.duration(),n;for(n in t)r.push(t[n]/i);return r},gc=function(e){return function(r){return $.utils.snap(Du(e),r)}},pa=function(e){var r=$.utils.snap(e),t=Array.isArray(e)&&e.slice(0).sort(function(i,n){return i-n});return t?function(i,n,s){s===void 0&&(s=.001);var o;if(!n)return r(i);if(n>0){for(i-=s,o=0;o<t.length;o++)if(t[o]>=i)return t[o];return t[o-1]}else for(o=t.length,i+=s;o--;)if(t[o]<=i)return t[o];return t[0]}:function(i,n,s){s===void 0&&(s=.001);var o=r(i);return!n||Math.abs(o-i)<s||o-i<0==n<0?o:r(n<0?i-e:i+e)}},mc=function(e){return function(r,t){return pa(Du(e))(r,t.direction)}},is=function(e,r,t,i){return t.split(",").forEach(function(n){return e(r,n,i)})},tt=function(e,r,t,i,n){return e.addEventListener(r,t,{passive:!i,capture:!!n})},et=function(e,r,t,i){return e.removeEventListener(r,t,!!i)},ns=function(e,r,t){t=t&&t.wheelHandler,t&&(e(r,"wheel",t),e(r,"touchmove",t))},Va={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},ss={toggleActions:"play",anticipatePin:0},Fs={top:0,left:0,center:.5,bottom:1,right:1},gs=function(e,r){if(Xt(e)){var t=e.indexOf("="),i=~t?+(e.charAt(t-1)+1)*parseFloat(e.substr(t+1)):0;~t&&(e.indexOf("%")>t&&(i*=r/100),e=e.substr(0,t-1)),e=i+(e in Fs?Fs[e]*r:~e.indexOf("%")?parseFloat(e)*r/100:parseFloat(e)||0)}return e},os=function(e,r,t,i,n,s,o,u){var f=n.startColor,c=n.endColor,h=n.fontSize,d=n.indent,a=n.fontWeight,_=Pe.createElement("div"),p=Ci(t)||Zr(t,"pinType")==="fixed",y=e.indexOf("scroller")!==-1,P=p?me:t,D=e.indexOf("start")!==-1,O=D?f:c,k="border-color:"+O+";font-size:"+h+";color:"+O+";font-weight:"+a+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return k+="position:"+((y||u)&&p?"fixed;":"absolute;"),(y||u||!p)&&(k+=(i===Je?ca:ha)+":"+(s+parseFloat(d))+"px;"),o&&(k+="box-sizing:border-box;text-align:left;width:"+o.offsetWidth+"px;"),_._isStart=D,_.setAttribute("class","gsap-marker-"+e+(r?" marker-"+r:"")),_.style.cssText=k,_.innerText=r||r===0?e+"-"+r:e,P.children[0]?P.insertBefore(_,P.children[0]):P.appendChild(_),_._offset=_["offset"+i.op.d2],ms(_,0,i,D),_},ms=function(e,r,t,i){var n={display:"block"},s=t[i?"os2":"p2"],o=t[i?"p2":"os2"];e._isFlipped=i,n[t.a+"Percent"]=i?-100:0,n[t.a]=i?"1px":0,n["border"+s+tn]=1,n["border"+o+tn]=0,n[t.p]=r+"px",$.set(e,n)},se=[],Fo={},Vn,Ua=function(){return pt()-lr>34&&(Vn||(Vn=requestAnimationFrame(Fr)))},Ri=function(){(!wt||!wt.isPressed||wt.startX>me.clientWidth)&&(ae.cache++,wt?Vn||(Vn=requestAnimationFrame(Fr)):Fr(),lr||ki("scrollStart"),lr=pt())},ro=function(){bu=oe.innerWidth,wu=oe.innerHeight},mn=function(e){ae.cache++,(e===!0||!dt&&!vu&&!Pe.fullscreenElement&&!Pe.webkitFullscreenElement&&(!Ro||bu!==oe.innerWidth||Math.abs(oe.innerHeight-wu)>oe.innerHeight*.25))&&Rs.restart(!0)},Pi={},yc=[],Eu=function l(){return et(ue,"scrollEnd",l)||pi(!0)},ki=function(e){return Pi[e]&&Pi[e].map(function(r){return r()})||yc},Yt=[],Ou=function(e){for(var r=0;r<Yt.length;r+=5)(!e||Yt[r+4]&&Yt[r+4].query===e)&&(Yt[r].style.cssText=Yt[r+1],Yt[r].getBBox&&Yt[r].setAttribute("transform",Yt[r+2]||""),Yt[r+3].uncache=1)},_a=function(e,r){var t;for(bt=0;bt<se.length;bt++)t=se[bt],t&&(!r||t._ctx===r)&&(e?t.kill(1):t.revert(!0,!0));As=!0,r&&Ou(r),r||ki("revert")},Ru=function(e,r){ae.cache++,(r||!Tt)&&ae.forEach(function(t){return gt(t)&&t.cacheID++&&(t.rec=0)}),Xt(e)&&(oe.history.scrollRestoration=ua=e)},Tt,wi=0,Ga,xc=function(){if(Ga!==wi){var e=Ga=wi;requestAnimationFrame(function(){return e===wi&&pi(!0)})}},Au=function(){me.appendChild(Hi),fa=!wt&&Hi.offsetHeight||oe.innerHeight,me.removeChild(Hi)},qa=function(e){return Hn(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(r){return r.style.display=e?"none":"block"})},pi=function(e,r){if($t=Pe.documentElement,me=Pe.body,la=[oe,Pe,$t,me],lr&&!e&&!As){tt(ue,"scrollEnd",Eu);return}Au(),Tt=ue.isRefreshing=!0,ae.forEach(function(i){return gt(i)&&++i.cacheID&&(i.rec=i())});var t=ki("refreshInit");xu&&ue.sort(),r||_a(),ae.forEach(function(i){gt(i)&&(i.smooth&&(i.target.style.scrollBehavior="auto"),i(0))}),se.slice(0).forEach(function(i){return i.refresh()}),As=!1,se.forEach(function(i){if(i._subPinOffset&&i.pin){var n=i.vars.horizontal?"offsetWidth":"offsetHeight",s=i.pin[n];i.revert(!0,1),i.adjustPinSpacing(i.pin[n]-s),i.refresh()}}),Lo=1,qa(!0),se.forEach(function(i){var n=Sr(i.scroller,i._dir),s=i.vars.end==="max"||i._endClamp&&i.end>n,o=i._startClamp&&i.start>=n;(s||o)&&i.setPositions(o?n-1:i.start,s?Math.max(o?n:i.start+1,n):i.end,!0)}),qa(!1),Lo=0,t.forEach(function(i){return i&&i.render&&i.render(-1)}),ae.forEach(function(i){gt(i)&&(i.smooth&&requestAnimationFrame(function(){return i.target.style.scrollBehavior="smooth"}),i.rec&&i(i.rec))}),Ru(ua,1),Rs.pause(),wi++,Tt=2,Fr(2),se.forEach(function(i){return gt(i.vars.onRefresh)&&i.vars.onRefresh(i)}),Tt=ue.isRefreshing=!1,ki("refresh")},No=0,ys=1,An,Fr=function(e){if(e===2||!Tt&&!As){ue.isUpdating=!0,An&&An.update(0);var r=se.length,t=pt(),i=t-eo>=50,n=r&&se[0].scroll();if(ys=No>n?-1:1,Tt||(No=n),i&&(lr&&!Bs&&t-lr>200&&(lr=0,ki("scrollEnd")),dn=eo,eo=t),ys<0){for(bt=r;bt-- >0;)se[bt]&&se[bt].update(0,i);ys=1}else for(bt=0;bt<r;bt++)se[bt]&&se[bt].update(0,i);ue.isUpdating=!1}Vn=0},Io=[ku,Mu,ha,ca,ir+Rn,ir+Dn,ir+On,ir+En,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],xs=Io.concat([xi,vi,"boxSizing","max"+tn,"max"+da,"position",ir,Ue,Ue+On,Ue+Dn,Ue+Rn,Ue+En]),vc=function(e,r,t){Vi(t);var i=e._gsap;if(i.spacerIsNative)Vi(i.spacerState);else if(e._gsap.swappedIn){var n=r.parentNode;n&&(n.insertBefore(e,r),n.removeChild(r))}e._gsap.swappedIn=!1},io=function(e,r,t,i){if(!e._gsap.swappedIn){for(var n=Io.length,s=r.style,o=e.style,u;n--;)u=Io[n],s[u]=t[u];s.position=t.position==="absolute"?"absolute":"relative",t.display==="inline"&&(s.display="inline-block"),o[ha]=o[ca]="auto",s.flexBasis=t.flexBasis||"auto",s.overflow="visible",s.boxSizing="border-box",s[xi]=Ls(e,Ct)+je,s[vi]=Ls(e,Je)+je,s[Ue]=o[ir]=o[Mu]=o[ku]="0",Vi(i),o[xi]=o["max"+tn]=t[xi],o[vi]=o["max"+da]=t[vi],o[Ue]=t[Ue],e.parentNode!==r&&(e.parentNode.insertBefore(r,e),r.appendChild(e)),e._gsap.swappedIn=!0}},wc=/([A-Z])/g,Vi=function(e){if(e){var r=e.t.style,t=e.length,i=0,n,s;for((e.t._gsap||$.core.getCache(e.t)).uncache=1;i<t;i+=2)s=e[i+1],n=e[i],s?r[n]=s:r[n]&&r.removeProperty(n.replace(wc,"-$1").toLowerCase())}},as=function(e){for(var r=xs.length,t=e.style,i=[],n=0;n<r;n++)i.push(xs[n],t[xs[n]]);return i.t=e,i},bc=function(e,r,t){for(var i=[],n=e.length,s=t?8:0,o;s<n;s+=2)o=e[s],i.push(o,o in r?r[o]:e[s+1]);return i.t=e.t,i},vs={left:0,top:0},Ka=function(e,r,t,i,n,s,o,u,f,c,h,d,a,_){gt(e)&&(e=e(u)),Xt(e)&&e.substr(0,3)==="max"&&(e=d+(e.charAt(4)==="="?gs("0"+e.substr(3),t):0));var p=a?a.time():0,y,P,D;if(a&&a.seek(0),isNaN(e)||(e=+e),gn(e))a&&(e=$.utils.mapRange(a.scrollTrigger.start,a.scrollTrigger.end,0,d,e)),o&&ms(o,t,i,!0);else{gt(r)&&(r=r(u));var O=(e||"0").split(" "),k,b,E,T;D=Dt(r,u)||me,k=Or(D)||{},(!k||!k.left&&!k.top)&&nr(D).display==="none"&&(T=D.style.display,D.style.display="block",k=Or(D),T?D.style.display=T:D.style.removeProperty("display")),b=gs(O[0],k[i.d]),E=gs(O[1]||"0",t),e=k[i.p]-f[i.p]-c+b+n-E,o&&ms(o,E,i,t-E<20||o._isStart&&E>20),t-=t-E}if(_&&(u[_]=e||-.001,e<0&&(e=0)),s){var v=e+t,M=s._isStart;y="scroll"+i.d2,ms(s,v,i,M&&v>20||!M&&(h?Math.max(me[y],$t[y]):s.parentNode[y])<=v+1),h&&(f=Or(o),h&&(s.style[i.op.p]=f[i.op.p]-i.op.m-s._offset+je))}return a&&D&&(y=Or(D),a.seek(d),P=Or(D),a._caScrollDist=y[i.p]-P[i.p],e=e/a._caScrollDist*d),a&&a.seek(p),a?e:Math.round(e)},Tc=/(webkit|moz|length|cssText|inset)/i,Qa=function(e,r,t,i){if(e.parentNode!==r){var n=e.style,s,o;if(r===me){e._stOrig=n.cssText,o=nr(e);for(s in o)!+s&&!Tc.test(s)&&o[s]&&typeof n[s]=="string"&&s!=="0"&&(n[s]=o[s]);n.top=t,n.left=i}else n.cssText=e._stOrig;$.core.getCache(e).uncache=1,r.appendChild(e)}},Lu=function(e,r,t){var i=r,n=i;return function(s){var o=Math.round(e());return o!==i&&o!==n&&Math.abs(o-i)>3&&Math.abs(o-n)>3&&(s=o,t&&t()),n=i,i=Math.round(s),i}},ls=function(e,r,t){var i={};i[r.p]="+="+t,$.set(e,i)},ja=function(e,r){var t=ti(e,r),i="_scroll"+r.p2,n=function s(o,u,f,c,h){var d=s.tween,a=u.onComplete,_={};f=f||t();var p=Lu(t,f,function(){d.kill(),s.tween=0});return h=c&&h||0,c=c||o-f,d&&d.kill(),u[i]=o,u.inherit=!1,u.modifiers=_,_[i]=function(){return p(f+c*d.ratio+h*d.ratio*d.ratio)},u.onUpdate=function(){ae.cache++,s.tween&&Fr()},u.onComplete=function(){s.tween=0,a&&a.call(d)},d=s.tween=$.to(e,u),d};return e[i]=t,t.wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},tt(e,"wheel",t.wheelHandler),ue.isTouch&&tt(e,"touchmove",t.wheelHandler),n},ue=function(){function l(r,t){Fi||l.register($)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),Ao(this),this.init(r,t)}var e=l.prototype;return e.init=function(t,i){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!pn){this.update=this.refresh=this.kill=wr;return}t=Ha(Xt(t)||gn(t)||t.nodeType?{trigger:t}:t,ss);var n=t,s=n.onUpdate,o=n.toggleClass,u=n.id,f=n.onToggle,c=n.onRefresh,h=n.scrub,d=n.trigger,a=n.pin,_=n.pinSpacing,p=n.invalidateOnRefresh,y=n.anticipatePin,P=n.onScrubComplete,D=n.onSnapComplete,O=n.once,k=n.snap,b=n.pinReparent,E=n.pinSpacer,T=n.containerAnimation,v=n.fastScrollEnd,M=n.preventOverlaps,R=t.horizontal||t.containerAnimation&&t.horizontal!==!1?Ct:Je,I=!h&&h!==0,N=Dt(t.scroller||oe),Y=$.core.getCache(N),G=Ci(N),K=("pinType"in t?t.pinType:Zr(N,"pinType")||G&&"fixed")==="fixed",V=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],B=I&&t.toggleActions.split(" "),te="markers"in t?t.markers:ss.markers,we=G?0:parseFloat(nr(N)["border"+R.p2+tn])||0,x=this,q=t.onRefreshInit&&function(){return t.onRefreshInit(x)},re=dc(N,G,R),ge=pc(N,G),J=0,Fe=0,ye=0,Ce=ti(N,R),de,He,it,De,Ne,ie,ne,j,ut,w,ke,Ft,Qt,Me,Oe,ur,jt,Xe,Ke,xe,yt,Pt,Nt,ii,Ie,zr,Zt,fr,Jt,gr,It,Q,zt,Re,ft,kt,mr,H,g;if(x._startClamp=x._endClamp=!1,x._dir=R,y*=45,x.scroller=N,x.scroll=T?T.time.bind(T):Ce,De=Ce(),x.vars=t,i=i||t.animation,"refreshPriority"in t&&(xu=1,t.refreshPriority===-9999&&(An=x)),Y.tweenScroll=Y.tweenScroll||{top:ja(N,Je),left:ja(N,Ct)},x.tweenTo=de=Y.tweenScroll[R.p],x.scrubDuration=function(C){zt=gn(C)&&C,zt?Q?Q.duration(C):Q=$.to(i,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:zt,paused:!0,onComplete:function(){return P&&P(x)}}):(Q&&Q.progress(1).kill(),Q=0)},i&&(i.vars.lazy=!1,i._initted&&!x.isReverted||i.vars.immediateRender!==!1&&t.immediateRender!==!1&&i.duration()&&i.render(0,!0,!0),x.animation=i.pause(),i.scrollTrigger=x,x.scrubDuration(h),gr=0,u||(u=i.vars.id)),k&&((!ci(k)||k.push)&&(k={snapTo:k}),"scrollBehavior"in me.style&&$.set(G?[me,$t]:N,{scrollBehavior:"auto"}),ae.forEach(function(C){return gt(C)&&C.target===(G?Pe.scrollingElement||$t:N)&&(C.smooth=!1)}),it=gt(k.snapTo)?k.snapTo:k.snapTo==="labels"?gc(i):k.snapTo==="labelsDirectional"?mc(i):k.directional!==!1?function(C,z){return pa(k.snapTo)(C,pt()-Fe<500?0:z.direction)}:$.utils.snap(k.snapTo),Re=k.duration||{min:.1,max:2},Re=ci(Re)?Mn(Re.min,Re.max):Mn(Re,Re),ft=$.delayedCall(k.delay||zt/2||.1,function(){var C=Ce(),z=pt()-Fe<500,A=de.tween;if((z||Math.abs(x.getVelocity())<10)&&!A&&!Bs&&J!==C){var X=(C-ie)/Me,U=i&&!I?i.totalProgress():X,W=z?0:(U-It)/(pt()-dn)*1e3||0,ee=$.utils.clamp(-X,1-X,Oi(W/2)*W/.185),pe=X+(k.inertia===!1?0:ee),_e,le,fe=k,ve=fe.onStart,Z=fe.onInterrupt,nt=fe.onComplete;if(_e=it(pe,x),gn(_e)||(_e=pe),le=Math.max(0,Math.round(ie+_e*Me)),C<=ne&&C>=ie&&le!==C){if(A&&!A._initted&&A.data<=Oi(le-C))return;k.inertia===!1&&(ee=_e-X),de(le,{duration:Re(Oi(Math.max(Oi(pe-U),Oi(_e-U))*.185/W/.05||0)),ease:k.ease||"power3",data:Oi(le-C),onInterrupt:function(){return ft.restart(!0)&&Z&&Z(x)},onComplete:function(){x.update(),J=Ce(),i&&!I&&(Q?Q.resetTo("totalProgress",_e,i._tTime/i._tDur):i.progress(_e)),gr=It=i&&!I?i.totalProgress():x.progress,D&&D(x),nt&&nt(x)}},C,ee*Me,le-C-ee*Me),ve&&ve(x,de.tween)}}else x.isActive&&J!==C&&ft.restart(!0)}).pause()),u&&(Fo[u]=x),d=x.trigger=Dt(d||a!==!0&&a),g=d&&d._gsap&&d._gsap.stRevert,g&&(g=g(x)),a=a===!0?d:Dt(a),Xt(o)&&(o={targets:d,className:o}),a&&(_===!1||_===ir||(_=!_&&a.parentNode&&a.parentNode.style&&nr(a.parentNode).display==="flex"?!1:Ue),x.pin=a,He=$.core.getCache(a),He.spacer?Oe=He.pinState:(E&&(E=Dt(E),E&&!E.nodeType&&(E=E.current||E.nativeElement),He.spacerIsNative=!!E,E&&(He.spacerState=as(E))),He.spacer=Xe=E||Pe.createElement("div"),Xe.classList.add("pin-spacer"),u&&Xe.classList.add("pin-spacer-"+u),He.pinState=Oe=as(a)),t.force3D!==!1&&$.set(a,{force3D:!0}),x.spacer=Xe=He.spacer,Jt=nr(a),ii=Jt[_+R.os2],xe=$.getProperty(a),yt=$.quickSetter(a,R.a,je),io(a,Xe,Jt),jt=as(a)),te){Ft=ci(te)?Ha(te,Va):Va,w=os("scroller-start",u,N,R,Ft,0),ke=os("scroller-end",u,N,R,Ft,0,w),Ke=w["offset"+R.op.d2];var F=Dt(Zr(N,"content")||N);j=this.markerStart=os("start",u,F,R,Ft,Ke,0,T),ut=this.markerEnd=os("end",u,F,R,Ft,Ke,0,T),T&&(H=$.quickSetter([j,ut],R.a,je)),!K&&!(Cr.length&&Zr(N,"fixedMarkers")===!0)&&(_c(G?me:N),$.set([w,ke],{force3D:!0}),zr=$.quickSetter(w,R.a,je),fr=$.quickSetter(ke,R.a,je))}if(T){var m=T.vars.onUpdate,S=T.vars.onUpdateParams;T.eventCallback("onUpdate",function(){x.update(0,0,1),m&&m.apply(T,S||[])})}if(x.previous=function(){return se[se.indexOf(x)-1]},x.next=function(){return se[se.indexOf(x)+1]},x.revert=function(C,z){if(!z)return x.kill(!0);var A=C!==!1||!x.enabled,X=dt;A!==x.isReverted&&(A&&(kt=Math.max(Ce(),x.scroll.rec||0),ye=x.progress,mr=i&&i.progress()),j&&[j,ut,w,ke].forEach(function(U){return U.style.display=A?"none":"block"}),A&&(dt=x,x.update(A)),a&&(!b||!x.isActive)&&(A?vc(a,Xe,Oe):io(a,Xe,nr(a),Ie)),A||x.update(A),dt=X,x.isReverted=A)},x.refresh=function(C,z,A,X){if(!((dt||!x.enabled)&&!z)){if(a&&C&&lr){tt(l,"scrollEnd",Eu);return}!Tt&&q&&q(x),dt=x,de.tween&&!A&&(de.tween.kill(),de.tween=0),Q&&Q.pause(),p&&i&&(i.revert({kill:!1}).invalidate(),i.getChildren&&i.getChildren(!0,!0,!1).forEach(function(Yr){return Yr.vars.immediateRender&&Yr.render(0,!0,!0)})),x.isReverted||x.revert(!0,!0),x._subPinOffset=!1;var U=re(),W=ge(),ee=T?T.duration():Sr(N,R),pe=Me<=.01||!Me,_e=0,le=X||0,fe=ci(A)?A.end:t.end,ve=t.endTrigger||d,Z=ci(A)?A.start:t.start||(t.start===0||!d?0:a?"0 0":"0 100%"),nt=x.pinnedContainer=t.pinnedContainer&&Dt(t.pinnedContainer,x),ze=d&&Math.max(0,se.indexOf(x))||0,st=ze,ot,ct,ni,Kn,ht,Qe,yr,Ws,xa,sn,xr,on,Qn;for(te&&ci(A)&&(on=$.getProperty(w,R.p),Qn=$.getProperty(ke,R.p));st-- >0;)Qe=se[st],Qe.end||Qe.refresh(0,1)||(dt=x),yr=Qe.pin,yr&&(yr===d||yr===a||yr===nt)&&!Qe.isReverted&&(sn||(sn=[]),sn.unshift(Qe),Qe.revert(!0,!0)),Qe!==se[st]&&(ze--,st--);for(gt(Z)&&(Z=Z(x)),Z=Xa(Z,"start",x),ie=Ka(Z,d,U,R,Ce(),j,w,x,W,we,K,ee,T,x._startClamp&&"_startClamp")||(a?-.001:0),gt(fe)&&(fe=fe(x)),Xt(fe)&&!fe.indexOf("+=")&&(~fe.indexOf(" ")?fe=(Xt(Z)?Z.split(" ")[0]:"")+fe:(_e=gs(fe.substr(2),U),fe=Xt(Z)?Z:(T?$.utils.mapRange(0,T.duration(),T.scrollTrigger.start,T.scrollTrigger.end,ie):ie)+_e,ve=d)),fe=Xa(fe,"end",x),ne=Math.max(ie,Ka(fe||(ve?"100% 0":ee),ve,U,R,Ce()+_e,ut,ke,x,W,we,K,ee,T,x._endClamp&&"_endClamp"))||-.001,_e=0,st=ze;st--;)Qe=se[st],yr=Qe.pin,yr&&Qe.start-Qe._pinPush<=ie&&!T&&Qe.end>0&&(ot=Qe.end-(x._startClamp?Math.max(0,Qe.start):Qe.start),(yr===d&&Qe.start-Qe._pinPush<ie||yr===nt)&&isNaN(Z)&&(_e+=ot*(1-Qe.progress)),yr===a&&(le+=ot));if(ie+=_e,ne+=_e,x._startClamp&&(x._startClamp+=_e),x._endClamp&&!Tt&&(x._endClamp=ne||-.001,ne=Math.min(ne,Sr(N,R))),Me=ne-ie||(ie-=.01)&&.001,pe&&(ye=$.utils.clamp(0,1,$.utils.normalize(ie,ne,kt))),x._pinPush=le,j&&_e&&(ot={},ot[R.a]="+="+_e,nt&&(ot[R.p]="-="+Ce()),$.set([j,ut],ot)),a&&!(Lo&&x.end>=Sr(N,R)))ot=nr(a),Kn=R===Je,ni=Ce(),Pt=parseFloat(xe(R.a))+le,!ee&&ne>1&&(xr=(G?Pe.scrollingElement||$t:N).style,xr={style:xr,value:xr["overflow"+R.a.toUpperCase()]},G&&nr(me)["overflow"+R.a.toUpperCase()]!=="scroll"&&(xr.style["overflow"+R.a.toUpperCase()]="scroll")),io(a,Xe,ot),jt=as(a),ct=Or(a,!0),Ws=K&&ti(N,Kn?Ct:Je)(),_?(Ie=[_+R.os2,Me+le+je],Ie.t=Xe,st=_===Ue?Ls(a,R)+Me+le:0,st&&(Ie.push(R.d,st+je),Xe.style.flexBasis!=="auto"&&(Xe.style.flexBasis=st+je)),Vi(Ie),nt&&se.forEach(function(Yr){Yr.pin===nt&&Yr.vars.pinSpacing!==!1&&(Yr._subPinOffset=!0)}),K&&Ce(kt)):(st=Ls(a,R),st&&Xe.style.flexBasis!=="auto"&&(Xe.style.flexBasis=st+je)),K&&(ht={top:ct.top+(Kn?ni-ie:Ws)+je,left:ct.left+(Kn?Ws:ni-ie)+je,boxSizing:"border-box",position:"fixed"},ht[xi]=ht["max"+tn]=Math.ceil(ct.width)+je,ht[vi]=ht["max"+da]=Math.ceil(ct.height)+je,ht[ir]=ht[ir+On]=ht[ir+Dn]=ht[ir+Rn]=ht[ir+En]="0",ht[Ue]=ot[Ue],ht[Ue+On]=ot[Ue+On],ht[Ue+Dn]=ot[Ue+Dn],ht[Ue+Rn]=ot[Ue+Rn],ht[Ue+En]=ot[Ue+En],ur=bc(Oe,ht,b),Tt&&Ce(0)),i?(xa=i._initted,Zs(1),i.render(i.duration(),!0,!0),Nt=xe(R.a)-Pt+Me+le,Zt=Math.abs(Me-Nt)>1,K&&Zt&&ur.splice(ur.length-2,2),i.render(0,!0,!0),xa||i.invalidate(!0),i.parent||i.totalTime(i.totalTime()),Zs(0)):Nt=Me,xr&&(xr.value?xr.style["overflow"+R.a.toUpperCase()]=xr.value:xr.style.removeProperty("overflow-"+R.a));else if(d&&Ce()&&!T)for(ct=d.parentNode;ct&&ct!==me;)ct._pinOffset&&(ie-=ct._pinOffset,ne-=ct._pinOffset),ct=ct.parentNode;sn&&sn.forEach(function(Yr){return Yr.revert(!1,!0)}),x.start=ie,x.end=ne,De=Ne=Tt?kt:Ce(),!T&&!Tt&&(De<kt&&Ce(kt),x.scroll.rec=0),x.revert(!1,!0),Fe=pt(),ft&&(J=-1,ft.restart(!0)),dt=0,i&&I&&(i._initted||mr)&&i.progress()!==mr&&i.progress(mr||0,!0).render(i.time(),!0,!0),(pe||ye!==x.progress||T||p||i&&!i._initted)&&(i&&!I&&(i._initted||ye||i.vars.immediateRender!==!1)&&i.totalProgress(T&&ie<-.001&&!ye?$.utils.normalize(ie,ne,0):ye,!0),x.progress=pe||(De-ie)/Me===ye?0:ye),a&&_&&(Xe._pinOffset=Math.round(x.progress*Nt)),Q&&Q.invalidate(),isNaN(on)||(on-=$.getProperty(w,R.p),Qn-=$.getProperty(ke,R.p),ls(w,R,on),ls(j,R,on-(X||0)),ls(ke,R,Qn),ls(ut,R,Qn-(X||0))),pe&&!Tt&&x.update(),c&&!Tt&&!Qt&&(Qt=!0,c(x),Qt=!1)}},x.getVelocity=function(){return(Ce()-Ne)/(pt()-dn)*1e3||0},x.endAnimation=function(){un(x.callbackAnimation),i&&(Q?Q.progress(1):i.paused()?I||un(i,x.direction<0,1):un(i,i.reversed()))},x.labelToScroll=function(C){return i&&i.labels&&(ie||x.refresh()||ie)+i.labels[C]/i.duration()*Me||0},x.getTrailing=function(C){var z=se.indexOf(x),A=x.direction>0?se.slice(0,z).reverse():se.slice(z+1);return(Xt(C)?A.filter(function(X){return X.vars.preventOverlaps===C}):A).filter(function(X){return x.direction>0?X.end<=ie:X.start>=ne})},x.update=function(C,z,A){if(!(T&&!A&&!C)){var X=Tt===!0?kt:x.scroll(),U=C?0:(X-ie)/Me,W=U<0?0:U>1?1:U||0,ee=x.progress,pe,_e,le,fe,ve,Z,nt,ze;if(z&&(Ne=De,De=T?Ce():X,k&&(It=gr,gr=i&&!I?i.totalProgress():W)),y&&a&&!dt&&!ts&&lr&&(!W&&ie<X+(X-Ne)/(pt()-dn)*y?W=1e-4:W===1&&ne>X+(X-Ne)/(pt()-dn)*y&&(W=.9999)),W!==ee&&x.enabled){if(pe=x.isActive=!!W&&W<1,_e=!!ee&&ee<1,Z=pe!==_e,ve=Z||!!W!=!!ee,x.direction=W>ee?1:-1,x.progress=W,ve&&!dt&&(le=W&&!ee?0:W===1?1:ee===1?2:3,I&&(fe=!Z&&B[le+1]!=="none"&&B[le+1]||B[le],ze=i&&(fe==="complete"||fe==="reset"||fe in i))),M&&(Z||ze)&&(ze||h||!i)&&(gt(M)?M(x):x.getTrailing(M).forEach(function(ni){return ni.endAnimation()})),I||(Q&&!dt&&!ts?(Q._dp._time-Q._start!==Q._time&&Q.render(Q._dp._time-Q._start),Q.resetTo?Q.resetTo("totalProgress",W,i._tTime/i._tDur):(Q.vars.totalProgress=W,Q.invalidate().restart())):i&&i.totalProgress(W,!!(dt&&(Fe||C)))),a){if(C&&_&&(Xe.style[_+R.os2]=ii),!K)yt(_n(Pt+Nt*W));else if(ve){if(nt=!C&&W>ee&&ne+1>X&&X+1>=Sr(N,R),b)if(!C&&(pe||nt)){var st=Or(a,!0),ot=X-ie;Qa(a,me,st.top+(R===Je?ot:0)+je,st.left+(R===Je?0:ot)+je)}else Qa(a,Xe);Vi(pe||nt?ur:jt),Zt&&W<1&&pe||yt(Pt+(W===1&&!nt?Nt:0))}}k&&!de.tween&&!dt&&!ts&&ft.restart(!0),o&&(Z||O&&W&&(W<1||!Js))&&Hn(o.targets).forEach(function(ni){return ni.classList[pe||O?"add":"remove"](o.className)}),s&&!I&&!C&&s(x),ve&&!dt?(I&&(ze&&(fe==="complete"?i.pause().totalProgress(1):fe==="reset"?i.restart(!0).pause():fe==="restart"?i.restart(!0):i[fe]()),s&&s(x)),(Z||!Js)&&(f&&Z&&to(x,f),V[le]&&to(x,V[le]),O&&(W===1?x.kill(!1,1):V[le]=0),Z||(le=W===1?1:3,V[le]&&to(x,V[le]))),v&&!pe&&Math.abs(x.getVelocity())>(gn(v)?v:2500)&&(un(x.callbackAnimation),Q?Q.progress(1):un(i,fe==="reverse"?1:!W,1))):I&&s&&!dt&&s(x)}if(fr){var ct=T?X/T.duration()*(T._caScrollDist||0):X;zr(ct+(w._isFlipped?1:0)),fr(ct)}H&&H(-X/T.duration()*(T._caScrollDist||0))}},x.enable=function(C,z){x.enabled||(x.enabled=!0,tt(N,"resize",mn),G||tt(N,"scroll",Ri),q&&tt(l,"refreshInit",q),C!==!1&&(x.progress=ye=0,De=Ne=J=Ce()),z!==!1&&x.refresh())},x.getTween=function(C){return C&&de?de.tween:Q},x.setPositions=function(C,z,A,X){if(T){var U=T.scrollTrigger,W=T.duration(),ee=U.end-U.start;C=U.start+ee*C/W,z=U.start+ee*z/W}x.refresh(!1,!1,{start:Ba(C,A&&!!x._startClamp),end:Ba(z,A&&!!x._endClamp)},X),x.update()},x.adjustPinSpacing=function(C){if(Ie&&C){var z=Ie.indexOf(R.d)+1;Ie[z]=parseFloat(Ie[z])+C+je,Ie[1]=parseFloat(Ie[1])+C+je,Vi(Ie)}},x.disable=function(C,z){if(x.enabled&&(C!==!1&&x.revert(!0,!0),x.enabled=x.isActive=!1,z||Q&&Q.pause(),kt=0,He&&(He.uncache=1),q&&et(l,"refreshInit",q),ft&&(ft.pause(),de.tween&&de.tween.kill()&&(de.tween=0)),!G)){for(var A=se.length;A--;)if(se[A].scroller===N&&se[A]!==x)return;et(N,"resize",mn),G||et(N,"scroll",Ri)}},x.kill=function(C,z){x.disable(C,z),Q&&!z&&Q.kill(),u&&delete Fo[u];var A=se.indexOf(x);A>=0&&se.splice(A,1),A===bt&&ys>0&&bt--,A=0,se.forEach(function(X){return X.scroller===x.scroller&&(A=1)}),A||Tt||(x.scroll.rec=0),i&&(i.scrollTrigger=null,C&&i.revert({kill:!1}),z||i.kill()),j&&[j,ut,w,ke].forEach(function(X){return X.parentNode&&X.parentNode.removeChild(X)}),An===x&&(An=0),a&&(He&&(He.uncache=1),A=0,se.forEach(function(X){return X.pin===a&&A++}),A||(He.spacer=0)),t.onKill&&t.onKill(x)},se.push(x),x.enable(!1,!1),g&&g(x),i&&i.add&&!Me){var L=x.update;x.update=function(){x.update=L,ae.cache++,ie||ne||x.refresh()},$.delayedCall(.01,x.update),Me=.01,ie=ne=0}else x.refresh();a&&xc()},l.register=function(t){return Fi||($=t||Su(),Tu()&&window.document&&l.enable(),Fi=pn),Fi},l.defaults=function(t){if(t)for(var i in t)ss[i]=t[i];return ss},l.disable=function(t,i){pn=0,se.forEach(function(s){return s[i?"kill":"disable"](t)}),et(oe,"wheel",Ri),et(Pe,"scroll",Ri),clearInterval(es),et(Pe,"touchcancel",wr),et(me,"touchstart",wr),is(et,Pe,"pointerdown,touchstart,mousedown",$a),is(et,Pe,"pointerup,touchend,mouseup",Wa),Rs.kill(),rs(et);for(var n=0;n<ae.length;n+=3)ns(et,ae[n],ae[n+1]),ns(et,ae[n],ae[n+2])},l.enable=function(){if(oe=window,Pe=document,$t=Pe.documentElement,me=Pe.body,$&&(Hn=$.utils.toArray,Mn=$.utils.clamp,Ao=$.core.context||wr,Zs=$.core.suppressOverwrites||wr,ua=oe.history.scrollRestoration||"auto",No=oe.pageYOffset||0,$.core.globals("ScrollTrigger",l),me)){pn=1,Hi=document.createElement("div"),Hi.style.height="100vh",Hi.style.position="absolute",Au(),hc(),We.register($),l.isTouch=We.isTouch,$r=We.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),Ro=We.isTouch===1,tt(oe,"wheel",Ri),la=[oe,Pe,$t,me],$.matchMedia?(l.matchMedia=function(f){var c=$.matchMedia(),h;for(h in f)c.add(h,f[h]);return c},$.addEventListener("matchMediaInit",function(){return _a()}),$.addEventListener("matchMediaRevert",function(){return Ou()}),$.addEventListener("matchMedia",function(){pi(0,1),ki("matchMedia")}),$.matchMedia().add("(orientation: portrait)",function(){return ro(),ro})):console.warn("Requires GSAP 3.11.0 or later"),ro(),tt(Pe,"scroll",Ri);var t=me.hasAttribute("style"),i=me.style,n=i.borderTopStyle,s=$.core.Animation.prototype,o,u;for(s.revert||Object.defineProperty(s,"revert",{value:function(){return this.time(-.01,!0)}}),i.borderTopStyle="solid",o=Or(me),Je.m=Math.round(o.top+Je.sc())||0,Ct.m=Math.round(o.left+Ct.sc())||0,n?i.borderTopStyle=n:i.removeProperty("border-top-style"),t||(me.setAttribute("style",""),me.removeAttribute("style")),es=setInterval(Ua,250),$.delayedCall(.5,function(){return ts=0}),tt(Pe,"touchcancel",wr),tt(me,"touchstart",wr),is(tt,Pe,"pointerdown,touchstart,mousedown",$a),is(tt,Pe,"pointerup,touchend,mouseup",Wa),Oo=$.utils.checkPrefix("transform"),xs.push(Oo),Fi=pt(),Rs=$.delayedCall(.2,pi).pause(),Ni=[Pe,"visibilitychange",function(){var f=oe.innerWidth,c=oe.innerHeight;Pe.hidden?(za=f,Ya=c):(za!==f||Ya!==c)&&mn()},Pe,"DOMContentLoaded",pi,oe,"load",pi,oe,"resize",mn],rs(tt),se.forEach(function(f){return f.enable(0,1)}),u=0;u<ae.length;u+=3)ns(et,ae[u],ae[u+1]),ns(et,ae[u],ae[u+2])}},l.config=function(t){"limitCallbacks"in t&&(Js=!!t.limitCallbacks);var i=t.syncInterval;i&&clearInterval(es)||(es=i)&&setInterval(Ua,i),"ignoreMobileResize"in t&&(Ro=l.isTouch===1&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(rs(et)||rs(tt,t.autoRefreshEvents||"none"),vu=(t.autoRefreshEvents+"").indexOf("resize")===-1)},l.scrollerProxy=function(t,i){var n=Dt(t),s=ae.indexOf(n),o=Ci(n);~s&&ae.splice(s,o?6:2),i&&(o?Cr.unshift(oe,i,me,i,$t,i):Cr.unshift(n,i))},l.clearMatchMedia=function(t){se.forEach(function(i){return i._ctx&&i._ctx.query===t&&i._ctx.kill(!0,!0)})},l.isInViewport=function(t,i,n){var s=(Xt(t)?Dt(t):t).getBoundingClientRect(),o=s[n?xi:vi]*i||0;return n?s.right-o>0&&s.left+o<oe.innerWidth:s.bottom-o>0&&s.top+o<oe.innerHeight},l.positionInViewport=function(t,i,n){Xt(t)&&(t=Dt(t));var s=t.getBoundingClientRect(),o=s[n?xi:vi],u=i==null?o/2:i in Fs?Fs[i]*o:~i.indexOf("%")?parseFloat(i)*o/100:parseFloat(i)||0;return n?(s.left+u)/oe.innerWidth:(s.top+u)/oe.innerHeight},l.killAll=function(t){if(se.slice(0).forEach(function(n){return n.vars.id!=="ScrollSmoother"&&n.kill()}),t!==!0){var i=Pi.killAll||[];Pi={},i.forEach(function(n){return n()})}},l}();ue.version="3.13.0";ue.saveStyles=function(l){return l?Hn(l).forEach(function(e){if(e&&e.style){var r=Yt.indexOf(e);r>=0&&Yt.splice(r,5),Yt.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),$.core.getCache(e),Ao())}}):Yt};ue.revert=function(l,e){return _a(!l,e)};ue.create=function(l,e){return new ue(l,e)};ue.refresh=function(l){return l?mn(!0):(Fi||ue.register())&&pi(!0)};ue.update=function(l){return++ae.cache&&Fr(l===!0?2:0)};ue.clearScrollMemory=Ru;ue.maxScroll=function(l,e){return Sr(l,e?Ct:Je)};ue.getScrollFunc=function(l,e){return ti(Dt(l),e?Ct:Je)};ue.getById=function(l){return Fo[l]};ue.getAll=function(){return se.filter(function(l){return l.vars.id!=="ScrollSmoother"})};ue.isScrolling=function(){return!!lr};ue.snapDirectional=pa;ue.addEventListener=function(l,e){var r=Pi[l]||(Pi[l]=[]);~r.indexOf(e)||r.push(e)};ue.removeEventListener=function(l,e){var r=Pi[l],t=r&&r.indexOf(e);t>=0&&r.splice(t,1)};ue.batch=function(l,e){var r=[],t={},i=e.interval||.016,n=e.batchMax||1e9,s=function(f,c){var h=[],d=[],a=$.delayedCall(i,function(){c(h,d),h=[],d=[]}).pause();return function(_){h.length||a.restart(!0),h.push(_.trigger),d.push(_),n<=h.length&&a.progress(1)}},o;for(o in e)t[o]=o.substr(0,2)==="on"&&gt(e[o])&&o!=="onRefreshInit"?s(o,e[o]):e[o];return gt(n)&&(n=n(),tt(ue,"refresh",function(){return n=e.batchMax()})),Hn(l).forEach(function(u){var f={};for(o in t)f[o]=t[o];f.trigger=u,r.push(ue.create(f))}),r};var Za=function(e,r,t,i){return r>i?e(i):r<0&&e(0),t>i?(i-r)/(t-r):t<0?r/(r-t):1},no=function l(e,r){r===!0?e.style.removeProperty("touch-action"):e.style.touchAction=r===!0?"auto":r?"pan-"+r+(We.isTouch?" pinch-zoom":""):"none",e===$t&&l(me,r)},us={auto:1,scroll:1},Sc=function(e){var r=e.event,t=e.target,i=e.axis,n=(r.changedTouches?r.changedTouches[0]:r).target,s=n._gsap||$.core.getCache(n),o=pt(),u;if(!s._isScrollT||o-s._isScrollT>2e3){for(;n&&n!==me&&(n.scrollHeight<=n.clientHeight&&n.scrollWidth<=n.clientWidth||!(us[(u=nr(n)).overflowY]||us[u.overflowX]));)n=n.parentNode;s._isScroll=n&&n!==t&&!Ci(n)&&(us[(u=nr(n)).overflowY]||us[u.overflowX]),s._isScrollT=o}(s._isScroll||i==="x")&&(r.stopPropagation(),r._gsapAllow=!0)},Fu=function(e,r,t,i){return We.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:r,onWheel:i=i&&Sc,onPress:i,onDrag:i,onScroll:i,onEnable:function(){return t&&tt(Pe,We.eventTypes[0],el,!1,!0)},onDisable:function(){return et(Pe,We.eventTypes[0],el,!0)}})},Cc=/(input|label|select|textarea)/i,Ja,el=function(e){var r=Cc.test(e.target.tagName);(r||Ja)&&(e._gsapAllow=!0,Ja=r)},Pc=function(e){ci(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var r=e,t=r.normalizeScrollX,i=r.momentum,n=r.allowNestedScroll,s=r.onRelease,o,u,f=Dt(e.target)||$t,c=$.core.globals().ScrollSmoother,h=c&&c.get(),d=$r&&(e.content&&Dt(e.content)||h&&e.content!==!1&&!h.smooth()&&h.content()),a=ti(f,Je),_=ti(f,Ct),p=1,y=(We.isTouch&&oe.visualViewport?oe.visualViewport.scale*oe.visualViewport.width:oe.outerWidth)/oe.innerWidth,P=0,D=gt(i)?function(){return i(o)}:function(){return i||2.8},O,k,b=Fu(f,e.type,!0,n),E=function(){return k=!1},T=wr,v=wr,M=function(){u=Sr(f,Je),v=Mn($r?1:0,u),t&&(T=Mn(0,Sr(f,Ct))),O=wi},R=function(){d._gsap.y=_n(parseFloat(d._gsap.y)+a.offset)+"px",d.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(d._gsap.y)+", 0, 1)",a.offset=a.cacheID=0},I=function(){if(k){requestAnimationFrame(E);var te=_n(o.deltaY/2),we=v(a.v-te);if(d&&we!==a.v+a.offset){a.offset=we-a.v;var x=_n((parseFloat(d&&d._gsap.y)||0)-a.offset);d.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+x+", 0, 1)",d._gsap.y=x+"px",a.cacheID=ae.cache,Fr()}return!0}a.offset&&R(),k=!0},N,Y,G,K,V=function(){M(),N.isActive()&&N.vars.scrollY>u&&(a()>u?N.progress(1)&&a(u):N.resetTo("scrollY",u))};return d&&$.set(d,{y:"+=0"}),e.ignoreCheck=function(B){return $r&&B.type==="touchmove"&&I()||p>1.05&&B.type!=="touchstart"||o.isGesturing||B.touches&&B.touches.length>1},e.onPress=function(){k=!1;var B=p;p=_n((oe.visualViewport&&oe.visualViewport.scale||1)/y),N.pause(),B!==p&&no(f,p>1.01?!0:t?!1:"x"),Y=_(),G=a(),M(),O=wi},e.onRelease=e.onGestureStart=function(B,te){if(a.offset&&R(),!te)K.restart(!0);else{ae.cache++;var we=D(),x,q;t&&(x=_(),q=x+we*.05*-B.velocityX/.227,we*=Za(_,x,q,Sr(f,Ct)),N.vars.scrollX=T(q)),x=a(),q=x+we*.05*-B.velocityY/.227,we*=Za(a,x,q,Sr(f,Je)),N.vars.scrollY=v(q),N.invalidate().duration(we).play(.01),($r&&N.vars.scrollY>=u||x>=u-1)&&$.to({},{onUpdate:V,duration:we})}s&&s(B)},e.onWheel=function(){N._ts&&N.pause(),pt()-P>1e3&&(O=0,P=pt())},e.onChange=function(B,te,we,x,q){if(wi!==O&&M(),te&&t&&_(T(x[2]===te?Y+(B.startX-B.x):_()+te-x[1])),we){a.offset&&R();var re=q[2]===we,ge=re?G+B.startY-B.y:a()+we-q[1],J=v(ge);re&&ge!==J&&(G+=J-ge),a(J)}(we||te)&&Fr()},e.onEnable=function(){no(f,t?!1:"x"),ue.addEventListener("refresh",V),tt(oe,"resize",V),a.smooth&&(a.target.style.scrollBehavior="auto",a.smooth=_.smooth=!1),b.enable()},e.onDisable=function(){no(f,!0),et(oe,"resize",V),ue.removeEventListener("refresh",V),b.kill()},e.lockAxis=e.lockAxis!==!1,o=new We(e),o.iOS=$r,$r&&!a()&&a(1),$r&&$.ticker.add(wr),K=o._dc,N=$.to(o,{ease:"power4",paused:!0,inherit:!1,scrollX:t?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:Lu(a,a(),function(){return N.pause()})},onUpdate:Fr,onComplete:K.vars.onComplete}),o};ue.sort=function(l){if(gt(l))return se.sort(l);var e=oe.pageYOffset||0;return ue.getAll().forEach(function(r){return r._sortY=r.trigger?e+r.trigger.getBoundingClientRect().top:r.start+oe.innerHeight}),se.sort(l||function(r,t){return(r.vars.refreshPriority||0)*-1e6+(r.vars.containerAnimation?1e6:r._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+(t.vars.refreshPriority||0)*-1e6)})};ue.observe=function(l){return new We(l)};ue.normalizeScroll=function(l){if(typeof l>"u")return wt;if(l===!0&&wt)return wt.enable();if(l===!1){wt&&wt.kill(),wt=l;return}var e=l instanceof We?l:Pc(l);return wt&&wt.target===e.target&&wt.kill(),Ci(e.target)&&(wt=e),e};ue.core={_getVelocityProp:Eo,_inputObserver:Fu,_scrollers:ae,_proxies:Cr,bridge:{ss:function(){lr||ki("scrollStart"),lr=pt()},ref:function(){return dt}}};Su()&&$.registerPlugin(ue);/*!
 * matrix 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Ar,bi,ga,$s,yn,ws,Ns,Ln,pr="transform",zo=pr+"Origin",Nu,Iu=function(e){var r=e.ownerDocument||e;for(!(pr in e.style)&&("msTransform"in e.style)&&(pr="msTransform",zo=pr+"Origin");r.parentNode&&(r=r.parentNode););if(bi=window,Ns=new Mi,r){Ar=r,ga=r.documentElement,$s=r.body,Ln=Ar.createElementNS("http://www.w3.org/2000/svg","g"),Ln.style.transform="none";var t=r.createElement("div"),i=r.createElement("div"),n=r&&(r.body||r.firstElementChild);n&&n.appendChild&&(n.appendChild(t),t.appendChild(i),t.setAttribute("style","position:static;transform:translate3d(0,0,1px)"),Nu=i.offsetParent!==t,n.removeChild(t))}return r},kc=function(e){for(var r,t;e&&e!==$s;)t=e._gsap,t&&t.uncache&&t.get(e,"x"),t&&!t.scaleX&&!t.scaleY&&t.renderTransform&&(t.scaleX=t.scaleY=1e-4,t.renderTransform(1,t),r?r.push(t):r=[t]),e=e.parentNode;return r},zu=[],Yu=[],Mc=function(){return bi.pageYOffset||Ar.scrollTop||ga.scrollTop||$s.scrollTop||0},Dc=function(){return bi.pageXOffset||Ar.scrollLeft||ga.scrollLeft||$s.scrollLeft||0},ma=function(e){return e.ownerSVGElement||((e.tagName+"").toLowerCase()==="svg"?e:null)},Ec=function l(e){if(bi.getComputedStyle(e).position==="fixed")return!0;if(e=e.parentNode,e&&e.nodeType===1)return l(e)},so=function l(e,r){if(e.parentNode&&(Ar||Iu(e))){var t=ma(e),i=t?t.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",n=t?r?"rect":"g":"div",s=r!==2?0:100,o=r===3?100:0,u="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",f=Ar.createElementNS?Ar.createElementNS(i.replace(/^https/,"http"),n):Ar.createElement(n);return r&&(t?(ws||(ws=l(e)),f.setAttribute("width",.01),f.setAttribute("height",.01),f.setAttribute("transform","translate("+s+","+o+")"),ws.appendChild(f)):(yn||(yn=l(e),yn.style.cssText=u),f.style.cssText=u+"width:0.1px;height:0.1px;top:"+o+"px;left:"+s+"px",yn.appendChild(f))),f}throw"Need document and parent."},Oc=function(e){for(var r=new Mi,t=0;t<e.numberOfItems;t++)r.multiply(e.getItem(t).matrix);return r},Rc=function(e){var r=e.getCTM(),t;return r||(t=e.style[pr],e.style[pr]="none",e.appendChild(Ln),r=Ln.getCTM(),e.removeChild(Ln),t?e.style[pr]=t:e.style.removeProperty(pr.replace(/([A-Z])/g,"-$1").toLowerCase())),r||Ns.clone()},Ac=function(e,r){var t=ma(e),i=e===t,n=t?zu:Yu,s=e.parentNode,o=s&&!t&&s.shadowRoot&&s.shadowRoot.appendChild?s.shadowRoot:s,u,f,c,h,d,a;if(e===bi)return e;if(n.length||n.push(so(e,1),so(e,2),so(e,3)),u=t?ws:yn,t)i?(c=Rc(e),h=-c.e/c.a,d=-c.f/c.d,f=Ns):e.getBBox?(c=e.getBBox(),f=e.transform?e.transform.baseVal:{},f=f.numberOfItems?f.numberOfItems>1?Oc(f):f.getItem(0).matrix:Ns,h=f.a*c.x+f.c*c.y,d=f.b*c.x+f.d*c.y):(f=new Mi,h=d=0),(i?t:s).appendChild(u),u.setAttribute("transform","matrix("+f.a+","+f.b+","+f.c+","+f.d+","+(f.e+h)+","+(f.f+d)+")");else{if(h=d=0,Nu)for(f=e.offsetParent,c=e;c&&(c=c.parentNode)&&c!==f&&c.parentNode;)(bi.getComputedStyle(c)[pr]+"").length>4&&(h=c.offsetLeft,d=c.offsetTop,c=0);if(a=bi.getComputedStyle(e),a.position!=="absolute"&&a.position!=="fixed")for(f=e.offsetParent;s&&s!==f;)h+=s.scrollLeft||0,d+=s.scrollTop||0,s=s.parentNode;c=u.style,c.top=e.offsetTop-d+"px",c.left=e.offsetLeft-h+"px",c[pr]=a[pr],c[zo]=a[zo],c.position=a.position==="fixed"?"fixed":"absolute",o.appendChild(u)}return u},oo=function(e,r,t,i,n,s,o){return e.a=r,e.b=t,e.c=i,e.d=n,e.e=s,e.f=o,e},Mi=function(){function l(r,t,i,n,s,o){r===void 0&&(r=1),t===void 0&&(t=0),i===void 0&&(i=0),n===void 0&&(n=1),s===void 0&&(s=0),o===void 0&&(o=0),oo(this,r,t,i,n,s,o)}var e=l.prototype;return e.inverse=function(){var t=this.a,i=this.b,n=this.c,s=this.d,o=this.e,u=this.f,f=t*s-i*n||1e-10;return oo(this,s/f,-i/f,-n/f,t/f,(n*u-s*o)/f,-(t*u-i*o)/f)},e.multiply=function(t){var i=this.a,n=this.b,s=this.c,o=this.d,u=this.e,f=this.f,c=t.a,h=t.c,d=t.b,a=t.d,_=t.e,p=t.f;return oo(this,c*i+d*s,c*n+d*o,h*i+a*s,h*n+a*o,u+_*i+p*s,f+_*n+p*o)},e.clone=function(){return new l(this.a,this.b,this.c,this.d,this.e,this.f)},e.equals=function(t){var i=this.a,n=this.b,s=this.c,o=this.d,u=this.e,f=this.f;return i===t.a&&n===t.b&&s===t.c&&o===t.d&&u===t.e&&f===t.f},e.apply=function(t,i){i===void 0&&(i={});var n=t.x,s=t.y,o=this.a,u=this.b,f=this.c,c=this.d,h=this.e,d=this.f;return i.x=n*o+s*f+h||0,i.y=n*u+s*c+d||0,i},l}();function hi(l,e,r,t){if(!l||!l.parentNode||(Ar||Iu(l)).documentElement===l)return new Mi;var i=kc(l),n=ma(l),s=n?zu:Yu,o=Ac(l),u=s[0].getBoundingClientRect(),f=s[1].getBoundingClientRect(),c=s[2].getBoundingClientRect(),h=o.parentNode,d=Ec(l),a=new Mi((f.left-u.left)/100,(f.top-u.top)/100,(c.left-u.left)/100,(c.top-u.top)/100,u.left+(d?0:Dc()),u.top+(d?0:Mc()));if(h.removeChild(o),i)for(u=i.length;u--;)f=i[u],f.scaleX=f.scaleY=0,f.renderTransform(1,f);return e?a.inverse():a}function tl(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function Lc(l,e){l.prototype=Object.create(e.prototype),l.prototype.constructor=l,l.__proto__=e}var ce,Te,Vt,_r,Lr,ao,Er,Yo,xn,Kr,Xu,Xo,Un,ya,vn,hr,wn,bs,Bu,Bo,Is=0,$u=function(){return typeof window<"u"},Wu=function(){return ce||$u()&&(ce=window.gsap)&&ce.registerPlugin&&ce},Wr=function(e){return typeof e=="function"},Fn=function(e){return typeof e=="object"},dr=function(e){return typeof e>"u"},Ts=function(){return!1},Nn="transform",$o="transformOrigin",Xr=function(e){return Math.round(e*1e4)/1e4},fn=Array.isArray,fs=function(e,r){var t=Vt.createElementNS?Vt.createElementNS("http://www.w3.org/1999/xhtml".replace(/^https/,"http"),e):Vt.createElement(e);return t.style?t:Vt.createElement(e)},rl=180/Math.PI,ai=1e20,Fc=new Mi,Br=Date.now||function(){return new Date().getTime()},Ti=[],Ui={},Nc=0,Ic=/^(?:a|input|textarea|button|select)$/i,il=0,Ai={},kr={},Hu=function(e,r){var t={},i;for(i in e)t[i]=r?e[i]*r:e[i];return t},zc=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},nl=function l(e,r){for(var t=e.length,i;t--;)r?e[t].style.touchAction=r:e[t].style.removeProperty("touch-action"),i=e[t].children,i&&i.length&&l(i,r)},Vu=function(){return Ti.forEach(function(e){return e()})},Yc=function(e){Ti.push(e),Ti.length===1&&ce.ticker.add(Vu)},sl=function(){return!Ti.length&&ce.ticker.remove(Vu)},ol=function(e){for(var r=Ti.length;r--;)Ti[r]===e&&Ti.splice(r,1);ce.to(sl,{overwrite:!0,delay:15,duration:0,onComplete:sl,data:"_draggable"})},Xc=function(e,r){for(var t in r)t in e||(e[t]=r[t]);return e},Ze=function(e,r,t,i){if(e.addEventListener){var n=Un[r];i=i||(Xu?{passive:!1}:null),e.addEventListener(n||r,t,i),n&&r!==n&&e.addEventListener(r,t,i)}},Ve=function(e,r,t,i){if(e.removeEventListener){var n=Un[r];e.removeEventListener(n||r,t,i),n&&r!==n&&e.removeEventListener(r,t,i)}},er=function(e){e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation()},Bc=function(e,r){for(var t=e.length;t--;)if(e[t].identifier===r)return!0},$c=function l(e){ya=e.touches&&Is<e.touches.length,Ve(e.target,"touchend",l)},al=function(e){ya=e.touches&&Is<e.touches.length,Ze(e.target,"touchend",$c)},Gi=function(e){return Te.pageYOffset||e.scrollTop||e.documentElement.scrollTop||e.body.scrollTop||0},qi=function(e){return Te.pageXOffset||e.scrollLeft||e.documentElement.scrollLeft||e.body.scrollLeft||0},ll=function l(e,r){Ze(e,"scroll",r),rn(e.parentNode)||l(e.parentNode,r)},ul=function l(e,r){Ve(e,"scroll",r),rn(e.parentNode)||l(e.parentNode,r)},rn=function(e){return!e||e===_r||e.nodeType===9||e===Vt.body||e===Te||!e.nodeType||!e.parentNode},fl=function(e,r){var t=r==="x"?"Width":"Height",i="scroll"+t,n="client"+t;return Math.max(0,rn(e)?Math.max(_r[i],Lr[i])-(Te["inner"+t]||_r[n]||Lr[n]):e[i]-e[n])},lo=function l(e,r){var t=fl(e,"x"),i=fl(e,"y");rn(e)?e=kr:l(e.parentNode,r),e._gsMaxScrollX=t,e._gsMaxScrollY=i,r||(e._gsScrollX=e.scrollLeft||0,e._gsScrollY=e.scrollTop||0)},uo=function(e,r,t){var i=e.style;i&&(dr(i[r])&&(r=xn(r,e)||r),t==null?i.removeProperty&&i.removeProperty(r.replace(/([A-Z])/g,"-$1").toLowerCase()):i[r]=t)},Gn=function(e){return Te.getComputedStyle(e instanceof Element?e:e.host||(e.parentNode||{}).host||e)},li={},Li=function(e){if(e===Te)return li.left=li.top=0,li.width=li.right=_r.clientWidth||e.innerWidth||Lr.clientWidth||0,li.height=li.bottom=(e.innerHeight||0)-20<_r.clientHeight?_r.clientHeight:e.innerHeight||Lr.clientHeight||0,li;var r=e.ownerDocument||Vt,t=dr(e.pageX)?!e.nodeType&&!dr(e.left)&&!dr(e.top)?e:Kr(e)[0].getBoundingClientRect():{left:e.pageX-qi(r),top:e.pageY-Gi(r),right:e.pageX-qi(r)+1,bottom:e.pageY-Gi(r)+1};return dr(t.right)&&!dr(t.width)?(t.right=t.left+t.width,t.bottom=t.top+t.height):dr(t.width)&&(t={width:t.right-t.left,height:t.bottom-t.top,right:t.right,left:t.left,bottom:t.bottom,top:t.top}),t},Be=function(e,r,t){var i=e.vars,n=i[t],s=e._listeners[r],o;return Wr(n)&&(o=n.apply(i.callbackScope||e,i[t+"Params"]||[e.pointerEvent])),s&&e.dispatchEvent(r)===!1&&(o=!1),o},cl=function(e,r){var t=Kr(e)[0],i,n,s;return!t.nodeType&&t!==Te?dr(e.left)?(n=e.min||e.minX||e.minRotation||0,i=e.min||e.minY||0,{left:n,top:i,width:(e.max||e.maxX||e.maxRotation||0)-n,height:(e.max||e.maxY||0)-i}):(s={x:0,y:0},{left:e.left-s.x,top:e.top-s.y,width:e.width,height:e.height}):Wc(t,r)},tr={},Wc=function(e,r){r=Kr(r)[0];var t=e.getBBox&&e.ownerSVGElement,i=e.ownerDocument||Vt,n,s,o,u,f,c,h,d,a,_,p,y,P;if(e===Te)o=Gi(i),n=qi(i),s=n+(i.documentElement.clientWidth||e.innerWidth||i.body.clientWidth||0),u=o+((e.innerHeight||0)-20<i.documentElement.clientHeight?i.documentElement.clientHeight:e.innerHeight||i.body.clientHeight||0);else{if(r===Te||dr(r))return e.getBoundingClientRect();n=o=0,t?(_=e.getBBox(),p=_.width,y=_.height):(e.viewBox&&(_=e.viewBox.baseVal)&&(n=_.x||0,o=_.y||0,p=_.width,y=_.height),p||(P=Gn(e),_=P.boxSizing==="border-box",p=(parseFloat(P.width)||e.clientWidth||0)+(_?0:parseFloat(P.borderLeftWidth)+parseFloat(P.borderRightWidth)),y=(parseFloat(P.height)||e.clientHeight||0)+(_?0:parseFloat(P.borderTopWidth)+parseFloat(P.borderBottomWidth)))),s=p,u=y}return e===r?{left:n,top:o,width:s-n,height:u-o}:(f=hi(r,!0).multiply(hi(e)),c=f.apply({x:n,y:o}),h=f.apply({x:s,y:o}),d=f.apply({x:s,y:u}),a=f.apply({x:n,y:u}),n=Math.min(c.x,h.x,d.x,a.x),o=Math.min(c.y,h.y,d.y,a.y),{left:n,top:o,width:Math.max(c.x,h.x,d.x,a.x)-n,height:Math.max(c.y,h.y,d.y,a.y)-o})},fo=function(e,r,t,i,n,s){var o={},u,f,c;if(r)if(n!==1&&r instanceof Array){if(o.end=u=[],c=r.length,Fn(r[0]))for(f=0;f<c;f++)u[f]=Hu(r[f],n);else for(f=0;f<c;f++)u[f]=r[f]*n;t+=1.1,i-=1.1}else Wr(r)?o.end=function(h){var d=r.call(e,h),a,_;if(n!==1)if(Fn(d)){a={};for(_ in d)a[_]=d[_]*n;d=a}else d*=n;return d}:o.end=r;return(t||t===0)&&(o.max=t),(i||i===0)&&(o.min=i),s&&(o.velocity=0),o},Hc=function l(e){var r;return!e||!e.getAttribute||e===Lr?!1:(r=e.getAttribute("data-clickable"))==="true"||r!=="false"&&(Ic.test(e.nodeName+"")||e.getAttribute("contentEditable")==="true")?!0:l(e.parentNode)},cs=function(e,r){for(var t=e.length,i;t--;)i=e[t],i.ondragstart=i.onselectstart=r?null:Ts,ce.set(i,{lazy:!0,userSelect:r?"text":"none"})},Vc=function l(e){if(Gn(e).position==="fixed")return!0;if(e=e.parentNode,e&&e.nodeType===1)return l(e)},Uu,Wo,Uc=function(e,r){e=ce.utils.toArray(e)[0],r=r||{};var t=document.createElement("div"),i=t.style,n=e.firstChild,s=0,o=0,u=e.scrollTop,f=e.scrollLeft,c=e.scrollWidth,h=e.scrollHeight,d=0,a=0,_=0,p,y,P,D,O,k;Uu&&r.force3D!==!1?(O="translate3d(",k="px,0px)"):Nn&&(O="translate(",k="px)"),this.scrollTop=function(b,E){if(!arguments.length)return-this.top();this.top(-b,E)},this.scrollLeft=function(b,E){if(!arguments.length)return-this.left();this.left(-b,E)},this.left=function(b,E){if(!arguments.length)return-(e.scrollLeft+o);var T=e.scrollLeft-f,v=o;if((T>2||T<-2)&&!E){f=e.scrollLeft,ce.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-f),r.onKill&&r.onKill();return}b=-b,b<0?(o=b-.5|0,b=0):b>a?(o=b-a|0,b=a):o=0,(o||v)&&(this._skip||(i[Nn]=O+-o+"px,"+-s+k),o+d>=0&&(i.paddingRight=o+d+"px")),e.scrollLeft=b|0,f=e.scrollLeft},this.top=function(b,E){if(!arguments.length)return-(e.scrollTop+s);var T=e.scrollTop-u,v=s;if((T>2||T<-2)&&!E){u=e.scrollTop,ce.killTweensOf(this,{top:1,scrollTop:1}),this.top(-u),r.onKill&&r.onKill();return}b=-b,b<0?(s=b-.5|0,b=0):b>_?(s=b-_|0,b=_):s=0,(s||v)&&(this._skip||(i[Nn]=O+-o+"px,"+-s+k)),e.scrollTop=b|0,u=e.scrollTop},this.maxScrollTop=function(){return _},this.maxScrollLeft=function(){return a},this.disable=function(){for(n=t.firstChild;n;)D=n.nextSibling,e.appendChild(n),n=D;e===t.parentNode&&e.removeChild(t)},this.enable=function(){if(n=e.firstChild,n!==t){for(;n;)D=n.nextSibling,t.appendChild(n),n=D;e.appendChild(t),this.calibrate()}},this.calibrate=function(b){var E=e.clientWidth===p,T,v,M;u=e.scrollTop,f=e.scrollLeft,!(E&&e.clientHeight===y&&t.offsetHeight===P&&c===e.scrollWidth&&h===e.scrollHeight&&!b)&&((s||o)&&(v=this.left(),M=this.top(),this.left(-e.scrollLeft),this.top(-e.scrollTop)),T=Gn(e),(!E||b)&&(i.display="block",i.width="auto",i.paddingRight="0px",d=Math.max(0,e.scrollWidth-e.clientWidth),d&&(d+=parseFloat(T.paddingLeft)+(Wo?parseFloat(T.paddingRight):0))),i.display="inline-block",i.position="relative",i.overflow="visible",i.verticalAlign="top",i.boxSizing="content-box",i.width="100%",i.paddingRight=d+"px",Wo&&(i.paddingBottom=T.paddingBottom),p=e.clientWidth,y=e.clientHeight,c=e.scrollWidth,h=e.scrollHeight,a=e.scrollWidth-p,_=e.scrollHeight-y,P=t.offsetHeight,i.display="block",(v||M)&&(this.left(v),this.top(M)))},this.content=t,this.element=e,this._skip=!1,this.enable()},co=function(e){if($u()&&document.body){var r=window&&window.navigator;Te=window,Vt=document,_r=Vt.documentElement,Lr=Vt.body,ao=fs("div"),bs=!!window.PointerEvent,Er=fs("div"),Er.style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",wn=Er.style.cursor==="grab"?"grab":"move",vn=r&&r.userAgent.toLowerCase().indexOf("android")!==-1,Xo="ontouchstart"in _r&&"orientation"in Te||r&&(r.MaxTouchPoints>0||r.msMaxTouchPoints>0),Wo=function(){var t=fs("div"),i=fs("div"),n=i.style,s=Lr,o;return n.display="inline-block",n.position="relative",t.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",t.appendChild(i),s.appendChild(t),o=i.offsetHeight+18>t.scrollHeight,s.removeChild(t),o}(),Un=function(t){for(var i=t.split(","),n=("onpointerdown"in ao?"pointerdown,pointermove,pointerup,pointercancel":"onmspointerdown"in ao?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":t).split(","),s={},o=4;--o>-1;)s[i[o]]=n[o],s[n[o]]=i[o];try{_r.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){Xu=1}}))}catch{}return s}("touchstart,touchmove,touchend,touchcancel"),Ze(Vt,"touchcancel",Ts),Ze(Te,"touchmove",Ts),Lr&&Lr.addEventListener("touchstart",Ts),Ze(Vt,"contextmenu",function(){for(var t in Ui)Ui[t].isPressed&&Ui[t].endDrag()}),ce=Yo=Wu()}ce?(hr=ce.plugins.inertia,Bu=ce.core.context||function(){},xn=ce.utils.checkPrefix,Nn=xn(Nn),$o=xn($o),Kr=ce.utils.toArray,Bo=ce.core.getStyleSaver,Uu=!!xn("perspective")):e&&console.warn("Please gsap.registerPlugin(Draggable)")},Gc=function(){function l(r){this._listeners={},this.target=r||this}var e=l.prototype;return e.addEventListener=function(t,i){var n=this._listeners[t]||(this._listeners[t]=[]);~n.indexOf(i)||n.push(i)},e.removeEventListener=function(t,i){var n=this._listeners[t],s=n&&n.indexOf(i);s>=0&&n.splice(s,1)},e.dispatchEvent=function(t){var i=this,n;return(this._listeners[t]||[]).forEach(function(s){return s.call(i,{type:t,target:i.target})===!1&&(n=!1)}),n},l}(),nn=function(l){Lc(e,l);function e(r,t){var i;i=l.call(this)||this,Yo||co(1),r=Kr(r)[0],i.styles=Bo&&Bo(r,"transform,left,top"),hr||(hr=ce.plugins.inertia),i.vars=t=Hu(t||{}),i.target=r,i.x=i.y=i.rotation=0,i.dragResistance=parseFloat(t.dragResistance)||0,i.edgeResistance=isNaN(t.edgeResistance)?1:parseFloat(t.edgeResistance)||0,i.lockAxis=t.lockAxis,i.autoScroll=t.autoScroll||0,i.lockedAxis=null,i.allowEventDefault=!!t.allowEventDefault,ce.getProperty(r,"x");var n=(t.type||"x,y").toLowerCase(),s=~n.indexOf("x")||~n.indexOf("y"),o=n.indexOf("rotation")!==-1,u=o?"rotation":s?"x":"left",f=s?"y":"top",c=!!(~n.indexOf("x")||~n.indexOf("left")||n==="scroll"),h=!!(~n.indexOf("y")||~n.indexOf("top")||n==="scroll"),d=t.minimumMovement||2,a=tl(i),_=Kr(t.trigger||t.handle||r),p={},y=0,P=!1,D=t.autoScrollMarginTop||40,O=t.autoScrollMarginRight||40,k=t.autoScrollMarginBottom||40,b=t.autoScrollMarginLeft||40,E=t.clickableTest||Hc,T=0,v=r._gsap||ce.core.getCache(r),M=Vc(r),R=function(g,F){return parseFloat(v.get(r,g,F))},I=r.ownerDocument||Vt,N,Y,G,K,V,B,te,we,x,q,re,ge,J,Fe,ye,Ce,de,He,it,De,Ne,ie,ne,j,ut,w,ke,Ft,Qt,Me,Oe,ur,jt,Xe=function(g){return er(g),g.stopImmediatePropagation&&g.stopImmediatePropagation(),!1},Ke=function H(g){if(a.autoScroll&&a.isDragging&&(P||de)){var F=r,m=a.autoScroll*15,S,L,C,z,A,X,U,W;for(P=!1,kr.scrollTop=Te.pageYOffset!=null?Te.pageYOffset:I.documentElement.scrollTop!=null?I.documentElement.scrollTop:I.body.scrollTop,kr.scrollLeft=Te.pageXOffset!=null?Te.pageXOffset:I.documentElement.scrollLeft!=null?I.documentElement.scrollLeft:I.body.scrollLeft,z=a.pointerX-kr.scrollLeft,A=a.pointerY-kr.scrollTop;F&&!L;)L=rn(F.parentNode),S=L?kr:F.parentNode,C=L?{bottom:Math.max(_r.clientHeight,Te.innerHeight||0),right:Math.max(_r.clientWidth,Te.innerWidth||0),left:0,top:0}:S.getBoundingClientRect(),X=U=0,h&&(W=S._gsMaxScrollY-S.scrollTop,W<0?U=W:A>C.bottom-k&&W?(P=!0,U=Math.min(W,m*(1-Math.max(0,C.bottom-A)/k)|0)):A<C.top+D&&S.scrollTop&&(P=!0,U=-Math.min(S.scrollTop,m*(1-Math.max(0,A-C.top)/D)|0)),U&&(S.scrollTop+=U)),c&&(W=S._gsMaxScrollX-S.scrollLeft,W<0?X=W:z>C.right-O&&W?(P=!0,X=Math.min(W,m*(1-Math.max(0,C.right-z)/O)|0)):z<C.left+b&&S.scrollLeft&&(P=!0,X=-Math.min(S.scrollLeft,m*(1-Math.max(0,z-C.left)/b)|0)),X&&(S.scrollLeft+=X)),L&&(X||U)&&(Te.scrollTo(S.scrollLeft,S.scrollTop),zt(a.pointerX+X,a.pointerY+U)),F=S}if(de){var ee=a.x,pe=a.y;o?(a.deltaX=ee-parseFloat(v.rotation),a.rotation=ee,v.rotation=ee+"deg",v.renderTransform(1,v)):Y?(h&&(a.deltaY=pe-Y.top(),Y.top(pe)),c&&(a.deltaX=ee-Y.left(),Y.left(ee))):s?(h&&(a.deltaY=pe-parseFloat(v.y),v.y=pe+"px"),c&&(a.deltaX=ee-parseFloat(v.x),v.x=ee+"px"),v.renderTransform(1,v)):(h&&(a.deltaY=pe-parseFloat(r.style.top||0),r.style.top=pe+"px"),c&&(a.deltaX=ee-parseFloat(r.style.left||0),r.style.left=ee+"px")),we&&!g&&!Ft&&(Ft=!0,Be(a,"drag","onDrag")===!1&&(c&&(a.x-=a.deltaX),h&&(a.y-=a.deltaY),H(!0)),Ft=!1)}de=!1},xe=function(g,F){var m=a.x,S=a.y,L,C;r._gsap||(v=ce.core.getCache(r)),v.uncache&&ce.getProperty(r,"x"),s?(a.x=parseFloat(v.x),a.y=parseFloat(v.y)):o?a.x=a.rotation=parseFloat(v.rotation):Y?(a.y=Y.top(),a.x=Y.left()):(a.y=parseFloat(r.style.top||(C=Gn(r))&&C.top)||0,a.x=parseFloat(r.style.left||(C||{}).left)||0),(it||De||Ne)&&!F&&(a.isDragging||a.isThrowing)&&(Ne&&(Ai.x=a.x,Ai.y=a.y,L=Ne(Ai),L.x!==a.x&&(a.x=L.x,de=!0),L.y!==a.y&&(a.y=L.y,de=!0)),it&&(L=it(a.x),L!==a.x&&(a.x=L,o&&(a.rotation=L),de=!0)),De&&(L=De(a.y),L!==a.y&&(a.y=L),de=!0)),de&&Ke(!0),g||(a.deltaX=a.x-m,a.deltaY=a.y-S,Be(a,"throwupdate","onThrowUpdate"))},yt=function(g,F,m,S){return F==null&&(F=-ai),m==null&&(m=ai),Wr(g)?function(L){var C=a.isPressed?1-a.edgeResistance:1;return g.call(a,(L>m?m+(L-m)*C:L<F?F+(L-F)*C:L)*S)*S}:fn(g)?function(L){for(var C=g.length,z=0,A=ai,X,U;--C>-1;)X=g[C],U=X-L,U<0&&(U=-U),U<A&&X>=F&&X<=m&&(z=C,A=U);return g[z]}:isNaN(g)?function(L){return L}:function(){return g*S}},Pt=function(g,F,m,S,L,C,z){return C=C&&C<ai?C*C:ai,Wr(g)?function(A){var X=a.isPressed?1-a.edgeResistance:1,U=A.x,W=A.y,ee,pe,_e;return A.x=U=U>m?m+(U-m)*X:U<F?F+(U-F)*X:U,A.y=W=W>L?L+(W-L)*X:W<S?S+(W-S)*X:W,ee=g.call(a,A),ee!==A&&(A.x=ee.x,A.y=ee.y),z!==1&&(A.x*=z,A.y*=z),C<ai&&(pe=A.x-U,_e=A.y-W,pe*pe+_e*_e>C&&(A.x=U,A.y=W)),A}:fn(g)?function(A){for(var X=g.length,U=0,W=ai,ee,pe,_e,le;--X>-1;)_e=g[X],ee=_e.x-A.x,pe=_e.y-A.y,le=ee*ee+pe*pe,le<W&&(U=X,W=le);return W<=C?g[U]:A}:function(A){return A}},Nt=function(){var g,F,m,S;te=!1,Y?(Y.calibrate(),a.minX=re=-Y.maxScrollLeft(),a.minY=J=-Y.maxScrollTop(),a.maxX=q=a.maxY=ge=0,te=!0):t.bounds&&(g=cl(t.bounds,r.parentNode),o?(a.minX=re=g.left,a.maxX=q=g.left+g.width,a.minY=J=a.maxY=ge=0):!dr(t.bounds.maxX)||!dr(t.bounds.maxY)?(g=t.bounds,a.minX=re=g.minX,a.minY=J=g.minY,a.maxX=q=g.maxX,a.maxY=ge=g.maxY):(F=cl(r,r.parentNode),a.minX=re=Math.round(R(u,"px")+g.left-F.left),a.minY=J=Math.round(R(f,"px")+g.top-F.top),a.maxX=q=Math.round(re+(g.width-F.width)),a.maxY=ge=Math.round(J+(g.height-F.height))),re>q&&(a.minX=q,a.maxX=q=re,re=a.minX),J>ge&&(a.minY=ge,a.maxY=ge=J,J=a.minY),o&&(a.minRotation=re,a.maxRotation=q),te=!0),t.liveSnap&&(m=t.liveSnap===!0?t.snap||{}:t.liveSnap,S=fn(m)||Wr(m),o?(it=yt(S?m:m.rotation,re,q,1),De=null):m.points?Ne=Pt(S?m:m.points,re,q,J,ge,m.radius,Y?-1:1):(c&&(it=yt(S?m:m.x||m.left||m.scrollLeft,re,q,Y?-1:1)),h&&(De=yt(S?m:m.y||m.top||m.scrollTop,J,ge,Y?-1:1))))},ii=function(){a.isThrowing=!1,Be(a,"throwcomplete","onThrowComplete")},Ie=function(){a.isThrowing=!1},zr=function(g,F){var m,S,L,C;g&&hr?(g===!0&&(m=t.snap||t.liveSnap||{},S=fn(m)||Wr(m),g={resistance:(t.throwResistance||t.resistance||1e3)/(o?10:1)},o?g.rotation=fo(a,S?m:m.rotation,q,re,1,F):(c&&(g[u]=fo(a,S?m:m.points||m.x||m.left,q,re,Y?-1:1,F||a.lockedAxis==="x")),h&&(g[f]=fo(a,S?m:m.points||m.y||m.top,ge,J,Y?-1:1,F||a.lockedAxis==="y")),(m.points||fn(m)&&Fn(m[0]))&&(g.linkedProps=u+","+f,g.radius=m.radius))),a.isThrowing=!0,C=isNaN(t.overshootTolerance)?t.edgeResistance===1?0:1-a.edgeResistance+.2:t.overshootTolerance,g.duration||(g.duration={max:Math.max(t.minDuration||0,"maxDuration"in t?t.maxDuration:2),min:isNaN(t.minDuration)?C===0||Fn(g)&&g.resistance>1e3?0:.5:t.minDuration,overshoot:C}),a.tween=L=ce.to(Y||r,{inertia:g,data:"_draggable",inherit:!1,onComplete:ii,onInterrupt:Ie,onUpdate:t.fastMode?Be:xe,onUpdateParams:t.fastMode?[a,"onthrowupdate","onThrowUpdate"]:m&&m.radius?[!1,!0]:[]}),t.fastMode||(Y&&(Y._skip=!0),L.render(1e9,!0,!0),xe(!0,!0),a.endX=a.x,a.endY=a.y,o&&(a.endRotation=a.x),L.play(0),xe(!0,!0),Y&&(Y._skip=!1))):te&&a.applyBounds()},Zt=function(g){var F=j,m;j=hi(r.parentNode,!0),g&&a.isPressed&&!j.equals(F||new Mi)&&(m=F.inverse().apply({x:G,y:K}),j.apply(m,m),G=m.x,K=m.y),j.equals(Fc)&&(j=null)},fr=function(){var g=1-a.edgeResistance,F=M?qi(I):0,m=M?Gi(I):0,S,L,C;s&&(v.x=R(u,"px")+"px",v.y=R(f,"px")+"px",v.renderTransform()),Zt(!1),tr.x=a.pointerX-F,tr.y=a.pointerY-m,j&&j.apply(tr,tr),G=tr.x,K=tr.y,de&&(zt(a.pointerX,a.pointerY),Ke(!0)),ur=hi(r),Y?(Nt(),B=Y.top(),V=Y.left()):(Jt()?(xe(!0,!0),Nt()):a.applyBounds(),o?(S=r.ownerSVGElement?[v.xOrigin-r.getBBox().x,v.yOrigin-r.getBBox().y]:(Gn(r)[$o]||"0 0").split(" "),Ce=a.rotationOrigin=hi(r).apply({x:parseFloat(S[0])||0,y:parseFloat(S[1])||0}),xe(!0,!0),L=a.pointerX-Ce.x-F,C=Ce.y-a.pointerY+m,V=a.x,B=a.y=Math.atan2(C,L)*rl):(B=R(f,"px"),V=R(u,"px"))),te&&g&&(V>q?V=q+(V-q)/g:V<re&&(V=re-(re-V)/g),o||(B>ge?B=ge+(B-ge)/g:B<J&&(B=J-(J-B)/g))),a.startX=V=Xr(V),a.startY=B=Xr(B)},Jt=function(){return a.tween&&a.tween.isActive()},gr=function(){Er.parentNode&&!Jt()&&!a.isDragging&&Er.parentNode.removeChild(Er)},It=function(g,F){var m;if(!N||a.isPressed||!g||(g.type==="mousedown"||g.type==="pointerdown")&&!F&&Br()-T<30&&Un[a.pointerEvent.type]){Oe&&g&&N&&er(g);return}if(ut=Jt(),jt=!1,a.pointerEvent=g,Un[g.type]?(ne=~g.type.indexOf("touch")?g.currentTarget||g.target:I,Ze(ne,"touchend",Re),Ze(ne,"touchmove",Q),Ze(ne,"touchcancel",Re),Ze(I,"touchstart",al)):(ne=null,Ze(I,"mousemove",Q)),ke=null,(!bs||!ne)&&(Ze(I,"mouseup",Re),g&&g.target&&Ze(g.target,"mouseup",Re)),ie=E.call(a,g.target)&&t.dragClickables===!1&&!F,ie){Ze(g.target,"change",Re),Be(a,"pressInit","onPressInit"),Be(a,"press","onPress"),cs(_,!0),Oe=!1;return}if(w=!ne||c===h||a.vars.allowNativeTouchScrolling===!1||a.vars.allowContextMenu&&g&&(g.ctrlKey||g.which>2)?!1:c?"y":"x",Oe=!w&&!a.allowEventDefault,Oe&&(er(g),Ze(Te,"touchforcechange",er)),g.changedTouches?(g=Fe=g.changedTouches[0],ye=g.identifier):g.pointerId?ye=g.pointerId:Fe=ye=null,Is++,Yc(Ke),K=a.pointerY=g.pageY,G=a.pointerX=g.pageX,Be(a,"pressInit","onPressInit"),(w||a.autoScroll)&&lo(r.parentNode),r.parentNode&&a.autoScroll&&!Y&&!o&&r.parentNode._gsMaxScrollX&&!Er.parentNode&&!r.getBBox&&(Er.style.width=r.parentNode.scrollWidth+"px",r.parentNode.appendChild(Er)),fr(),a.tween&&a.tween.kill(),a.isThrowing=!1,ce.killTweensOf(Y||r,p,!0),Y&&ce.killTweensOf(r,{scrollTo:1},!0),a.tween=a.lockedAxis=null,(t.zIndexBoost||!o&&!Y&&t.zIndexBoost!==!1)&&(r.style.zIndex=e.zIndex++),a.isPressed=!0,we=!!(t.onDrag||a._listeners.drag),x=!!(t.onMove||a._listeners.move),t.cursor!==!1||t.activeCursor)for(m=_.length;--m>-1;)ce.set(_[m],{cursor:t.activeCursor||t.cursor||(wn==="grab"?"grabbing":wn)});Be(a,"press","onPress")},Q=function(g){var F=g,m,S,L,C,z,A;if(!N||ya||!a.isPressed||!g){Oe&&g&&N&&er(g);return}if(a.pointerEvent=g,m=g.changedTouches,m){if(g=m[0],g!==Fe&&g.identifier!==ye){for(C=m.length;--C>-1&&(g=m[C]).identifier!==ye&&g.target!==r;);if(C<0)return}}else if(g.pointerId&&ye&&g.pointerId!==ye)return;if(ne&&w&&!ke&&(tr.x=g.pageX-(M?qi(I):0),tr.y=g.pageY-(M?Gi(I):0),j&&j.apply(tr,tr),S=tr.x,L=tr.y,z=Math.abs(S-G),A=Math.abs(L-K),(z!==A&&(z>d||A>d)||vn&&w===ke)&&(ke=z>A&&c?"x":"y",w&&ke!==w&&Ze(Te,"touchforcechange",er),a.vars.lockAxisOnTouchScroll!==!1&&c&&h&&(a.lockedAxis=ke==="x"?"y":"x",Wr(a.vars.onLockAxis)&&a.vars.onLockAxis.call(a,F)),vn&&w===ke))){Re(F);return}!a.allowEventDefault&&(!w||ke&&w!==ke)&&F.cancelable!==!1?(er(F),Oe=!0):Oe&&(Oe=!1),a.autoScroll&&(P=!0),zt(g.pageX,g.pageY,x)},zt=function(g,F,m){var S=1-a.dragResistance,L=1-a.edgeResistance,C=a.pointerX,z=a.pointerY,A=B,X=a.x,U=a.y,W=a.endX,ee=a.endY,pe=a.endRotation,_e=de,le,fe,ve,Z,nt,ze;a.pointerX=g,a.pointerY=F,M&&(g-=qi(I),F-=Gi(I)),o?(Z=Math.atan2(Ce.y-F,g-Ce.x)*rl,nt=a.y-Z,nt>180?(B-=360,a.y=Z):nt<-180&&(B+=360,a.y=Z),a.x!==V||Math.max(Math.abs(G-g),Math.abs(K-F))>d?(a.y=Z,ve=V+(B-Z)*S):ve=V):(j&&(ze=g*j.a+F*j.c+j.e,F=g*j.b+F*j.d+j.f,g=ze),fe=F-K,le=g-G,fe<d&&fe>-d&&(fe=0),le<d&&le>-d&&(le=0),(a.lockAxis||a.lockedAxis)&&(le||fe)&&(ze=a.lockedAxis,ze||(a.lockedAxis=ze=c&&Math.abs(le)>Math.abs(fe)?"y":h?"x":null,ze&&Wr(a.vars.onLockAxis)&&a.vars.onLockAxis.call(a,a.pointerEvent)),ze==="y"?fe=0:ze==="x"&&(le=0)),ve=Xr(V+le*S),Z=Xr(B+fe*S)),(it||De||Ne)&&(a.x!==ve||a.y!==Z&&!o)&&(Ne&&(Ai.x=ve,Ai.y=Z,ze=Ne(Ai),ve=Xr(ze.x),Z=Xr(ze.y)),it&&(ve=Xr(it(ve))),De&&(Z=Xr(De(Z)))),te&&(ve>q?ve=q+Math.round((ve-q)*L):ve<re&&(ve=re+Math.round((ve-re)*L)),o||(Z>ge?Z=Math.round(ge+(Z-ge)*L):Z<J&&(Z=Math.round(J+(Z-J)*L)))),(a.x!==ve||a.y!==Z&&!o)&&(o?(a.endRotation=a.x=a.endX=ve,de=!0):(h&&(a.y=a.endY=Z,de=!0),c&&(a.x=a.endX=ve,de=!0)),!m||Be(a,"move","onMove")!==!1?!a.isDragging&&a.isPressed&&(a.isDragging=jt=!0,Be(a,"dragstart","onDragStart")):(a.pointerX=C,a.pointerY=z,B=A,a.x=X,a.y=U,a.endX=W,a.endY=ee,a.endRotation=pe,de=_e))},Re=function H(g,F){if(!N||!a.isPressed||g&&ye!=null&&!F&&(g.pointerId&&g.pointerId!==ye&&g.target!==r||g.changedTouches&&!Bc(g.changedTouches,ye))){Oe&&g&&N&&er(g);return}a.isPressed=!1;var m=g,S=a.isDragging,L=a.vars.allowContextMenu&&g&&(g.ctrlKey||g.which>2),C=ce.delayedCall(.001,gr),z,A,X,U,W;if(ne?(Ve(ne,"touchend",H),Ve(ne,"touchmove",Q),Ve(ne,"touchcancel",H),Ve(I,"touchstart",al)):Ve(I,"mousemove",Q),Ve(Te,"touchforcechange",er),(!bs||!ne)&&(Ve(I,"mouseup",H),g&&g.target&&Ve(g.target,"mouseup",H)),de=!1,S&&(y=il=Br(),a.isDragging=!1),ol(Ke),ie&&!L){g&&(Ve(g.target,"change",H),a.pointerEvent=m),cs(_,!1),Be(a,"release","onRelease"),Be(a,"click","onClick"),ie=!1;return}for(A=_.length;--A>-1;)uo(_[A],"cursor",t.cursor||(t.cursor!==!1?wn:null));if(Is--,g){if(z=g.changedTouches,z&&(g=z[0],g!==Fe&&g.identifier!==ye)){for(A=z.length;--A>-1&&(g=z[A]).identifier!==ye&&g.target!==r;);if(A<0&&!F)return}a.pointerEvent=m,a.pointerX=g.pageX,a.pointerY=g.pageY}return L&&m?(er(m),Oe=!0,Be(a,"release","onRelease")):m&&!S?(Oe=!1,ut&&(t.snap||t.bounds)&&zr(t.inertia||t.throwProps),Be(a,"release","onRelease"),(!vn||m.type!=="touchmove")&&m.type.indexOf("cancel")===-1&&(Be(a,"click","onClick"),Br()-T<300&&Be(a,"doubleclick","onDoubleClick"),U=m.target||r,T=Br(),W=function(){T!==Qt&&a.enabled()&&!a.isPressed&&!m.defaultPrevented&&(U.click?U.click():I.createEvent&&(X=I.createEvent("MouseEvents"),X.initMouseEvent("click",!0,!0,Te,1,a.pointerEvent.screenX,a.pointerEvent.screenY,a.pointerX,a.pointerY,!1,!1,!1,!1,0,null),U.dispatchEvent(X)))},!vn&&!m.defaultPrevented&&ce.delayedCall(.05,W))):(zr(t.inertia||t.throwProps),!a.allowEventDefault&&m&&(t.dragClickables!==!1||!E.call(a,m.target))&&S&&(!w||ke&&w===ke)&&m.cancelable!==!1?(Oe=!0,er(m)):Oe=!1,Be(a,"release","onRelease")),Jt()&&C.duration(a.tween.duration()),S&&Be(a,"dragend","onDragEnd"),!0},ft=function(g){if(g&&a.isDragging&&!Y){var F=g.target||r.parentNode,m=F.scrollLeft-F._gsScrollX,S=F.scrollTop-F._gsScrollY;(m||S)&&(j?(G-=m*j.a+S*j.c,K-=S*j.d+m*j.b):(G-=m,K-=S),F._gsScrollX+=m,F._gsScrollY+=S,zt(a.pointerX,a.pointerY))}},kt=function(g){var F=Br(),m=F-T<100,S=F-y<50,L=m&&Qt===T,C=a.pointerEvent&&a.pointerEvent.defaultPrevented,z=m&&Me===T,A=g.isTrusted||g.isTrusted==null&&m&&L;if((L||S&&a.vars.suppressClickOnDrag!==!1)&&g.stopImmediatePropagation&&g.stopImmediatePropagation(),m&&!(a.pointerEvent&&a.pointerEvent.defaultPrevented)&&(!L||A&&!z)){A&&L&&(Me=T),Qt=T;return}(a.isPressed||S||m)&&(!A||!g.detail||!m||C)&&er(g),!m&&!S&&!jt&&(g&&g.target&&(a.pointerEvent=g),Be(a,"click","onClick"))},mr=function(g){return j?{x:g.x*j.a+g.y*j.c+j.e,y:g.x*j.b+g.y*j.d+j.f}:{x:g.x,y:g.y}};return He=e.get(r),He&&He.kill(),i.startDrag=function(H,g){var F,m,S,L;It(H||a.pointerEvent,!0),g&&!a.hitTest(H||a.pointerEvent)&&(F=Li(H||a.pointerEvent),m=Li(r),S=mr({x:F.left+F.width/2,y:F.top+F.height/2}),L=mr({x:m.left+m.width/2,y:m.top+m.height/2}),G-=S.x-L.x,K-=S.y-L.y),a.isDragging||(a.isDragging=jt=!0,Be(a,"dragstart","onDragStart"))},i.drag=Q,i.endDrag=function(H){return Re(H||a.pointerEvent,!0)},i.timeSinceDrag=function(){return a.isDragging?0:(Br()-y)/1e3},i.timeSinceClick=function(){return(Br()-T)/1e3},i.hitTest=function(H,g){return e.hitTest(a.target,H,g)},i.getDirection=function(H,g){var F=H==="velocity"&&hr?H:Fn(H)&&!o?"element":"start",m,S,L,C,z,A;return F==="element"&&(z=Li(a.target),A=Li(H)),m=F==="start"?a.x-V:F==="velocity"?hr.getVelocity(r,u):z.left+z.width/2-(A.left+A.width/2),o?m<0?"counter-clockwise":"clockwise":(g=g||2,S=F==="start"?a.y-B:F==="velocity"?hr.getVelocity(r,f):z.top+z.height/2-(A.top+A.height/2),L=Math.abs(m/S),C=L<1/g?"":m<0?"left":"right",L<g&&(C!==""&&(C+="-"),C+=S<0?"up":"down"),C)},i.applyBounds=function(H,g){var F,m,S,L,C,z;if(H&&t.bounds!==H)return t.bounds=H,a.update(!0,g);if(xe(!0),Nt(),te&&!Jt()){if(F=a.x,m=a.y,F>q?F=q:F<re&&(F=re),m>ge?m=ge:m<J&&(m=J),(a.x!==F||a.y!==m)&&(S=!0,a.x=a.endX=F,o?a.endRotation=F:a.y=a.endY=m,de=!0,Ke(!0),a.autoScroll&&!a.isDragging))for(lo(r.parentNode),L=r,kr.scrollTop=Te.pageYOffset!=null?Te.pageYOffset:I.documentElement.scrollTop!=null?I.documentElement.scrollTop:I.body.scrollTop,kr.scrollLeft=Te.pageXOffset!=null?Te.pageXOffset:I.documentElement.scrollLeft!=null?I.documentElement.scrollLeft:I.body.scrollLeft;L&&!z;)z=rn(L.parentNode),C=z?kr:L.parentNode,h&&C.scrollTop>C._gsMaxScrollY&&(C.scrollTop=C._gsMaxScrollY),c&&C.scrollLeft>C._gsMaxScrollX&&(C.scrollLeft=C._gsMaxScrollX),L=C;a.isThrowing&&(S||a.endX>q||a.endX<re||a.endY>ge||a.endY<J)&&zr(t.inertia||t.throwProps,S)}return a},i.update=function(H,g,F){if(g&&a.isPressed){var m=hi(r),S=ur.apply({x:a.x-V,y:a.y-B}),L=hi(r.parentNode,!0);L.apply({x:m.e-S.x,y:m.f-S.y},S),a.x-=S.x-L.e,a.y-=S.y-L.f,Ke(!0),fr()}var C=a.x,z=a.y;return Zt(!g),H?a.applyBounds():(de&&F&&Ke(!0),xe(!0)),g&&(zt(a.pointerX,a.pointerY),de&&Ke(!0)),a.isPressed&&!g&&(c&&Math.abs(C-a.x)>.01||h&&Math.abs(z-a.y)>.01&&!o)&&fr(),a.autoScroll&&(lo(r.parentNode,a.isDragging),P=a.isDragging,Ke(!0),ul(r,ft),ll(r,ft)),a},i.enable=function(H){var g={lazy:!0},F,m,S;if(t.cursor!==!1&&(g.cursor=t.cursor||wn),ce.utils.checkPrefix("touchCallout")&&(g.touchCallout="none"),H!=="soft"){for(nl(_,c===h?"none":t.allowNativeTouchScrolling&&r.scrollHeight===r.clientHeight==(r.scrollWidth===r.clientHeight)||t.allowEventDefault?"manipulation":c?"pan-y":"pan-x"),m=_.length;--m>-1;)S=_[m],bs||Ze(S,"mousedown",It),Ze(S,"touchstart",It),Ze(S,"click",kt,!0),ce.set(S,g),S.getBBox&&S.ownerSVGElement&&c!==h&&ce.set(S.ownerSVGElement,{touchAction:t.allowNativeTouchScrolling||t.allowEventDefault?"manipulation":c?"pan-y":"pan-x"}),t.allowContextMenu||Ze(S,"contextmenu",Xe);cs(_,!1)}return ll(r,ft),N=!0,hr&&H!=="soft"&&hr.track(Y||r,s?"x,y":o?"rotation":"top,left"),r._gsDragID=F=r._gsDragID||"d"+Nc++,Ui[F]=a,Y&&(Y.enable(),Y.element._gsDragID=F),(t.bounds||o)&&fr(),t.bounds&&a.applyBounds(),a},i.disable=function(H){for(var g=a.isDragging,F=_.length,m;--F>-1;)uo(_[F],"cursor",null);if(H!=="soft"){for(nl(_,null),F=_.length;--F>-1;)m=_[F],uo(m,"touchCallout",null),Ve(m,"mousedown",It),Ve(m,"touchstart",It),Ve(m,"click",kt,!0),Ve(m,"contextmenu",Xe);cs(_,!0),ne&&(Ve(ne,"touchcancel",Re),Ve(ne,"touchend",Re),Ve(ne,"touchmove",Q)),Ve(I,"mouseup",Re),Ve(I,"mousemove",Q)}return ul(r,ft),N=!1,hr&&H!=="soft"&&(hr.untrack(Y||r,s?"x,y":o?"rotation":"top,left"),a.tween&&a.tween.kill()),Y&&Y.disable(),ol(Ke),a.isDragging=a.isPressed=ie=!1,g&&Be(a,"dragend","onDragEnd"),a},i.enabled=function(H,g){return arguments.length?H?a.enable(g):a.disable(g):N},i.kill=function(){return a.isThrowing=!1,a.tween&&a.tween.kill(),a.disable(),ce.set(_,{clearProps:"userSelect"}),delete Ui[r._gsDragID],a},i.revert=function(){this.kill(),this.styles&&this.styles.revert()},~n.indexOf("scroll")&&(Y=i.scrollProxy=new Uc(r,zc({onKill:function(){a.isPressed&&Re(null)}},t)),r.style.overflowY=h&&!Xo?"auto":"hidden",r.style.overflowX=c&&!Xo?"auto":"hidden",r=Y.content),o?p.rotation=1:(c&&(p[u]=1),h&&(p[f]=1)),v.force3D="force3D"in t?t.force3D:!0,Bu(tl(i)),i.enable(),i}return e.register=function(t){ce=t,co()},e.create=function(t,i){return Yo||co(!0),Kr(t).map(function(n){return new e(n,i)})},e.get=function(t){return Ui[(Kr(t)[0]||{})._gsDragID]},e.timeSinceDrag=function(){return(Br()-il)/1e3},e.hitTest=function(t,i,n){if(t===i)return!1;var s=Li(t),o=Li(i),u=s.top,f=s.left,c=s.right,h=s.bottom,d=s.width,a=s.height,_=o.left>c||o.right<f||o.top>h||o.bottom<u,p,y,P;return _||!n?!_:(P=(n+"").indexOf("%")!==-1,n=parseFloat(n)||0,p={left:Math.max(f,o.left),top:Math.max(u,o.top)},p.width=Math.min(c,o.right)-p.left,p.height=Math.min(h,o.bottom)-p.top,p.width<0||p.height<0?!1:P?(n*=.01,y=p.width*p.height,y>=d*a*n||y>=o.width*o.height*n):p.width>n&&p.height>n)},e}(Gc);Xc(nn.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1});nn.zIndex=1e3;nn.version="3.13.0";Wu()&&ce.registerPlugin(nn);Mt.registerPlugin(ue,nn);function hl(l,e){let r;return function(...i){const n=()=>{clearTimeout(r),l(...i)};clearTimeout(r),r=setTimeout(n,e)}}function qc(l,e){let r=0,t=l.length-1,i,n=t;for(;r<=t;)if(i=Math.floor((r+t)/2),Math.abs(l[i]-e)<Math.abs(l[n]-e)&&(n=i),l[i]<e)r=i+1;else if(l[i]>e)t=i-1;else return i;return n}function Kc({lenses:l,projects:e,publications:r}){const t=l,i=t.filter(v=>v.type!=="redirect"),n=e.map(v=>({...v,lenses:v.lenses?.map(M=>M.toLowerCase())??[]}));let s=0;const o={isAnimating:!1},u=document.getElementById("lens-rail-viewport"),f=document.getElementById("lens-rail"),c=document.getElementById("content-display");let h=[],d=[],a=0,_;function p(v){const M=document.createElement("button");return M.className="pill px-5 py-2 text-sm font-medium rounded-full transition-opacity duration-300 bg-gray-800/50 text-gray-300 hover:opacity-85 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-gray-400",M.dataset.lensId=v.id,M.innerHTML=`<span class="relative z-10">${v.name}</span><div class="pill-glint"></div><div class="pill-border-trace"></div>`,M.addEventListener("click",()=>D(M)),M}function y(){f.innerHTML="",[...i,...i,...i].forEach(v=>f.appendChild(p(v))),h=Mt.utils.toArray("#lens-rail .pill")}function P(v){s=v%i.length,h.forEach((M,R)=>{const I=R%i.length===s;M.classList.toggle("unfocused",!I),M.classList.toggle("bg-gray-700",I),M.classList.toggle("text-white",I)})}function D(v){if(o.isAnimating||_&&_.isDragging)return;o.isAnimating=!0;const M=h.indexOf(v),R=v.dataset.lensId;t.find(Y=>Y.id===R);const I=M%i.length;Mt.killTweensOf(f);const N=a-d[M];Mt.to(f,{x:N,duration:.6,ease:"power1.out",onComplete:()=>{s!==I?(P(I),O(v),Mt.to(c,{opacity:0,duration:.2,onComplete:()=>{E(R),Mt.to(c,{opacity:1,duration:.3,onComplete:()=>o.isAnimating=!1})}})):(O(v),o.isAnimating=!1)}})}function O(v){const M=v.querySelector(".pill-glint"),R=v.querySelector(".pill-border-trace");M&&Mt.fromTo(M,{transform:"skewX(-25deg) translateX(-250%)"},{transform:"skewX(-25deg) translateX(350%)",duration:.8,ease:"power2.out"}),R&&Mt.fromTo(R,{opacity:0,"--angle":"90deg"},{opacity:1,"--angle":"450deg",duration:.6,ease:"power2.inOut",onComplete:()=>{Mt.to(R,{opacity:0,duration:.3})}})}const k=hl(()=>{const v=d[i.length+s];isNaN(v)||Mt.to(f,{x:a-v,duration:.4,ease:"power2.out"})},100);function b(){_&&_.kill();const v=f.scrollWidth/3;_=nn.create(f,{type:"x",edgeResistance:.65,inertia:!0,onDragStart:()=>Mt.killTweensOf(f),bounds:{minX:-v*2,maxX:-v},snap:{x:M=>{const R=M*-1+a,I=qc(d,R);return a-d[I]}},onDragEnd:k,onThrowComplete:k})[0]}function E(v){const M=window.matchMedia("(prefers-reduced-motion: reduce)").matches,R=n.filter(N=>N.lenses.includes(v)),I=document.getElementById("work-live-region");if(I){const N=R.length,Y=t.find(G=>G.id===v)?.name??v;I.textContent=`Showing ${N} project${N!==1?"s":""} for ${Y}.`}if(R.length===0){c.innerHTML='<div class="empty-state"><h3>No projects found.</h3></div>';return}c.innerHTML=`<div class="project-grid" role="list">${R.map(N=>{const Y=N.tags?.length?`<div class="project-card-tags">${N.tags.map(G=>`<span>#${G}</span>`).join("")}</div>`:"";return`<a href="#" class="project-card-wrapper" role="listitem" aria-label="${N.title}"><article class="project-card">${Y}<div class="project-card-thumbnail" role="img" aria-label="Thumbnail for ${N.title}"><img src="${N.cover}" alt="Cover image for ${N.title}" loading="lazy" decoding="async" class="w-full h-full object-cover"></div><div class="project-card-content"><h3 class="project-card-title">${N.title}</h3><p class="project-card-description">${N.description}</p></div></article></a>`}).join("")}</div>`,M||Mt.fromTo(c.querySelectorAll(".project-card-wrapper"),{opacity:0,y:15},{opacity:1,y:0,stagger:.05,duration:.4,ease:"power2.out"})}function T(){y(),h.length&&(a=u.offsetWidth/2,d=h.map(v=>v.offsetLeft+v.offsetWidth/2),P(s),E(i[s].id),b(),Mt.set(f,{x:a-d[i.length+s]}),window.addEventListener("resize",hl(()=>{if(a=u.offsetWidth/2,d=h.map(v=>v.offsetLeft+v.offsetWidth/2),_){const v=f.scrollWidth/3;_.applyBounds({minX:-v*2,maxX:-v})}k()},200)))}T()}const Qc=document.getElementById("work-page-container"),jc=JSON.parse(Qc.dataset.pageData);Kc(jc);
