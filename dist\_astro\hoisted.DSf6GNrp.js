function nn(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function Df(s,t){s.prototype=Object.create(t.prototype),s.prototype.constructor=s,s.__proto__=t}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: <PERSON>, <EMAIL>
*/var di={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Qs={duration:.5,overwrite:!1,delay:0},xc,ve,zt,Gi=1e8,Ee=1/Gi,Al=Math.PI*2,ap=Al/4,lp=0,Of=Math.sqrt,cp=Math.cos,hp=Math.sin,pe=function(t){return typeof t=="string"},Ut=function(t){return typeof t=="function"},pn=function(t){return typeof t=="number"},yc=function(t){return typeof t>"u"},Qi=function(t){return typeof t=="object"},qe=function(t){return t!==!1},vc=function(){return typeof window<"u"},Mo=function(t){return Ut(t)||pe(t)},Af=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},Le=Array.isArray,El=/(?:-?\.?\d|\.)+/gi,Ef=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,Bs=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,qa=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,Rf=/[+-]=-?[.\d]+/,Lf=/[^,'"\[\]\s]+/gi,up=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,Ht,Yi,Rl,wc,gi={},da={},Ff,If=function(t){return(da=Zs(t,gi))&&Je},Sc=function(t,e){return console.warn("Invalid property",t,"set to",e,"Missing plugin? gsap.registerPlugin()")},to=function(t,e){return!e&&console.warn(t)},zf=function(t,e){return t&&(gi[t]=e)&&da&&(da[t]=e)||gi},eo=function(){return 0},fp={suppressEvents:!0,isStart:!0,kill:!1},Jo={suppressEvents:!0,kill:!1},dp={suppressEvents:!0},Mc={},Rn=[],Ll={},Bf,ai={},Ka={},dh=30,ta=[],kc="",Tc=function(t){var e=t[0],i,n;if(Qi(e)||Ut(e)||(t=[t]),!(i=(e._gsap||{}).harness)){for(n=ta.length;n--&&!ta[n].targetTest(e););i=ta[n]}for(n=t.length;n--;)t[n]&&(t[n]._gsap||(t[n]._gsap=new ld(t[n],i)))||t.splice(n,1);return t},ls=function(t){return t._gsap||Tc(Ti(t))[0]._gsap},Nf=function(t,e,i){return(i=t[e])&&Ut(i)?t[e]():yc(i)&&t.getAttribute&&t.getAttribute(e)||i},Ke=function(t,e){return(t=t.split(",")).forEach(e)||t},Qt=function(t){return Math.round(t*1e5)/1e5||0},se=function(t){return Math.round(t*1e7)/1e7||0},Vs=function(t,e){var i=e.charAt(0),n=parseFloat(e.substr(2));return t=parseFloat(t),i==="+"?t+n:i==="-"?t-n:i==="*"?t*n:t/n},gp=function(t,e){for(var i=e.length,n=0;t.indexOf(e[n])<0&&++n<i;);return n<i},ga=function(){var t=Rn.length,e=Rn.slice(0),i,n;for(Ll={},Rn.length=0,i=0;i<t;i++)n=e[i],n&&n._lazy&&(n.render(n._lazy[0],n._lazy[1],!0)._lazy=0)},Pc=function(t){return!!(t._initted||t._startAt||t.add)},Wf=function(t,e,i,n){Rn.length&&!ve&&ga(),t.render(e,i,!!(ve&&e<0&&Pc(t))),Rn.length&&!ve&&ga()},Vf=function(t){var e=parseFloat(t);return(e||e===0)&&(t+"").match(Lf).length<2?e:pe(t)?t.trim():t},Hf=function(t){return t},pi=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},pp=function(t){return function(e,i){for(var n in i)n in e||n==="duration"&&t||n==="ease"||(e[n]=i[n])}},Zs=function(t,e){for(var i in e)t[i]=e[i];return t},gh=function s(t,e){for(var i in e)i!=="__proto__"&&i!=="constructor"&&i!=="prototype"&&(t[i]=Qi(e[i])?s(t[i]||(t[i]={}),e[i]):e[i]);return t},pa=function(t,e){var i={},n;for(n in t)n in e||(i[n]=t[n]);return i},Fr=function(t){var e=t.parent||Ht,i=t.keyframes?pp(Le(t.keyframes)):pi;if(qe(t.inherit))for(;e;)i(t,e.vars.defaults),e=e.parent||e._dp;return t},_p=function(t,e){for(var i=t.length,n=i===e.length;n&&i--&&t[i]===e[i];);return i<0},Yf=function(t,e,i,n,r){var o=t[n],a;if(r)for(a=e[r];o&&o[r]>a;)o=o._prev;return o?(e._next=o._next,o._next=e):(e._next=t[i],t[i]=e),e._next?e._next._prev=e:t[n]=e,e._prev=o,e.parent=e._dp=t,e},Fa=function(t,e,i,n){i===void 0&&(i="_first"),n===void 0&&(n="_last");var r=e._prev,o=e._next;r?r._next=o:t[i]===e&&(t[i]=o),o?o._prev=r:t[n]===e&&(t[n]=r),e._next=e._prev=e.parent=null},zn=function(t,e){t.parent&&(!e||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},cs=function(t,e){if(t&&(!e||e._end>t._dur||e._start<0))for(var i=t;i;)i._dirty=1,i=i.parent;return t},mp=function(t){for(var e=t.parent;e&&e.parent;)e._dirty=1,e.totalDuration(),e=e.parent;return t},Fl=function(t,e,i,n){return t._startAt&&(ve?t._startAt.revert(Jo):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(e,!0,n))},bp=function s(t){return!t||t._ts&&s(t.parent)},ph=function(t){return t._repeat?Js(t._tTime,t=t.duration()+t._rDelay)*t:0},Js=function(t,e){var i=Math.floor(t=se(t/e));return t&&i===t?i-1:i},_a=function(t,e){return(t-e._start)*e._ts+(e._ts>=0?0:e._dirty?e.totalDuration():e._tDur)},Ia=function(t){return t._end=se(t._start+(t._tDur/Math.abs(t._ts||t._rts||Ee)||0))},za=function(t,e){var i=t._dp;return i&&i.smoothChildTiming&&t._ts&&(t._start=se(i._time-(t._ts>0?e/t._ts:((t._dirty?t.totalDuration():t._tDur)-e)/-t._ts)),Ia(t),i._dirty||cs(i,t)),t},$f=function(t,e){var i;if((e._time||!e._dur&&e._initted||e._start<t._time&&(e._dur||!e.add))&&(i=_a(t.rawTime(),e),(!e._dur||xo(0,e.totalDuration(),i)-e._tTime>Ee)&&e.render(i,!0)),cs(t,e)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(i=t;i._dp;)i.rawTime()>=0&&i.totalTime(i._tTime),i=i._dp;t._zTime=-1e-8}},Xi=function(t,e,i,n){return e.parent&&zn(e),e._start=se((pn(i)?i:i||t!==Ht?Si(t,i,e):t._time)+e._delay),e._end=se(e._start+(e.totalDuration()/Math.abs(e.timeScale())||0)),Yf(t,e,"_first","_last",t._sort?"_start":0),Il(e)||(t._recent=e),n||$f(t,e),t._ts<0&&za(t,t._tTime),t},Xf=function(t,e){return(gi.ScrollTrigger||Sc("scrollTrigger",e))&&gi.ScrollTrigger.create(e,t)},jf=function(t,e,i,n,r){if(Dc(t,e,r),!t._initted)return 1;if(!i&&t._pt&&!ve&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&Bf!==ci.frame)return Rn.push(t),t._lazy=[r,n],1},xp=function s(t){var e=t.parent;return e&&e._ts&&e._initted&&!e._lock&&(e.rawTime()<0||s(e))},Il=function(t){var e=t.data;return e==="isFromStart"||e==="isStart"},yp=function(t,e,i,n){var r=t.ratio,o=e<0||!e&&(!t._start&&xp(t)&&!(!t._initted&&Il(t))||(t._ts<0||t._dp._ts<0)&&!Il(t))?0:1,a=t._rDelay,l=0,c,h,f;if(a&&t._repeat&&(l=xo(0,t._tDur,e),h=Js(l,a),t._yoyo&&h&1&&(o=1-o),h!==Js(t._tTime,a)&&(r=1-o,t.vars.repeatRefresh&&t._initted&&t.invalidate())),o!==r||ve||n||t._zTime===Ee||!e&&t._zTime){if(!t._initted&&jf(t,e,n,i,l))return;for(f=t._zTime,t._zTime=e||(i?Ee:0),i||(i=e&&!f),t.ratio=o,t._from&&(o=1-o),t._time=0,t._tTime=l,c=t._pt;c;)c.r(o,c.d),c=c._next;e<0&&Fl(t,e,i,!0),t._onUpdate&&!i&&fi(t,"onUpdate"),l&&t._repeat&&!i&&t.parent&&fi(t,"onRepeat"),(e>=t._tDur||e<0)&&t.ratio===o&&(o&&zn(t,1),!i&&!ve&&(fi(t,o?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=e)},vp=function(t,e,i){var n;if(i>e)for(n=t._first;n&&n._start<=i;){if(n.data==="isPause"&&n._start>e)return n;n=n._next}else for(n=t._last;n&&n._start>=i;){if(n.data==="isPause"&&n._start<e)return n;n=n._prev}},tr=function(t,e,i,n){var r=t._repeat,o=se(e)||0,a=t._tTime/t._tDur;return a&&!n&&(t._time*=o/t._dur),t._dur=o,t._tDur=r?r<0?1e10:se(o*(r+1)+t._rDelay*r):o,a>0&&!n&&za(t,t._tTime=t._tDur*a),t.parent&&Ia(t),i||cs(t.parent,t),t},_h=function(t){return t instanceof He?cs(t):tr(t,t._dur)},wp={_start:0,endTime:eo,totalDuration:eo},Si=function s(t,e,i){var n=t.labels,r=t._recent||wp,o=t.duration()>=Gi?r.endTime(!1):t._dur,a,l,c;return pe(e)&&(isNaN(e)||e in n)?(l=e.charAt(0),c=e.substr(-1)==="%",a=e.indexOf("="),l==="<"||l===">"?(a>=0&&(e=e.replace(/=/,"")),(l==="<"?r._start:r.endTime(r._repeat>=0))+(parseFloat(e.substr(1))||0)*(c?(a<0?r:i).totalDuration()/100:1)):a<0?(e in n||(n[e]=o),n[e]):(l=parseFloat(e.charAt(a-1)+e.substr(a+1)),c&&i&&(l=l/100*(Le(i)?i[0]:i).totalDuration()),a>1?s(t,e.substr(0,a-1),i)+l:o+l)):e==null?o:+e},Ir=function(t,e,i){var n=pn(e[1]),r=(n?2:1)+(t<2?0:1),o=e[r],a,l;if(n&&(o.duration=e[1]),o.parent=i,t){for(a=o,l=i;l&&!("immediateRender"in a);)a=l.vars.defaults||{},l=qe(l.vars.inherit)&&l.parent;o.immediateRender=qe(a.immediateRender),t<2?o.runBackwards=1:o.startAt=e[r-1]}return new ne(e[0],o,e[r+1])},Hn=function(t,e){return t||t===0?e(t):e},xo=function(t,e,i){return i<t?t:i>e?e:i},Oe=function(t,e){return!pe(t)||!(e=up.exec(t))?"":e[1]},Sp=function(t,e,i){return Hn(i,function(n){return xo(t,e,n)})},zl=[].slice,Uf=function(t,e){return t&&Qi(t)&&"length"in t&&(!e&&!t.length||t.length-1 in t&&Qi(t[0]))&&!t.nodeType&&t!==Yi},Mp=function(t,e,i){return i===void 0&&(i=[]),t.forEach(function(n){var r;return pe(n)&&!e||Uf(n,1)?(r=i).push.apply(r,Ti(n)):i.push(n)})||i},Ti=function(t,e,i){return zt&&!e&&zt.selector?zt.selector(t):pe(t)&&!i&&(Rl||!er())?zl.call((e||wc).querySelectorAll(t),0):Le(t)?Mp(t,i):Uf(t)?zl.call(t,0):t?[t]:[]},Bl=function(t){return t=Ti(t)[0]||to("Invalid scope")||{},function(e){var i=t.current||t.nativeElement||t;return Ti(e,i.querySelectorAll?i:i===t?to("Invalid scope")||wc.createElement("div"):t)}},Gf=function(t){return t.sort(function(){return .5-Math.random()})},qf=function(t){if(Ut(t))return t;var e=Qi(t)?t:{each:t},i=hs(e.ease),n=e.from||0,r=parseFloat(e.base)||0,o={},a=n>0&&n<1,l=isNaN(n)||a,c=e.axis,h=n,f=n;return pe(n)?h=f={center:.5,edges:.5,end:1}[n]||0:!a&&l&&(h=n[0],f=n[1]),function(d,u,p){var g=(p||e).length,_=o[g],m,b,w,y,x,k,v,M,C;if(!_){if(C=e.grid==="auto"?0:(e.grid||[1,Gi])[1],!C){for(v=-1e8;v<(v=p[C++].getBoundingClientRect().left)&&C<g;);C<g&&C--}for(_=o[g]=[],m=l?Math.min(C,g)*h-.5:n%C,b=C===Gi?0:l?g*f/C-.5:n/C|0,v=0,M=Gi,k=0;k<g;k++)w=k%C-m,y=b-(k/C|0),_[k]=x=c?Math.abs(c==="y"?y:w):Of(w*w+y*y),x>v&&(v=x),x<M&&(M=x);n==="random"&&Gf(_),_.max=v-M,_.min=M,_.v=g=(parseFloat(e.amount)||parseFloat(e.each)*(C>g?g-1:c?c==="y"?g/C:C:Math.max(C,g/C))||0)*(n==="edges"?-1:1),_.b=g<0?r-g:r,_.u=Oe(e.amount||e.each)||0,i=i&&g<0?rd(i):i}return g=(_[d]-_.min)/_.max||0,se(_.b+(i?i(g):g)*_.v)+_.u}},Nl=function(t){var e=Math.pow(10,((t+"").split(".")[1]||"").length);return function(i){var n=se(Math.round(parseFloat(i)/t)*t*e);return(n-n%1)/e+(pn(i)?0:Oe(i))}},Kf=function(t,e){var i=Le(t),n,r;return!i&&Qi(t)&&(n=i=t.radius||Gi,t.values?(t=Ti(t.values),(r=!pn(t[0]))&&(n*=n)):t=Nl(t.increment)),Hn(e,i?Ut(t)?function(o){return r=t(o),Math.abs(r-o)<=n?r:o}:function(o){for(var a=parseFloat(r?o.x:o),l=parseFloat(r?o.y:0),c=Gi,h=0,f=t.length,d,u;f--;)r?(d=t[f].x-a,u=t[f].y-l,d=d*d+u*u):d=Math.abs(t[f]-a),d<c&&(c=d,h=f);return h=!n||c<=n?t[h]:o,r||h===o||pn(o)?h:h+Oe(o)}:Nl(t))},Qf=function(t,e,i,n){return Hn(Le(t)?!e:i===!0?!!(i=0):!n,function(){return Le(t)?t[~~(Math.random()*t.length)]:(i=i||1e-5)&&(n=i<1?Math.pow(10,(i+"").length-2):1)&&Math.floor(Math.round((t-i/2+Math.random()*(e-t+i*.99))/i)*i*n)/n})},kp=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(n){return e.reduce(function(r,o){return o(r)},n)}},Tp=function(t,e){return function(i){return t(parseFloat(i))+(e||Oe(i))}},Pp=function(t,e,i){return Jf(t,e,0,1,i)},Zf=function(t,e,i){return Hn(i,function(n){return t[~~e(n)]})},Cp=function s(t,e,i){var n=e-t;return Le(t)?Zf(t,s(0,t.length),e):Hn(i,function(r){return(n+(r-t)%n)%n+t})},Dp=function s(t,e,i){var n=e-t,r=n*2;return Le(t)?Zf(t,s(0,t.length-1),e):Hn(i,function(o){return o=(r+(o-t)%r)%r||0,t+(o>n?r-o:o)})},io=function(t){for(var e=0,i="",n,r,o,a;~(n=t.indexOf("random(",e));)o=t.indexOf(")",n),a=t.charAt(n+7)==="[",r=t.substr(n+7,o-n-7).match(a?Lf:El),i+=t.substr(e,n-e)+Qf(a?r:+r[0],a?0:+r[1],+r[2]||1e-5),e=o+1;return i+t.substr(e,t.length-e)},Jf=function(t,e,i,n,r){var o=e-t,a=n-i;return Hn(r,function(l){return i+((l-t)/o*a||0)})},Op=function s(t,e,i,n){var r=isNaN(t+e)?0:function(u){return(1-u)*t+u*e};if(!r){var o=pe(t),a={},l,c,h,f,d;if(i===!0&&(n=1)&&(i=null),o)t={p:t},e={p:e};else if(Le(t)&&!Le(e)){for(h=[],f=t.length,d=f-2,c=1;c<f;c++)h.push(s(t[c-1],t[c]));f--,r=function(p){p*=f;var g=Math.min(d,~~p);return h[g](p-g)},i=e}else n||(t=Zs(Le(t)?[]:{},t));if(!h){for(l in e)Cc.call(a,t,l,"get",e[l]);r=function(p){return Ec(p,a)||(o?t.p:t)}}}return Hn(i,r)},mh=function(t,e,i){var n=t.labels,r=Gi,o,a,l;for(o in n)a=n[o]-e,a<0==!!i&&a&&r>(a=Math.abs(a))&&(l=o,r=a);return l},fi=function(t,e,i){var n=t.vars,r=n[e],o=zt,a=t._ctx,l,c,h;if(r)return l=n[e+"Params"],c=n.callbackScope||t,i&&Rn.length&&ga(),a&&(zt=a),h=l?r.apply(c,l):r.call(c),zt=o,h},yr=function(t){return zn(t),t.scrollTrigger&&t.scrollTrigger.kill(!!ve),t.progress()<1&&fi(t,"onInterrupt"),t},Ns,td=[],ed=function(t){if(t)if(t=!t.name&&t.default||t,vc()||t.headless){var e=t.name,i=Ut(t),n=e&&!i&&t.init?function(){this._props=[]}:t,r={init:eo,render:Ec,add:Cc,kill:jp,modifier:Xp,rawVars:0},o={targetTest:0,get:0,getSetter:Ac,aliases:{},register:0};if(er(),t!==n){if(ai[e])return;pi(n,pi(pa(t,r),o)),Zs(n.prototype,Zs(r,pa(t,o))),ai[n.prop=e]=n,t.targetTest&&(ta.push(n),Mc[e]=1),e=(e==="css"?"CSS":e.charAt(0).toUpperCase()+e.substr(1))+"Plugin"}zf(e,n),t.register&&t.register(Je,n,Qe)}else td.push(t)},Ct=255,vr={aqua:[0,Ct,Ct],lime:[0,Ct,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,Ct],navy:[0,0,128],white:[Ct,Ct,Ct],olive:[128,128,0],yellow:[Ct,Ct,0],orange:[Ct,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[Ct,0,0],pink:[Ct,192,203],cyan:[0,Ct,Ct],transparent:[Ct,Ct,Ct,0]},Qa=function(t,e,i){return t+=t<0?1:t>1?-1:0,(t*6<1?e+(i-e)*t*6:t<.5?i:t*3<2?e+(i-e)*(2/3-t)*6:e)*Ct+.5|0},id=function(t,e,i){var n=t?pn(t)?[t>>16,t>>8&Ct,t&Ct]:0:vr.black,r,o,a,l,c,h,f,d,u,p;if(!n){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),vr[t])n=vr[t];else if(t.charAt(0)==="#"){if(t.length<6&&(r=t.charAt(1),o=t.charAt(2),a=t.charAt(3),t="#"+r+r+o+o+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return n=parseInt(t.substr(1,6),16),[n>>16,n>>8&Ct,n&Ct,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),n=[t>>16,t>>8&Ct,t&Ct]}else if(t.substr(0,3)==="hsl"){if(n=p=t.match(El),!e)l=+n[0]%360/360,c=+n[1]/100,h=+n[2]/100,o=h<=.5?h*(c+1):h+c-h*c,r=h*2-o,n.length>3&&(n[3]*=1),n[0]=Qa(l+1/3,r,o),n[1]=Qa(l,r,o),n[2]=Qa(l-1/3,r,o);else if(~t.indexOf("="))return n=t.match(Ef),i&&n.length<4&&(n[3]=1),n}else n=t.match(El)||vr.transparent;n=n.map(Number)}return e&&!p&&(r=n[0]/Ct,o=n[1]/Ct,a=n[2]/Ct,f=Math.max(r,o,a),d=Math.min(r,o,a),h=(f+d)/2,f===d?l=c=0:(u=f-d,c=h>.5?u/(2-f-d):u/(f+d),l=f===r?(o-a)/u+(o<a?6:0):f===o?(a-r)/u+2:(r-o)/u+4,l*=60),n[0]=~~(l+.5),n[1]=~~(c*100+.5),n[2]=~~(h*100+.5)),i&&n.length<4&&(n[3]=1),n},nd=function(t){var e=[],i=[],n=-1;return t.split(Ln).forEach(function(r){var o=r.match(Bs)||[];e.push.apply(e,o),i.push(n+=o.length+1)}),e.c=i,e},bh=function(t,e,i){var n="",r=(t+n).match(Ln),o=e?"hsla(":"rgba(",a=0,l,c,h,f;if(!r)return t;if(r=r.map(function(d){return(d=id(d,e,1))&&o+(e?d[0]+","+d[1]+"%,"+d[2]+"%,"+d[3]:d.join(","))+")"}),i&&(h=nd(t),l=i.c,l.join(n)!==h.c.join(n)))for(c=t.replace(Ln,"1").split(Bs),f=c.length-1;a<f;a++)n+=c[a]+(~l.indexOf(a)?r.shift()||o+"0,0,0,0)":(h.length?h:r.length?r:i).shift());if(!c)for(c=t.split(Ln),f=c.length-1;a<f;a++)n+=c[a]+r[a];return n+c[f]},Ln=function(){var s="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in vr)s+="|"+t+"\\b";return new RegExp(s+")","gi")}(),Ap=/hsl[a]?\(/,sd=function(t){var e=t.join(" "),i;if(Ln.lastIndex=0,Ln.test(e))return i=Ap.test(e),t[1]=bh(t[1],i),t[0]=bh(t[0],i,nd(t[1])),!0},no,ci=function(){var s=Date.now,t=500,e=33,i=s(),n=i,r=1e3/240,o=r,a=[],l,c,h,f,d,u,p=function g(_){var m=s()-n,b=_===!0,w,y,x,k;if((m>t||m<0)&&(i+=m-e),n+=m,x=n-i,w=x-o,(w>0||b)&&(k=++f.frame,d=x-f.time*1e3,f.time=x=x/1e3,o+=w+(w>=r?4:r-w),y=1),b||(l=c(g)),y)for(u=0;u<a.length;u++)a[u](x,d,k,_)};return f={time:0,frame:0,tick:function(){p(!0)},deltaRatio:function(_){return d/(1e3/(_||60))},wake:function(){Ff&&(!Rl&&vc()&&(Yi=Rl=window,wc=Yi.document||{},gi.gsap=Je,(Yi.gsapVersions||(Yi.gsapVersions=[])).push(Je.version),If(da||Yi.GreenSockGlobals||!Yi.gsap&&Yi||{}),td.forEach(ed)),h=typeof requestAnimationFrame<"u"&&requestAnimationFrame,l&&f.sleep(),c=h||function(_){return setTimeout(_,o-f.time*1e3+1|0)},no=1,p(2))},sleep:function(){(h?cancelAnimationFrame:clearTimeout)(l),no=0,c=eo},lagSmoothing:function(_,m){t=_||1/0,e=Math.min(m||33,t)},fps:function(_){r=1e3/(_||240),o=f.time*1e3+r},add:function(_,m,b){var w=m?function(y,x,k,v){_(y,x,k,v),f.remove(w)}:_;return f.remove(_),a[b?"unshift":"push"](w),er(),w},remove:function(_,m){~(m=a.indexOf(_))&&a.splice(m,1)&&u>=m&&u--},_listeners:a},f}(),er=function(){return!no&&ci.wake()},bt={},Ep=/^[\d.\-M][\d.\-,\s]/,Rp=/["']/g,Lp=function(t){for(var e={},i=t.substr(1,t.length-3).split(":"),n=i[0],r=1,o=i.length,a,l,c;r<o;r++)l=i[r],a=r!==o-1?l.lastIndexOf(","):l.length,c=l.substr(0,a),e[n]=isNaN(c)?c.replace(Rp,"").trim():+c,n=l.substr(a+1).trim();return e},Fp=function(t){var e=t.indexOf("(")+1,i=t.indexOf(")"),n=t.indexOf("(",e);return t.substring(e,~n&&n<i?t.indexOf(")",i+1):i)},Ip=function(t){var e=(t+"").split("("),i=bt[e[0]];return i&&e.length>1&&i.config?i.config.apply(null,~t.indexOf("{")?[Lp(e[1])]:Fp(t).split(",").map(Vf)):bt._CE&&Ep.test(t)?bt._CE("",t):i},rd=function(t){return function(e){return 1-t(1-e)}},od=function s(t,e){for(var i=t._first,n;i;)i instanceof He?s(i,e):i.vars.yoyoEase&&(!i._yoyo||!i._repeat)&&i._yoyo!==e&&(i.timeline?s(i.timeline,e):(n=i._ease,i._ease=i._yEase,i._yEase=n,i._yoyo=e)),i=i._next},hs=function(t,e){return t&&(Ut(t)?t:bt[t]||Ip(t))||e},Ts=function(t,e,i,n){i===void 0&&(i=function(l){return 1-e(1-l)}),n===void 0&&(n=function(l){return l<.5?e(l*2)/2:1-e((1-l)*2)/2});var r={easeIn:e,easeOut:i,easeInOut:n},o;return Ke(t,function(a){bt[a]=gi[a]=r,bt[o=a.toLowerCase()]=i;for(var l in r)bt[o+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=bt[a+"."+l]=r[l]}),r},ad=function(t){return function(e){return e<.5?(1-t(1-e*2))/2:.5+t((e-.5)*2)/2}},Za=function s(t,e,i){var n=e>=1?e:1,r=(i||(t?.3:.45))/(e<1?e:1),o=r/Al*(Math.asin(1/n)||0),a=function(h){return h===1?1:n*Math.pow(2,-10*h)*hp((h-o)*r)+1},l=t==="out"?a:t==="in"?function(c){return 1-a(1-c)}:ad(a);return r=Al/r,l.config=function(c,h){return s(t,c,h)},l},Ja=function s(t,e){e===void 0&&(e=1.70158);var i=function(o){return o?--o*o*((e+1)*o+e)+1:0},n=t==="out"?i:t==="in"?function(r){return 1-i(1-r)}:ad(i);return n.config=function(r){return s(t,r)},n};Ke("Linear,Quad,Cubic,Quart,Quint,Strong",function(s,t){var e=t<5?t+1:t;Ts(s+",Power"+(e-1),t?function(i){return Math.pow(i,e)}:function(i){return i},function(i){return 1-Math.pow(1-i,e)},function(i){return i<.5?Math.pow(i*2,e)/2:1-Math.pow((1-i)*2,e)/2})});bt.Linear.easeNone=bt.none=bt.Linear.easeIn;Ts("Elastic",Za("in"),Za("out"),Za());(function(s,t){var e=1/t,i=2*e,n=2.5*e,r=function(a){return a<e?s*a*a:a<i?s*Math.pow(a-1.5/t,2)+.75:a<n?s*(a-=2.25/t)*a+.9375:s*Math.pow(a-2.625/t,2)+.984375};Ts("Bounce",function(o){return 1-r(1-o)},r)})(7.5625,2.75);Ts("Expo",function(s){return Math.pow(2,10*(s-1))*s+s*s*s*s*s*s*(1-s)});Ts("Circ",function(s){return-(Of(1-s*s)-1)});Ts("Sine",function(s){return s===1?1:-cp(s*ap)+1});Ts("Back",Ja("in"),Ja("out"),Ja());bt.SteppedEase=bt.steps=gi.SteppedEase={config:function(t,e){t===void 0&&(t=1);var i=1/t,n=t+(e?0:1),r=e?1:0,o=1-Ee;return function(a){return((n*xo(0,o,a)|0)+r)*i}}};Qs.ease=bt["quad.out"];Ke("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(s){return kc+=s+","+s+"Params,"});var ld=function(t,e){this.id=lp++,t._gsap=this,this.target=t,this.harness=e,this.get=e?e.get:Nf,this.set=e?e.getSetter:Ac},so=function(){function s(e){this.vars=e,this._delay=+e.delay||0,(this._repeat=e.repeat===1/0?-2:e.repeat||0)&&(this._rDelay=e.repeatDelay||0,this._yoyo=!!e.yoyo||!!e.yoyoEase),this._ts=1,tr(this,+e.duration,1,1),this.data=e.data,zt&&(this._ctx=zt,zt.data.push(this)),no||ci.wake()}var t=s.prototype;return t.delay=function(i){return i||i===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+i-this._delay),this._delay=i,this):this._delay},t.duration=function(i){return arguments.length?this.totalDuration(this._repeat>0?i+(i+this._rDelay)*this._repeat:i):this.totalDuration()&&this._dur},t.totalDuration=function(i){return arguments.length?(this._dirty=0,tr(this,this._repeat<0?i:(i-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(i,n){if(er(),!arguments.length)return this._tTime;var r=this._dp;if(r&&r.smoothChildTiming&&this._ts){for(za(this,i),!r._dp||r.parent||$f(r,this);r&&r.parent;)r.parent._time!==r._start+(r._ts>=0?r._tTime/r._ts:(r.totalDuration()-r._tTime)/-r._ts)&&r.totalTime(r._tTime,!0),r=r.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&i<this._tDur||this._ts<0&&i>0||!this._tDur&&!i)&&Xi(this._dp,this,this._start-this._delay)}return(this._tTime!==i||!this._dur&&!n||this._initted&&Math.abs(this._zTime)===Ee||!i&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=i),Wf(this,i,n)),this},t.time=function(i,n){return arguments.length?this.totalTime(Math.min(this.totalDuration(),i+ph(this))%(this._dur+this._rDelay)||(i?this._dur:0),n):this._time},t.totalProgress=function(i,n){return arguments.length?this.totalTime(this.totalDuration()*i,n):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},t.progress=function(i,n){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-i:i)+ph(this),n):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(i,n){var r=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(i-1)*r,n):this._repeat?Js(this._tTime,r)+1:1},t.timeScale=function(i,n){if(!arguments.length)return this._rts===-1e-8?0:this._rts;if(this._rts===i)return this;var r=this.parent&&this._ts?_a(this.parent._time,this):this._tTime;return this._rts=+i||0,this._ts=this._ps||i===-1e-8?0:this._rts,this.totalTime(xo(-Math.abs(this._delay),this.totalDuration(),r),n!==!1),Ia(this),mp(this)},t.paused=function(i){return arguments.length?(this._ps!==i&&(this._ps=i,i?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(er(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==Ee&&(this._tTime-=Ee)))),this):this._ps},t.startTime=function(i){if(arguments.length){this._start=i;var n=this.parent||this._dp;return n&&(n._sort||!this.parent)&&Xi(n,this,i-this._delay),this}return this._start},t.endTime=function(i){return this._start+(qe(i)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(i){var n=this.parent||this._dp;return n?i&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?_a(n.rawTime(i),this):this._tTime:this._tTime},t.revert=function(i){i===void 0&&(i=dp);var n=ve;return ve=i,Pc(this)&&(this.timeline&&this.timeline.revert(i),this.totalTime(-.01,i.suppressEvents)),this.data!=="nested"&&i.kill!==!1&&this.kill(),ve=n,this},t.globalTime=function(i){for(var n=this,r=arguments.length?i:n.rawTime();n;)r=n._start+r/(Math.abs(n._ts)||1),n=n._dp;return!this.parent&&this._sat?this._sat.globalTime(i):r},t.repeat=function(i){return arguments.length?(this._repeat=i===1/0?-2:i,_h(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(i){if(arguments.length){var n=this._time;return this._rDelay=i,_h(this),n?this.time(n):this}return this._rDelay},t.yoyo=function(i){return arguments.length?(this._yoyo=i,this):this._yoyo},t.seek=function(i,n){return this.totalTime(Si(this,i),qe(n))},t.restart=function(i,n){return this.play().totalTime(i?-this._delay:0,qe(n)),this._dur||(this._zTime=-1e-8),this},t.play=function(i,n){return i!=null&&this.seek(i,n),this.reversed(!1).paused(!1)},t.reverse=function(i,n){return i!=null&&this.seek(i||this.totalDuration(),n),this.reversed(!0).paused(!1)},t.pause=function(i,n){return i!=null&&this.seek(i,n),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(i){return arguments.length?(!!i!==this.reversed()&&this.timeScale(-this._rts||(i?-1e-8:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-1e-8,this},t.isActive=function(){var i=this.parent||this._dp,n=this._start,r;return!!(!i||this._ts&&this._initted&&i.isActive()&&(r=i.rawTime(!0))>=n&&r<this.endTime(!0)-Ee)},t.eventCallback=function(i,n,r){var o=this.vars;return arguments.length>1?(n?(o[i]=n,r&&(o[i+"Params"]=r),i==="onUpdate"&&(this._onUpdate=n)):delete o[i],this):o[i]},t.then=function(i){var n=this;return new Promise(function(r){var o=Ut(i)?i:Hf,a=function(){var c=n.then;n.then=null,Ut(o)&&(o=o(n))&&(o.then||o===n)&&(n.then=c),r(o),n.then=c};n._initted&&n.totalProgress()===1&&n._ts>=0||!n._tTime&&n._ts<0?a():n._prom=a})},t.kill=function(){yr(this)},s}();pi(so.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-1e-8,_prom:0,_ps:!1,_rts:1});var He=function(s){Df(t,s);function t(i,n){var r;return i===void 0&&(i={}),r=s.call(this,i)||this,r.labels={},r.smoothChildTiming=!!i.smoothChildTiming,r.autoRemoveChildren=!!i.autoRemoveChildren,r._sort=qe(i.sortChildren),Ht&&Xi(i.parent||Ht,nn(r),n),i.reversed&&r.reverse(),i.paused&&r.paused(!0),i.scrollTrigger&&Xf(nn(r),i.scrollTrigger),r}var e=t.prototype;return e.to=function(n,r,o){return Ir(0,arguments,this),this},e.from=function(n,r,o){return Ir(1,arguments,this),this},e.fromTo=function(n,r,o,a){return Ir(2,arguments,this),this},e.set=function(n,r,o){return r.duration=0,r.parent=this,Fr(r).repeatDelay||(r.repeat=0),r.immediateRender=!!r.immediateRender,new ne(n,r,Si(this,o),1),this},e.call=function(n,r,o){return Xi(this,ne.delayedCall(0,n,r),o)},e.staggerTo=function(n,r,o,a,l,c,h){return o.duration=r,o.stagger=o.stagger||a,o.onComplete=c,o.onCompleteParams=h,o.parent=this,new ne(n,o,Si(this,l)),this},e.staggerFrom=function(n,r,o,a,l,c,h){return o.runBackwards=1,Fr(o).immediateRender=qe(o.immediateRender),this.staggerTo(n,r,o,a,l,c,h)},e.staggerFromTo=function(n,r,o,a,l,c,h,f){return a.startAt=o,Fr(a).immediateRender=qe(a.immediateRender),this.staggerTo(n,r,a,l,c,h,f)},e.render=function(n,r,o){var a=this._time,l=this._dirty?this.totalDuration():this._tDur,c=this._dur,h=n<=0?0:se(n),f=this._zTime<0!=n<0&&(this._initted||!c),d,u,p,g,_,m,b,w,y,x,k,v;if(this!==Ht&&h>l&&n>=0&&(h=l),h!==this._tTime||o||f){if(a!==this._time&&c&&(h+=this._time-a,n+=this._time-a),d=h,y=this._start,w=this._ts,m=!w,f&&(c||(a=this._zTime),(n||!r)&&(this._zTime=n)),this._repeat){if(k=this._yoyo,_=c+this._rDelay,this._repeat<-1&&n<0)return this.totalTime(_*100+n,r,o);if(d=se(h%_),h===l?(g=this._repeat,d=c):(x=se(h/_),g=~~x,g&&g===x&&(d=c,g--),d>c&&(d=c)),x=Js(this._tTime,_),!a&&this._tTime&&x!==g&&this._tTime-x*_-this._dur<=0&&(x=g),k&&g&1&&(d=c-d,v=1),g!==x&&!this._lock){var M=k&&x&1,C=M===(k&&g&1);if(g<x&&(M=!M),a=M?0:h%c?c:h,this._lock=1,this.render(a||(v?0:se(g*_)),r,!c)._lock=0,this._tTime=h,!r&&this.parent&&fi(this,"onRepeat"),this.vars.repeatRefresh&&!v&&(this.invalidate()._lock=1),a&&a!==this._time||m!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(c=this._dur,l=this._tDur,C&&(this._lock=2,a=M?c:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!v&&this.invalidate()),this._lock=0,!this._ts&&!m)return this;od(this,v)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(b=vp(this,se(a),se(d)),b&&(h-=d-(d=b._start))),this._tTime=h,this._time=d,this._act=!w,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=n,a=0),!a&&h&&!r&&!x&&(fi(this,"onStart"),this._tTime!==h))return this;if(d>=a&&n>=0)for(u=this._first;u;){if(p=u._next,(u._act||d>=u._start)&&u._ts&&b!==u){if(u.parent!==this)return this.render(n,r,o);if(u.render(u._ts>0?(d-u._start)*u._ts:(u._dirty?u.totalDuration():u._tDur)+(d-u._start)*u._ts,r,o),d!==this._time||!this._ts&&!m){b=0,p&&(h+=this._zTime=-1e-8);break}}u=p}else{u=this._last;for(var L=n<0?n:d;u;){if(p=u._prev,(u._act||L<=u._end)&&u._ts&&b!==u){if(u.parent!==this)return this.render(n,r,o);if(u.render(u._ts>0?(L-u._start)*u._ts:(u._dirty?u.totalDuration():u._tDur)+(L-u._start)*u._ts,r,o||ve&&Pc(u)),d!==this._time||!this._ts&&!m){b=0,p&&(h+=this._zTime=L?-1e-8:Ee);break}}u=p}}if(b&&!r&&(this.pause(),b.render(d>=a?0:-1e-8)._zTime=d>=a?1:-1,this._ts))return this._start=y,Ia(this),this.render(n,r,o);this._onUpdate&&!r&&fi(this,"onUpdate",!0),(h===l&&this._tTime>=this.totalDuration()||!h&&a)&&(y===this._start||Math.abs(w)!==Math.abs(this._ts))&&(this._lock||((n||!c)&&(h===l&&this._ts>0||!h&&this._ts<0)&&zn(this,1),!r&&!(n<0&&!a)&&(h||a||!l)&&(fi(this,h===l&&n>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(h<l&&this.timeScale()>0)&&this._prom())))}return this},e.add=function(n,r){var o=this;if(pn(r)||(r=Si(this,r,n)),!(n instanceof so)){if(Le(n))return n.forEach(function(a){return o.add(a,r)}),this;if(pe(n))return this.addLabel(n,r);if(Ut(n))n=ne.delayedCall(0,n);else return this}return this!==n?Xi(this,n,r):this},e.getChildren=function(n,r,o,a){n===void 0&&(n=!0),r===void 0&&(r=!0),o===void 0&&(o=!0),a===void 0&&(a=-1e8);for(var l=[],c=this._first;c;)c._start>=a&&(c instanceof ne?r&&l.push(c):(o&&l.push(c),n&&l.push.apply(l,c.getChildren(!0,r,o)))),c=c._next;return l},e.getById=function(n){for(var r=this.getChildren(1,1,1),o=r.length;o--;)if(r[o].vars.id===n)return r[o]},e.remove=function(n){return pe(n)?this.removeLabel(n):Ut(n)?this.killTweensOf(n):(n.parent===this&&Fa(this,n),n===this._recent&&(this._recent=this._last),cs(this))},e.totalTime=function(n,r){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=se(ci.time-(this._ts>0?n/this._ts:(this.totalDuration()-n)/-this._ts))),s.prototype.totalTime.call(this,n,r),this._forcing=0,this):this._tTime},e.addLabel=function(n,r){return this.labels[n]=Si(this,r),this},e.removeLabel=function(n){return delete this.labels[n],this},e.addPause=function(n,r,o){var a=ne.delayedCall(0,r||eo,o);return a.data="isPause",this._hasPause=1,Xi(this,a,Si(this,n))},e.removePause=function(n){var r=this._first;for(n=Si(this,n);r;)r._start===n&&r.data==="isPause"&&zn(r),r=r._next},e.killTweensOf=function(n,r,o){for(var a=this.getTweensOf(n,o),l=a.length;l--;)Mn!==a[l]&&a[l].kill(n,r);return this},e.getTweensOf=function(n,r){for(var o=[],a=Ti(n),l=this._first,c=pn(r),h;l;)l instanceof ne?gp(l._targets,a)&&(c?(!Mn||l._initted&&l._ts)&&l.globalTime(0)<=r&&l.globalTime(l.totalDuration())>r:!r||l.isActive())&&o.push(l):(h=l.getTweensOf(a,r)).length&&o.push.apply(o,h),l=l._next;return o},e.tweenTo=function(n,r){r=r||{};var o=this,a=Si(o,n),l=r,c=l.startAt,h=l.onStart,f=l.onStartParams,d=l.immediateRender,u,p=ne.to(o,pi({ease:r.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:r.duration||Math.abs((a-(c&&"time"in c?c.time:o._time))/o.timeScale())||Ee,onStart:function(){if(o.pause(),!u){var _=r.duration||Math.abs((a-(c&&"time"in c?c.time:o._time))/o.timeScale());p._dur!==_&&tr(p,_,0,1).render(p._time,!0,!0),u=1}h&&h.apply(p,f||[])}},r));return d?p.render(0):p},e.tweenFromTo=function(n,r,o){return this.tweenTo(r,pi({startAt:{time:Si(this,n)}},o))},e.recent=function(){return this._recent},e.nextLabel=function(n){return n===void 0&&(n=this._time),mh(this,Si(this,n))},e.previousLabel=function(n){return n===void 0&&(n=this._time),mh(this,Si(this,n),1)},e.currentLabel=function(n){return arguments.length?this.seek(n,!0):this.previousLabel(this._time+Ee)},e.shiftChildren=function(n,r,o){o===void 0&&(o=0);for(var a=this._first,l=this.labels,c;a;)a._start>=o&&(a._start+=n,a._end+=n),a=a._next;if(r)for(c in l)l[c]>=o&&(l[c]+=n);return cs(this)},e.invalidate=function(n){var r=this._first;for(this._lock=0;r;)r.invalidate(n),r=r._next;return s.prototype.invalidate.call(this,n)},e.clear=function(n){n===void 0&&(n=!0);for(var r=this._first,o;r;)o=r._next,this.remove(r),r=o;return this._dp&&(this._time=this._tTime=this._pTime=0),n&&(this.labels={}),cs(this)},e.totalDuration=function(n){var r=0,o=this,a=o._last,l=Gi,c,h,f;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-n:n));if(o._dirty){for(f=o.parent;a;)c=a._prev,a._dirty&&a.totalDuration(),h=a._start,h>l&&o._sort&&a._ts&&!o._lock?(o._lock=1,Xi(o,a,h-a._delay,1)._lock=0):l=h,h<0&&a._ts&&(r-=h,(!f&&!o._dp||f&&f.smoothChildTiming)&&(o._start+=h/o._ts,o._time-=h,o._tTime-=h),o.shiftChildren(-h,!1,-1/0),l=0),a._end>r&&a._ts&&(r=a._end),a=c;tr(o,o===Ht&&o._time>r?o._time:r,1,1),o._dirty=0}return o._tDur},t.updateRoot=function(n){if(Ht._ts&&(Wf(Ht,_a(n,Ht)),Bf=ci.frame),ci.frame>=dh){dh+=di.autoSleep||120;var r=Ht._first;if((!r||!r._ts)&&di.autoSleep&&ci._listeners.length<2){for(;r&&!r._ts;)r=r._next;r||ci.sleep()}}},t}(so);pi(He.prototype,{_lock:0,_hasPause:0,_forcing:0});var zp=function(t,e,i,n,r,o,a){var l=new Qe(this._pt,t,e,0,1,gd,null,r),c=0,h=0,f,d,u,p,g,_,m,b;for(l.b=i,l.e=n,i+="",n+="",(m=~n.indexOf("random("))&&(n=io(n)),o&&(b=[i,n],o(b,t,e),i=b[0],n=b[1]),d=i.match(qa)||[];f=qa.exec(n);)p=f[0],g=n.substring(c,f.index),u?u=(u+1)%5:g.substr(-5)==="rgba("&&(u=1),p!==d[h++]&&(_=parseFloat(d[h-1])||0,l._pt={_next:l._pt,p:g||h===1?g:",",s:_,c:p.charAt(1)==="="?Vs(_,p)-_:parseFloat(p)-_,m:u&&u<4?Math.round:0},c=qa.lastIndex);return l.c=c<n.length?n.substring(c,n.length):"",l.fp=a,(Rf.test(n)||m)&&(l.e=0),this._pt=l,l},Cc=function(t,e,i,n,r,o,a,l,c,h){Ut(n)&&(n=n(r||0,t,o));var f=t[e],d=i!=="get"?i:Ut(f)?c?t[e.indexOf("set")||!Ut(t["get"+e.substr(3)])?e:"get"+e.substr(3)](c):t[e]():f,u=Ut(f)?c?Hp:fd:Oc,p;if(pe(n)&&(~n.indexOf("random(")&&(n=io(n)),n.charAt(1)==="="&&(p=Vs(d,n)+(Oe(d)||0),(p||p===0)&&(n=p))),!h||d!==n||Wl)return!isNaN(d*n)&&n!==""?(p=new Qe(this._pt,t,e,+d||0,n-(d||0),typeof f=="boolean"?$p:dd,0,u),c&&(p.fp=c),a&&p.modifier(a,this,t),this._pt=p):(!f&&!(e in t)&&Sc(e,n),zp.call(this,t,e,d,n,u,l||di.stringFilter,c))},Bp=function(t,e,i,n,r){if(Ut(t)&&(t=zr(t,r,e,i,n)),!Qi(t)||t.style&&t.nodeType||Le(t)||Af(t))return pe(t)?zr(t,r,e,i,n):t;var o={},a;for(a in t)o[a]=zr(t[a],r,e,i,n);return o},cd=function(t,e,i,n,r,o){var a,l,c,h;if(ai[t]&&(a=new ai[t]).init(r,a.rawVars?e[t]:Bp(e[t],n,r,o,i),i,n,o)!==!1&&(i._pt=l=new Qe(i._pt,r,t,0,1,a.render,a,0,a.priority),i!==Ns))for(c=i._ptLookup[i._targets.indexOf(r)],h=a._props.length;h--;)c[a._props[h]]=l;return a},Mn,Wl,Dc=function s(t,e,i){var n=t.vars,r=n.ease,o=n.startAt,a=n.immediateRender,l=n.lazy,c=n.onUpdate,h=n.runBackwards,f=n.yoyoEase,d=n.keyframes,u=n.autoRevert,p=t._dur,g=t._startAt,_=t._targets,m=t.parent,b=m&&m.data==="nested"?m.vars.targets:_,w=t._overwrite==="auto"&&!xc,y=t.timeline,x,k,v,M,C,L,D,O,F,Y,N,V,W;if(y&&(!d||!r)&&(r="none"),t._ease=hs(r,Qs.ease),t._yEase=f?rd(hs(f===!0?r:f,Qs.ease)):0,f&&t._yoyo&&!t._repeat&&(f=t._yEase,t._yEase=t._ease,t._ease=f),t._from=!y&&!!n.runBackwards,!y||d&&!n.stagger){if(O=_[0]?ls(_[0]).harness:0,V=O&&n[O.prop],x=pa(n,Mc),g&&(g._zTime<0&&g.progress(1),e<0&&h&&a&&!u?g.render(-1,!0):g.revert(h&&p?Jo:fp),g._lazy=0),o){if(zn(t._startAt=ne.set(_,pi({data:"isStart",overwrite:!1,parent:m,immediateRender:!0,lazy:!g&&qe(l),startAt:null,delay:0,onUpdate:c&&function(){return fi(t,"onUpdate")},stagger:0},o))),t._startAt._dp=0,t._startAt._sat=t,e<0&&(ve||!a&&!u)&&t._startAt.revert(Jo),a&&p&&e<=0&&i<=0){e&&(t._zTime=e);return}}else if(h&&p&&!g){if(e&&(a=!1),v=pi({overwrite:!1,data:"isFromStart",lazy:a&&!g&&qe(l),immediateRender:a,stagger:0,parent:m},x),V&&(v[O.prop]=V),zn(t._startAt=ne.set(_,v)),t._startAt._dp=0,t._startAt._sat=t,e<0&&(ve?t._startAt.revert(Jo):t._startAt.render(-1,!0)),t._zTime=e,!a)s(t._startAt,Ee,Ee);else if(!e)return}for(t._pt=t._ptCache=0,l=p&&qe(l)||l&&!p,k=0;k<_.length;k++){if(C=_[k],D=C._gsap||Tc(_)[k]._gsap,t._ptLookup[k]=Y={},Ll[D.id]&&Rn.length&&ga(),N=b===_?k:b.indexOf(C),O&&(F=new O).init(C,V||x,t,N,b)!==!1&&(t._pt=M=new Qe(t._pt,C,F.name,0,1,F.render,F,0,F.priority),F._props.forEach(function(G){Y[G]=M}),F.priority&&(L=1)),!O||V)for(v in x)ai[v]&&(F=cd(v,x,t,N,C,b))?F.priority&&(L=1):Y[v]=M=Cc.call(t,C,v,"get",x[v],N,b,0,n.stringFilter);t._op&&t._op[k]&&t.kill(C,t._op[k]),w&&t._pt&&(Mn=t,Ht.killTweensOf(C,Y,t.globalTime(e)),W=!t.parent,Mn=0),t._pt&&l&&(Ll[D.id]=1)}L&&pd(t),t._onInit&&t._onInit(t)}t._onUpdate=c,t._initted=(!t._op||t._pt)&&!W,d&&e<=0&&y.render(Gi,!0,!0)},Np=function(t,e,i,n,r,o,a,l){var c=(t._pt&&t._ptCache||(t._ptCache={}))[e],h,f,d,u;if(!c)for(c=t._ptCache[e]=[],d=t._ptLookup,u=t._targets.length;u--;){if(h=d[u][e],h&&h.d&&h.d._pt)for(h=h.d._pt;h&&h.p!==e&&h.fp!==e;)h=h._next;if(!h)return Wl=1,t.vars[e]="+=0",Dc(t,a),Wl=0,l?to(e+" not eligible for reset"):1;c.push(h)}for(u=c.length;u--;)f=c[u],h=f._pt||f,h.s=(n||n===0)&&!r?n:h.s+(n||0)+o*h.c,h.c=i-h.s,f.e&&(f.e=Qt(i)+Oe(f.e)),f.b&&(f.b=h.s+Oe(f.b))},Wp=function(t,e){var i=t[0]?ls(t[0]).harness:0,n=i&&i.aliases,r,o,a,l;if(!n)return e;r=Zs({},e);for(o in n)if(o in r)for(l=n[o].split(","),a=l.length;a--;)r[l[a]]=r[o];return r},Vp=function(t,e,i,n){var r=e.ease||n||"power1.inOut",o,a;if(Le(e))a=i[t]||(i[t]=[]),e.forEach(function(l,c){return a.push({t:c/(e.length-1)*100,v:l,e:r})});else for(o in e)a=i[o]||(i[o]=[]),o==="ease"||a.push({t:parseFloat(t),v:e[o],e:r})},zr=function(t,e,i,n,r){return Ut(t)?t.call(e,i,n,r):pe(t)&&~t.indexOf("random(")?io(t):t},hd=kc+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",ud={};Ke(hd+",id,stagger,delay,duration,paused,scrollTrigger",function(s){return ud[s]=1});var ne=function(s){Df(t,s);function t(i,n,r,o){var a;typeof n=="number"&&(r.duration=n,n=r,r=null),a=s.call(this,o?n:Fr(n))||this;var l=a.vars,c=l.duration,h=l.delay,f=l.immediateRender,d=l.stagger,u=l.overwrite,p=l.keyframes,g=l.defaults,_=l.scrollTrigger,m=l.yoyoEase,b=n.parent||Ht,w=(Le(i)||Af(i)?pn(i[0]):"length"in n)?[i]:Ti(i),y,x,k,v,M,C,L,D;if(a._targets=w.length?Tc(w):to("GSAP target "+i+" not found. https://gsap.com",!di.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=u,p||d||Mo(c)||Mo(h)){if(n=a.vars,y=a.timeline=new He({data:"nested",defaults:g||{},targets:b&&b.data==="nested"?b.vars.targets:w}),y.kill(),y.parent=y._dp=nn(a),y._start=0,d||Mo(c)||Mo(h)){if(v=w.length,L=d&&qf(d),Qi(d))for(M in d)~hd.indexOf(M)&&(D||(D={}),D[M]=d[M]);for(x=0;x<v;x++)k=pa(n,ud),k.stagger=0,m&&(k.yoyoEase=m),D&&Zs(k,D),C=w[x],k.duration=+zr(c,nn(a),x,C,w),k.delay=(+zr(h,nn(a),x,C,w)||0)-a._delay,!d&&v===1&&k.delay&&(a._delay=h=k.delay,a._start+=h,k.delay=0),y.to(C,k,L?L(x,C,w):0),y._ease=bt.none;y.duration()?c=h=0:a.timeline=0}else if(p){Fr(pi(y.vars.defaults,{ease:"none"})),y._ease=hs(p.ease||n.ease||"none");var O=0,F,Y,N;if(Le(p))p.forEach(function(V){return y.to(w,V,">")}),y.duration();else{k={};for(M in p)M==="ease"||M==="easeEach"||Vp(M,p[M],k,p.easeEach);for(M in k)for(F=k[M].sort(function(V,W){return V.t-W.t}),O=0,x=0;x<F.length;x++)Y=F[x],N={ease:Y.e,duration:(Y.t-(x?F[x-1].t:0))/100*c},N[M]=Y.v,y.to(w,N,O),O+=N.duration;y.duration()<c&&y.to({},{duration:c-y.duration()})}}c||a.duration(c=y.duration())}else a.timeline=0;return u===!0&&!xc&&(Mn=nn(a),Ht.killTweensOf(w),Mn=0),Xi(b,nn(a),r),n.reversed&&a.reverse(),n.paused&&a.paused(!0),(f||!c&&!p&&a._start===se(b._time)&&qe(f)&&bp(nn(a))&&b.data!=="nested")&&(a._tTime=-1e-8,a.render(Math.max(0,-h)||0)),_&&Xf(nn(a),_),a}var e=t.prototype;return e.render=function(n,r,o){var a=this._time,l=this._tDur,c=this._dur,h=n<0,f=n>l-Ee&&!h?l:n<Ee?0:n,d,u,p,g,_,m,b,w,y;if(!c)yp(this,n,r,o);else if(f!==this._tTime||!n||o||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==h||this._lazy){if(d=f,w=this.timeline,this._repeat){if(g=c+this._rDelay,this._repeat<-1&&h)return this.totalTime(g*100+n,r,o);if(d=se(f%g),f===l?(p=this._repeat,d=c):(_=se(f/g),p=~~_,p&&p===_?(d=c,p--):d>c&&(d=c)),m=this._yoyo&&p&1,m&&(y=this._yEase,d=c-d),_=Js(this._tTime,g),d===a&&!o&&this._initted&&p===_)return this._tTime=f,this;p!==_&&(w&&this._yEase&&od(w,m),this.vars.repeatRefresh&&!m&&!this._lock&&d!==g&&this._initted&&(this._lock=o=1,this.render(se(g*p),!0).invalidate()._lock=0))}if(!this._initted){if(jf(this,h?n:d,o,r,f))return this._tTime=0,this;if(a!==this._time&&!(o&&this.vars.repeatRefresh&&p!==_))return this;if(c!==this._dur)return this.render(n,r,o)}if(this._tTime=f,this._time=d,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=b=(y||this._ease)(d/c),this._from&&(this.ratio=b=1-b),!a&&f&&!r&&!_&&(fi(this,"onStart"),this._tTime!==f))return this;for(u=this._pt;u;)u.r(b,u.d),u=u._next;w&&w.render(n<0?n:w._dur*w._ease(d/this._dur),r,o)||this._startAt&&(this._zTime=n),this._onUpdate&&!r&&(h&&Fl(this,n,r,o),fi(this,"onUpdate")),this._repeat&&p!==_&&this.vars.onRepeat&&!r&&this.parent&&fi(this,"onRepeat"),(f===this._tDur||!f)&&this._tTime===f&&(h&&!this._onUpdate&&Fl(this,n,!0,!0),(n||!c)&&(f===this._tDur&&this._ts>0||!f&&this._ts<0)&&zn(this,1),!r&&!(h&&!a)&&(f||a||m)&&(fi(this,f===l?"onComplete":"onReverseComplete",!0),this._prom&&!(f<l&&this.timeScale()>0)&&this._prom()))}return this},e.targets=function(){return this._targets},e.invalidate=function(n){return(!n||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(n),s.prototype.invalidate.call(this,n)},e.resetTo=function(n,r,o,a,l){no||ci.wake(),this._ts||this.play();var c=Math.min(this._dur,(this._dp._time-this._start)*this._ts),h;return this._initted||Dc(this,c),h=this._ease(c/this._dur),Np(this,n,r,o,a,h,c,l)?this.resetTo(n,r,o,a,1):(za(this,0),this.parent||Yf(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},e.kill=function(n,r){if(r===void 0&&(r="all"),!n&&(!r||r==="all"))return this._lazy=this._pt=0,this.parent?yr(this):this.scrollTrigger&&this.scrollTrigger.kill(!!ve),this;if(this.timeline){var o=this.timeline.totalDuration();return this.timeline.killTweensOf(n,r,Mn&&Mn.vars.overwrite!==!0)._first||yr(this),this.parent&&o!==this.timeline.totalDuration()&&tr(this,this._dur*this.timeline._tDur/o,0,1),this}var a=this._targets,l=n?Ti(n):a,c=this._ptLookup,h=this._pt,f,d,u,p,g,_,m;if((!r||r==="all")&&_p(a,l))return r==="all"&&(this._pt=0),yr(this);for(f=this._op=this._op||[],r!=="all"&&(pe(r)&&(g={},Ke(r,function(b){return g[b]=1}),r=g),r=Wp(a,r)),m=a.length;m--;)if(~l.indexOf(a[m])){d=c[m],r==="all"?(f[m]=r,p=d,u={}):(u=f[m]=f[m]||{},p=r);for(g in p)_=d&&d[g],_&&((!("kill"in _.d)||_.d.kill(g)===!0)&&Fa(this,_,"_pt"),delete d[g]),u!=="all"&&(u[g]=1)}return this._initted&&!this._pt&&h&&yr(this),this},t.to=function(n,r){return new t(n,r,arguments[2])},t.from=function(n,r){return Ir(1,arguments)},t.delayedCall=function(n,r,o,a){return new t(r,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:n,onComplete:r,onReverseComplete:r,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:a})},t.fromTo=function(n,r,o){return Ir(2,arguments)},t.set=function(n,r){return r.duration=0,r.repeatDelay||(r.repeat=0),new t(n,r)},t.killTweensOf=function(n,r,o){return Ht.killTweensOf(n,r,o)},t}(so);pi(ne.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});Ke("staggerTo,staggerFrom,staggerFromTo",function(s){ne[s]=function(){var t=new He,e=zl.call(arguments,0);return e.splice(s==="staggerFromTo"?5:4,0,0),t[s].apply(t,e)}});var Oc=function(t,e,i){return t[e]=i},fd=function(t,e,i){return t[e](i)},Hp=function(t,e,i,n){return t[e](n.fp,i)},Yp=function(t,e,i){return t.setAttribute(e,i)},Ac=function(t,e){return Ut(t[e])?fd:yc(t[e])&&t.setAttribute?Yp:Oc},dd=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e6)/1e6,e)},$p=function(t,e){return e.set(e.t,e.p,!!(e.s+e.c*t),e)},gd=function(t,e){var i=e._pt,n="";if(!t&&e.b)n=e.b;else if(t===1&&e.e)n=e.e;else{for(;i;)n=i.p+(i.m?i.m(i.s+i.c*t):Math.round((i.s+i.c*t)*1e4)/1e4)+n,i=i._next;n+=e.c}e.set(e.t,e.p,n,e)},Ec=function(t,e){for(var i=e._pt;i;)i.r(t,i.d),i=i._next},Xp=function(t,e,i,n){for(var r=this._pt,o;r;)o=r._next,r.p===n&&r.modifier(t,e,i),r=o},jp=function(t){for(var e=this._pt,i,n;e;)n=e._next,e.p===t&&!e.op||e.op===t?Fa(this,e,"_pt"):e.dep||(i=1),e=n;return!i},Up=function(t,e,i,n){n.mSet(t,e,n.m.call(n.tween,i,n.mt),n)},pd=function(t){for(var e=t._pt,i,n,r,o;e;){for(i=e._next,n=r;n&&n.pr>e.pr;)n=n._next;(e._prev=n?n._prev:o)?e._prev._next=e:r=e,(e._next=n)?n._prev=e:o=e,e=i}t._pt=r},Qe=function(){function s(e,i,n,r,o,a,l,c,h){this.t=i,this.s=r,this.c=o,this.p=n,this.r=a||dd,this.d=l||this,this.set=c||Oc,this.pr=h||0,this._next=e,e&&(e._prev=this)}var t=s.prototype;return t.modifier=function(i,n,r){this.mSet=this.mSet||this.set,this.set=Up,this.m=i,this.mt=r,this.tween=n},s}();Ke(kc+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(s){return Mc[s]=1});gi.TweenMax=gi.TweenLite=ne;gi.TimelineLite=gi.TimelineMax=He;Ht=new He({sortChildren:!1,defaults:Qs,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});di.stringFilter=sd;var us=[],ea={},Gp=[],xh=0,qp=0,tl=function(t){return(ea[t]||Gp).map(function(e){return e()})},Vl=function(){var t=Date.now(),e=[];t-xh>2&&(tl("matchMediaInit"),us.forEach(function(i){var n=i.queries,r=i.conditions,o,a,l,c;for(a in n)o=Yi.matchMedia(n[a]).matches,o&&(l=1),o!==r[a]&&(r[a]=o,c=1);c&&(i.revert(),l&&e.push(i))}),tl("matchMediaRevert"),e.forEach(function(i){return i.onMatch(i,function(n){return i.add(null,n)})}),xh=t,tl("matchMedia"))},_d=function(){function s(e,i){this.selector=i&&Bl(i),this.data=[],this._r=[],this.isReverted=!1,this.id=qp++,e&&this.add(e)}var t=s.prototype;return t.add=function(i,n,r){Ut(i)&&(r=n,n=i,i=Ut);var o=this,a=function(){var c=zt,h=o.selector,f;return c&&c!==o&&c.data.push(o),r&&(o.selector=Bl(r)),zt=o,f=n.apply(o,arguments),Ut(f)&&o._r.push(f),zt=c,o.selector=h,o.isReverted=!1,f};return o.last=a,i===Ut?a(o,function(l){return o.add(null,l)}):i?o[i]=a:a},t.ignore=function(i){var n=zt;zt=null,i(this),zt=n},t.getTweens=function(){var i=[];return this.data.forEach(function(n){return n instanceof s?i.push.apply(i,n.getTweens()):n instanceof ne&&!(n.parent&&n.parent.data==="nested")&&i.push(n)}),i},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(i,n){var r=this;if(i?function(){for(var a=r.getTweens(),l=r.data.length,c;l--;)c=r.data[l],c.data==="isFlip"&&(c.revert(),c.getChildren(!0,!0,!1).forEach(function(h){return a.splice(a.indexOf(h),1)}));for(a.map(function(h){return{g:h._dur||h._delay||h._sat&&!h._sat.vars.immediateRender?h.globalTime(0):-1/0,t:h}}).sort(function(h,f){return f.g-h.g||-1/0}).forEach(function(h){return h.t.revert(i)}),l=r.data.length;l--;)c=r.data[l],c instanceof He?c.data!=="nested"&&(c.scrollTrigger&&c.scrollTrigger.revert(),c.kill()):!(c instanceof ne)&&c.revert&&c.revert(i);r._r.forEach(function(h){return h(i,r)}),r.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),n)for(var o=us.length;o--;)us[o].id===this.id&&us.splice(o,1)},t.revert=function(i){this.kill(i||{})},s}(),Kp=function(){function s(e){this.contexts=[],this.scope=e,zt&&zt.data.push(this)}var t=s.prototype;return t.add=function(i,n,r){Qi(i)||(i={matches:i});var o=new _d(0,r||this.scope),a=o.conditions={},l,c,h;zt&&!o.selector&&(o.selector=zt.selector),this.contexts.push(o),n=o.add("onMatch",n),o.queries=i;for(c in i)c==="all"?h=1:(l=Yi.matchMedia(i[c]),l&&(us.indexOf(o)<0&&us.push(o),(a[c]=l.matches)&&(h=1),l.addListener?l.addListener(Vl):l.addEventListener("change",Vl)));return h&&n(o,function(f){return o.add(null,f)}),this},t.revert=function(i){this.kill(i||{})},t.kill=function(i){this.contexts.forEach(function(n){return n.kill(i,!0)})},s}(),ma={registerPlugin:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];e.forEach(function(n){return ed(n)})},timeline:function(t){return new He(t)},getTweensOf:function(t,e){return Ht.getTweensOf(t,e)},getProperty:function(t,e,i,n){pe(t)&&(t=Ti(t)[0]);var r=ls(t||{}).get,o=i?Hf:Vf;return i==="native"&&(i=""),t&&(e?o((ai[e]&&ai[e].get||r)(t,e,i,n)):function(a,l,c){return o((ai[a]&&ai[a].get||r)(t,a,l,c))})},quickSetter:function(t,e,i){if(t=Ti(t),t.length>1){var n=t.map(function(h){return Je.quickSetter(h,e,i)}),r=n.length;return function(h){for(var f=r;f--;)n[f](h)}}t=t[0]||{};var o=ai[e],a=ls(t),l=a.harness&&(a.harness.aliases||{})[e]||e,c=o?function(h){var f=new o;Ns._pt=0,f.init(t,i?h+i:h,Ns,0,[t]),f.render(1,f),Ns._pt&&Ec(1,Ns)}:a.set(t,l);return o?c:function(h){return c(t,l,i?h+i:h,a,1)}},quickTo:function(t,e,i){var n,r=Je.to(t,pi((n={},n[e]="+=0.1",n.paused=!0,n.stagger=0,n),i||{})),o=function(l,c,h){return r.resetTo(e,l,c,h)};return o.tween=r,o},isTweening:function(t){return Ht.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=hs(t.ease,Qs.ease)),gh(Qs,t||{})},config:function(t){return gh(di,t||{})},registerEffect:function(t){var e=t.name,i=t.effect,n=t.plugins,r=t.defaults,o=t.extendTimeline;(n||"").split(",").forEach(function(a){return a&&!ai[a]&&!gi[a]&&to(e+" effect requires "+a+" plugin.")}),Ka[e]=function(a,l,c){return i(Ti(a),pi(l||{},r),c)},o&&(He.prototype[e]=function(a,l,c){return this.add(Ka[e](a,Qi(l)?l:(c=l)&&{},this),c)})},registerEase:function(t,e){bt[t]=hs(e)},parseEase:function(t,e){return arguments.length?hs(t,e):bt},getById:function(t){return Ht.getById(t)},exportRoot:function(t,e){t===void 0&&(t={});var i=new He(t),n,r;for(i.smoothChildTiming=qe(t.smoothChildTiming),Ht.remove(i),i._dp=0,i._time=i._tTime=Ht._time,n=Ht._first;n;)r=n._next,(e||!(!n._dur&&n instanceof ne&&n.vars.onComplete===n._targets[0]))&&Xi(i,n,n._start-n._delay),n=r;return Xi(Ht,i,0),i},context:function(t,e){return t?new _d(t,e):zt},matchMedia:function(t){return new Kp(t)},matchMediaRefresh:function(){return us.forEach(function(t){var e=t.conditions,i,n;for(n in e)e[n]&&(e[n]=!1,i=1);i&&t.revert()})||Vl()},addEventListener:function(t,e){var i=ea[t]||(ea[t]=[]);~i.indexOf(e)||i.push(e)},removeEventListener:function(t,e){var i=ea[t],n=i&&i.indexOf(e);n>=0&&i.splice(n,1)},utils:{wrap:Cp,wrapYoyo:Dp,distribute:qf,random:Qf,snap:Kf,normalize:Pp,getUnit:Oe,clamp:Sp,splitColor:id,toArray:Ti,selector:Bl,mapRange:Jf,pipe:kp,unitize:Tp,interpolate:Op,shuffle:Gf},install:If,effects:Ka,ticker:ci,updateRoot:He.updateRoot,plugins:ai,globalTimeline:Ht,core:{PropTween:Qe,globals:zf,Tween:ne,Timeline:He,Animation:so,getCache:ls,_removeLinkedListItem:Fa,reverting:function(){return ve},context:function(t){return t&&zt&&(zt.data.push(t),t._ctx=zt),zt},suppressOverwrites:function(t){return xc=t}}};Ke("to,from,fromTo,delayedCall,set,killTweensOf",function(s){return ma[s]=ne[s]});ci.add(He.updateRoot);Ns=ma.to({},{duration:0});var Qp=function(t,e){for(var i=t._pt;i&&i.p!==e&&i.op!==e&&i.fp!==e;)i=i._next;return i},Zp=function(t,e){var i=t._targets,n,r,o;for(n in e)for(r=i.length;r--;)o=t._ptLookup[r][n],o&&(o=o.d)&&(o._pt&&(o=Qp(o,n)),o&&o.modifier&&o.modifier(e[n],t,i[r],n))},el=function(t,e){return{name:t,headless:1,rawVars:1,init:function(n,r,o){o._onInit=function(a){var l,c;if(pe(r)&&(l={},Ke(r,function(h){return l[h]=1}),r=l),e){l={};for(c in r)l[c]=e(r[c]);r=l}Zp(a,r)}}}},Je=ma.registerPlugin({name:"attr",init:function(t,e,i,n,r){var o,a,l;this.tween=i;for(o in e)l=t.getAttribute(o)||"",a=this.add(t,"setAttribute",(l||0)+"",e[o],n,r,0,0,o),a.op=o,a.b=l,this._props.push(o)},render:function(t,e){for(var i=e._pt;i;)ve?i.set(i.t,i.p,i.b,i):i.r(t,i.d),i=i._next}},{name:"endArray",headless:1,init:function(t,e){for(var i=e.length;i--;)this.add(t,i,t[i]||0,e[i],0,0,0,0,0,1)}},el("roundProps",Nl),el("modifiers"),el("snap",Kf))||ma;ne.version=He.version=Je.version="3.13.0";Ff=1;vc()&&er();bt.Power0;bt.Power1;bt.Power2;bt.Power3;bt.Power4;bt.Linear;bt.Quad;bt.Cubic;bt.Quart;bt.Quint;bt.Strong;bt.Elastic;bt.Back;bt.SteppedEase;bt.Bounce;bt.Sine;bt.Expo;bt.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var yh,kn,Hs,Rc,os,vh,Lc,Jp=function(){return typeof window<"u"},_n={},ts=180/Math.PI,Ys=Math.PI/180,Cs=Math.atan2,wh=1e8,Fc=/([A-Z])/g,t_=/(left|right|width|margin|padding|x)/i,e_=/[\s,\(]\S/,ji={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},Hl=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},i_=function(t,e){return e.set(e.t,e.p,t===1?e.e:Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},n_=function(t,e){return e.set(e.t,e.p,t?Math.round((e.s+e.c*t)*1e4)/1e4+e.u:e.b,e)},s_=function(t,e){var i=e.s+e.c*t;e.set(e.t,e.p,~~(i+(i<0?-.5:.5))+e.u,e)},md=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},bd=function(t,e){return e.set(e.t,e.p,t!==1?e.b:e.e,e)},r_=function(t,e,i){return t.style[e]=i},o_=function(t,e,i){return t.style.setProperty(e,i)},a_=function(t,e,i){return t._gsap[e]=i},l_=function(t,e,i){return t._gsap.scaleX=t._gsap.scaleY=i},c_=function(t,e,i,n,r){var o=t._gsap;o.scaleX=o.scaleY=i,o.renderTransform(r,o)},h_=function(t,e,i,n,r){var o=t._gsap;o[e]=i,o.renderTransform(r,o)},Yt="transform",Ze=Yt+"Origin",u_=function s(t,e){var i=this,n=this.target,r=n.style,o=n._gsap;if(t in _n&&r){if(this.tfm=this.tfm||{},t!=="transform")t=ji[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return i.tfm[a]=sn(n,a)}):this.tfm[t]=o.x?o[t]:sn(n,t),t===Ze&&(this.tfm.zOrigin=o.zOrigin);else return ji.transform.split(",").forEach(function(a){return s.call(i,a,e)});if(this.props.indexOf(Yt)>=0)return;o.svg&&(this.svgo=n.getAttribute("data-svg-origin"),this.props.push(Ze,e,"")),t=Yt}(r||e)&&this.props.push(t,e,r[t])},xd=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},f_=function(){var t=this.props,e=this.target,i=e.style,n=e._gsap,r,o;for(r=0;r<t.length;r+=3)t[r+1]?t[r+1]===2?e[t[r]](t[r+2]):e[t[r]]=t[r+2]:t[r+2]?i[t[r]]=t[r+2]:i.removeProperty(t[r].substr(0,2)==="--"?t[r]:t[r].replace(Fc,"-$1").toLowerCase());if(this.tfm){for(o in this.tfm)n[o]=this.tfm[o];n.svg&&(n.renderTransform(),e.setAttribute("data-svg-origin",this.svgo||"")),r=Lc(),(!r||!r.isStart)&&!i[Yt]&&(xd(i),n.zOrigin&&i[Ze]&&(i[Ze]+=" "+n.zOrigin+"px",n.zOrigin=0,n.renderTransform()),n.uncache=1)}},yd=function(t,e){var i={target:t,props:[],revert:f_,save:u_};return t._gsap||Je.core.getCache(t),e&&t.style&&t.nodeType&&e.split(",").forEach(function(n){return i.save(n)}),i},vd,Yl=function(t,e){var i=kn.createElementNS?kn.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):kn.createElement(t);return i&&i.style?i:kn.createElement(t)},Pi=function s(t,e,i){var n=getComputedStyle(t);return n[e]||n.getPropertyValue(e.replace(Fc,"-$1").toLowerCase())||n.getPropertyValue(e)||!i&&s(t,ir(e)||e,1)||""},Sh="O,Moz,ms,Ms,Webkit".split(","),ir=function(t,e,i){var n=e||os,r=n.style,o=5;if(t in r&&!i)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);o--&&!(Sh[o]+t in r););return o<0?null:(o===3?"ms":o>=0?Sh[o]:"")+t},$l=function(){Jp()&&window.document&&(yh=window,kn=yh.document,Hs=kn.documentElement,os=Yl("div")||{style:{}},Yl("div"),Yt=ir(Yt),Ze=Yt+"Origin",os.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",vd=!!ir("perspective"),Lc=Je.core.reverting,Rc=1)},Mh=function(t){var e=t.ownerSVGElement,i=Yl("svg",e&&e.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),n=t.cloneNode(!0),r;n.style.display="block",i.appendChild(n),Hs.appendChild(i);try{r=n.getBBox()}catch{}return i.removeChild(n),Hs.removeChild(i),r},kh=function(t,e){for(var i=e.length;i--;)if(t.hasAttribute(e[i]))return t.getAttribute(e[i])},wd=function(t){var e,i;try{e=t.getBBox()}catch{e=Mh(t),i=1}return e&&(e.width||e.height)||i||(e=Mh(t)),e&&!e.width&&!e.x&&!e.y?{x:+kh(t,["x","cx","x1"])||0,y:+kh(t,["y","cy","y1"])||0,width:0,height:0}:e},Sd=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&wd(t))},xs=function(t,e){if(e){var i=t.style,n;e in _n&&e!==Ze&&(e=Yt),i.removeProperty?(n=e.substr(0,2),(n==="ms"||e.substr(0,6)==="webkit")&&(e="-"+e),i.removeProperty(n==="--"?e:e.replace(Fc,"-$1").toLowerCase())):i.removeAttribute(e)}},Tn=function(t,e,i,n,r,o){var a=new Qe(t._pt,e,i,0,1,o?bd:md);return t._pt=a,a.b=n,a.e=r,t._props.push(i),a},Th={deg:1,rad:1,turn:1},d_={grid:1,flex:1},Bn=function s(t,e,i,n){var r=parseFloat(i)||0,o=(i+"").trim().substr((r+"").length)||"px",a=os.style,l=t_.test(e),c=t.tagName.toLowerCase()==="svg",h=(c?"client":"offset")+(l?"Width":"Height"),f=100,d=n==="px",u=n==="%",p,g,_,m;if(n===o||!r||Th[n]||Th[o])return r;if(o!=="px"&&!d&&(r=s(t,e,i,"px")),m=t.getCTM&&Sd(t),(u||o==="%")&&(_n[e]||~e.indexOf("adius")))return p=m?t.getBBox()[l?"width":"height"]:t[h],Qt(u?r/p*f:r/100*p);if(a[l?"width":"height"]=f+(d?o:n),g=n!=="rem"&&~e.indexOf("adius")||n==="em"&&t.appendChild&&!c?t:t.parentNode,m&&(g=(t.ownerSVGElement||{}).parentNode),(!g||g===kn||!g.appendChild)&&(g=kn.body),_=g._gsap,_&&u&&_.width&&l&&_.time===ci.time&&!_.uncache)return Qt(r/_.width*f);if(u&&(e==="height"||e==="width")){var b=t.style[e];t.style[e]=f+n,p=t[h],b?t.style[e]=b:xs(t,e)}else(u||o==="%")&&!d_[Pi(g,"display")]&&(a.position=Pi(t,"position")),g===t&&(a.position="static"),g.appendChild(os),p=os[h],g.removeChild(os),a.position="absolute";return l&&u&&(_=ls(g),_.time=ci.time,_.width=g[h]),Qt(d?p*r/f:p&&r?f/p*r:0)},sn=function(t,e,i,n){var r;return Rc||$l(),e in ji&&e!=="transform"&&(e=ji[e],~e.indexOf(",")&&(e=e.split(",")[0])),_n[e]&&e!=="transform"?(r=oo(t,n),r=e!=="transformOrigin"?r[e]:r.svg?r.origin:xa(Pi(t,Ze))+" "+r.zOrigin+"px"):(r=t.style[e],(!r||r==="auto"||n||~(r+"").indexOf("calc("))&&(r=ba[e]&&ba[e](t,e,i)||Pi(t,e)||Nf(t,e)||(e==="opacity"?1:0))),i&&!~(r+"").trim().indexOf(" ")?Bn(t,e,r,i)+i:r},g_=function(t,e,i,n){if(!i||i==="none"){var r=ir(e,t,1),o=r&&Pi(t,r,1);o&&o!==i?(e=r,i=o):e==="borderColor"&&(i=Pi(t,"borderTopColor"))}var a=new Qe(this._pt,t.style,e,0,1,gd),l=0,c=0,h,f,d,u,p,g,_,m,b,w,y,x;if(a.b=i,a.e=n,i+="",n+="",n.substring(0,6)==="var(--"&&(n=Pi(t,n.substring(4,n.indexOf(")")))),n==="auto"&&(g=t.style[e],t.style[e]=n,n=Pi(t,e)||n,g?t.style[e]=g:xs(t,e)),h=[i,n],sd(h),i=h[0],n=h[1],d=i.match(Bs)||[],x=n.match(Bs)||[],x.length){for(;f=Bs.exec(n);)_=f[0],b=n.substring(l,f.index),p?p=(p+1)%5:(b.substr(-5)==="rgba("||b.substr(-5)==="hsla(")&&(p=1),_!==(g=d[c++]||"")&&(u=parseFloat(g)||0,y=g.substr((u+"").length),_.charAt(1)==="="&&(_=Vs(u,_)+y),m=parseFloat(_),w=_.substr((m+"").length),l=Bs.lastIndex-w.length,w||(w=w||di.units[e]||y,l===n.length&&(n+=w,a.e+=w)),y!==w&&(u=Bn(t,e,g,w)||0),a._pt={_next:a._pt,p:b||c===1?b:",",s:u,c:m-u,m:p&&p<4||e==="zIndex"?Math.round:0});a.c=l<n.length?n.substring(l,n.length):""}else a.r=e==="display"&&n==="none"?bd:md;return Rf.test(n)&&(a.e=0),this._pt=a,a},Ph={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},p_=function(t){var e=t.split(" "),i=e[0],n=e[1]||"50%";return(i==="top"||i==="bottom"||n==="left"||n==="right")&&(t=i,i=n,n=t),e[0]=Ph[i]||i,e[1]=Ph[n]||n,e.join(" ")},__=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var i=e.t,n=i.style,r=e.u,o=i._gsap,a,l,c;if(r==="all"||r===!0)n.cssText="",l=1;else for(r=r.split(","),c=r.length;--c>-1;)a=r[c],_n[a]&&(l=1,a=a==="transformOrigin"?Ze:Yt),xs(i,a);l&&(xs(i,Yt),o&&(o.svg&&i.removeAttribute("transform"),n.scale=n.rotate=n.translate="none",oo(i,1),o.uncache=1,xd(n)))}},ba={clearProps:function(t,e,i,n,r){if(r.data!=="isFromStart"){var o=t._pt=new Qe(t._pt,e,i,0,0,__);return o.u=n,o.pr=-10,o.tween=r,t._props.push(i),1}}},ro=[1,0,0,1,0,0],Md={},kd=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Ch=function(t){var e=Pi(t,Yt);return kd(e)?ro:e.substr(7).match(Ef).map(Qt)},Ic=function(t,e){var i=t._gsap||ls(t),n=t.style,r=Ch(t),o,a,l,c;return i.svg&&t.getAttribute("transform")?(l=t.transform.baseVal.consolidate().matrix,r=[l.a,l.b,l.c,l.d,l.e,l.f],r.join(",")==="1,0,0,1,0,0"?ro:r):(r===ro&&!t.offsetParent&&t!==Hs&&!i.svg&&(l=n.display,n.display="block",o=t.parentNode,(!o||!t.offsetParent&&!t.getBoundingClientRect().width)&&(c=1,a=t.nextElementSibling,Hs.appendChild(t)),r=Ch(t),l?n.display=l:xs(t,"display"),c&&(a?o.insertBefore(t,a):o?o.appendChild(t):Hs.removeChild(t))),e&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r)},Xl=function(t,e,i,n,r,o){var a=t._gsap,l=r||Ic(t,!0),c=a.xOrigin||0,h=a.yOrigin||0,f=a.xOffset||0,d=a.yOffset||0,u=l[0],p=l[1],g=l[2],_=l[3],m=l[4],b=l[5],w=e.split(" "),y=parseFloat(w[0])||0,x=parseFloat(w[1])||0,k,v,M,C;i?l!==ro&&(v=u*_-p*g)&&(M=y*(_/v)+x*(-g/v)+(g*b-_*m)/v,C=y*(-p/v)+x*(u/v)-(u*b-p*m)/v,y=M,x=C):(k=wd(t),y=k.x+(~w[0].indexOf("%")?y/100*k.width:y),x=k.y+(~(w[1]||w[0]).indexOf("%")?x/100*k.height:x)),n||n!==!1&&a.smooth?(m=y-c,b=x-h,a.xOffset=f+(m*u+b*g)-m,a.yOffset=d+(m*p+b*_)-b):a.xOffset=a.yOffset=0,a.xOrigin=y,a.yOrigin=x,a.smooth=!!n,a.origin=e,a.originIsAbsolute=!!i,t.style[Ze]="0px 0px",o&&(Tn(o,a,"xOrigin",c,y),Tn(o,a,"yOrigin",h,x),Tn(o,a,"xOffset",f,a.xOffset),Tn(o,a,"yOffset",d,a.yOffset)),t.setAttribute("data-svg-origin",y+" "+x)},oo=function(t,e){var i=t._gsap||new ld(t);if("x"in i&&!e&&!i.uncache)return i;var n=t.style,r=i.scaleX<0,o="px",a="deg",l=getComputedStyle(t),c=Pi(t,Ze)||"0",h,f,d,u,p,g,_,m,b,w,y,x,k,v,M,C,L,D,O,F,Y,N,V,W,G,Z,P,q,J,ht,Q,Pt;return h=f=d=g=_=m=b=w=y=0,u=p=1,i.svg=!!(t.getCTM&&Sd(t)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(n[Yt]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[Yt]!=="none"?l[Yt]:"")),n.scale=n.rotate=n.translate="none"),v=Ic(t,i.svg),i.svg&&(i.uncache?(G=t.getBBox(),c=i.xOrigin-G.x+"px "+(i.yOrigin-G.y)+"px",W=""):W=!e&&t.getAttribute("data-svg-origin"),Xl(t,W||c,!!W||i.originIsAbsolute,i.smooth!==!1,v)),x=i.xOrigin||0,k=i.yOrigin||0,v!==ro&&(D=v[0],O=v[1],F=v[2],Y=v[3],h=N=v[4],f=V=v[5],v.length===6?(u=Math.sqrt(D*D+O*O),p=Math.sqrt(Y*Y+F*F),g=D||O?Cs(O,D)*ts:0,b=F||Y?Cs(F,Y)*ts+g:0,b&&(p*=Math.abs(Math.cos(b*Ys))),i.svg&&(h-=x-(x*D+k*F),f-=k-(x*O+k*Y))):(Pt=v[6],ht=v[7],P=v[8],q=v[9],J=v[10],Q=v[11],h=v[12],f=v[13],d=v[14],M=Cs(Pt,J),_=M*ts,M&&(C=Math.cos(-M),L=Math.sin(-M),W=N*C+P*L,G=V*C+q*L,Z=Pt*C+J*L,P=N*-L+P*C,q=V*-L+q*C,J=Pt*-L+J*C,Q=ht*-L+Q*C,N=W,V=G,Pt=Z),M=Cs(-F,J),m=M*ts,M&&(C=Math.cos(-M),L=Math.sin(-M),W=D*C-P*L,G=O*C-q*L,Z=F*C-J*L,Q=Y*L+Q*C,D=W,O=G,F=Z),M=Cs(O,D),g=M*ts,M&&(C=Math.cos(M),L=Math.sin(M),W=D*C+O*L,G=N*C+V*L,O=O*C-D*L,V=V*C-N*L,D=W,N=G),_&&Math.abs(_)+Math.abs(g)>359.9&&(_=g=0,m=180-m),u=Qt(Math.sqrt(D*D+O*O+F*F)),p=Qt(Math.sqrt(V*V+Pt*Pt)),M=Cs(N,V),b=Math.abs(M)>2e-4?M*ts:0,y=Q?1/(Q<0?-Q:Q):0),i.svg&&(W=t.getAttribute("transform"),i.forceCSS=t.setAttribute("transform","")||!kd(Pi(t,Yt)),W&&t.setAttribute("transform",W))),Math.abs(b)>90&&Math.abs(b)<270&&(r?(u*=-1,b+=g<=0?180:-180,g+=g<=0?180:-180):(p*=-1,b+=b<=0?180:-180)),e=e||i.uncache,i.x=h-((i.xPercent=h&&(!e&&i.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-h)?-50:0)))?t.offsetWidth*i.xPercent/100:0)+o,i.y=f-((i.yPercent=f&&(!e&&i.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-f)?-50:0)))?t.offsetHeight*i.yPercent/100:0)+o,i.z=d+o,i.scaleX=Qt(u),i.scaleY=Qt(p),i.rotation=Qt(g)+a,i.rotationX=Qt(_)+a,i.rotationY=Qt(m)+a,i.skewX=b+a,i.skewY=w+a,i.transformPerspective=y+o,(i.zOrigin=parseFloat(c.split(" ")[2])||!e&&i.zOrigin||0)&&(n[Ze]=xa(c)),i.xOffset=i.yOffset=0,i.force3D=di.force3D,i.renderTransform=i.svg?b_:vd?Td:m_,i.uncache=0,i},xa=function(t){return(t=t.split(" "))[0]+" "+t[1]},il=function(t,e,i){var n=Oe(e);return Qt(parseFloat(e)+parseFloat(Bn(t,"x",i+"px",n)))+n},m_=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,Td(t,e)},Un="0deg",ur="0px",Gn=") ",Td=function(t,e){var i=e||this,n=i.xPercent,r=i.yPercent,o=i.x,a=i.y,l=i.z,c=i.rotation,h=i.rotationY,f=i.rotationX,d=i.skewX,u=i.skewY,p=i.scaleX,g=i.scaleY,_=i.transformPerspective,m=i.force3D,b=i.target,w=i.zOrigin,y="",x=m==="auto"&&t&&t!==1||m===!0;if(w&&(f!==Un||h!==Un)){var k=parseFloat(h)*Ys,v=Math.sin(k),M=Math.cos(k),C;k=parseFloat(f)*Ys,C=Math.cos(k),o=il(b,o,v*C*-w),a=il(b,a,-Math.sin(k)*-w),l=il(b,l,M*C*-w+w)}_!==ur&&(y+="perspective("+_+Gn),(n||r)&&(y+="translate("+n+"%, "+r+"%) "),(x||o!==ur||a!==ur||l!==ur)&&(y+=l!==ur||x?"translate3d("+o+", "+a+", "+l+") ":"translate("+o+", "+a+Gn),c!==Un&&(y+="rotate("+c+Gn),h!==Un&&(y+="rotateY("+h+Gn),f!==Un&&(y+="rotateX("+f+Gn),(d!==Un||u!==Un)&&(y+="skew("+d+", "+u+Gn),(p!==1||g!==1)&&(y+="scale("+p+", "+g+Gn),b.style[Yt]=y||"translate(0, 0)"},b_=function(t,e){var i=e||this,n=i.xPercent,r=i.yPercent,o=i.x,a=i.y,l=i.rotation,c=i.skewX,h=i.skewY,f=i.scaleX,d=i.scaleY,u=i.target,p=i.xOrigin,g=i.yOrigin,_=i.xOffset,m=i.yOffset,b=i.forceCSS,w=parseFloat(o),y=parseFloat(a),x,k,v,M,C;l=parseFloat(l),c=parseFloat(c),h=parseFloat(h),h&&(h=parseFloat(h),c+=h,l+=h),l||c?(l*=Ys,c*=Ys,x=Math.cos(l)*f,k=Math.sin(l)*f,v=Math.sin(l-c)*-d,M=Math.cos(l-c)*d,c&&(h*=Ys,C=Math.tan(c-h),C=Math.sqrt(1+C*C),v*=C,M*=C,h&&(C=Math.tan(h),C=Math.sqrt(1+C*C),x*=C,k*=C)),x=Qt(x),k=Qt(k),v=Qt(v),M=Qt(M)):(x=f,M=d,k=v=0),(w&&!~(o+"").indexOf("px")||y&&!~(a+"").indexOf("px"))&&(w=Bn(u,"x",o,"px"),y=Bn(u,"y",a,"px")),(p||g||_||m)&&(w=Qt(w+p-(p*x+g*v)+_),y=Qt(y+g-(p*k+g*M)+m)),(n||r)&&(C=u.getBBox(),w=Qt(w+n/100*C.width),y=Qt(y+r/100*C.height)),C="matrix("+x+","+k+","+v+","+M+","+w+","+y+")",u.setAttribute("transform",C),b&&(u.style[Yt]=C)},x_=function(t,e,i,n,r){var o=360,a=pe(r),l=parseFloat(r)*(a&&~r.indexOf("rad")?ts:1),c=l-n,h=n+c+"deg",f,d;return a&&(f=r.split("_")[1],f==="short"&&(c%=o,c!==c%(o/2)&&(c+=c<0?o:-360)),f==="cw"&&c<0?c=(c+o*wh)%o-~~(c/o)*o:f==="ccw"&&c>0&&(c=(c-o*wh)%o-~~(c/o)*o)),t._pt=d=new Qe(t._pt,e,i,n,c,i_),d.e=h,d.u="deg",t._props.push(i),d},Dh=function(t,e){for(var i in e)t[i]=e[i];return t},y_=function(t,e,i){var n=Dh({},i._gsap),r="perspective,force3D,transformOrigin,svgOrigin",o=i.style,a,l,c,h,f,d,u,p;n.svg?(c=i.getAttribute("transform"),i.setAttribute("transform",""),o[Yt]=e,a=oo(i,1),xs(i,Yt),i.setAttribute("transform",c)):(c=getComputedStyle(i)[Yt],o[Yt]=e,a=oo(i,1),o[Yt]=c);for(l in _n)c=n[l],h=a[l],c!==h&&r.indexOf(l)<0&&(u=Oe(c),p=Oe(h),f=u!==p?Bn(i,l,c,p):parseFloat(c),d=parseFloat(h),t._pt=new Qe(t._pt,a,l,f,d-f,Hl),t._pt.u=p||0,t._props.push(l));Dh(a,n)};Ke("padding,margin,Width,Radius",function(s,t){var e="Top",i="Right",n="Bottom",r="Left",o=(t<3?[e,i,n,r]:[e+r,e+i,n+i,n+r]).map(function(a){return t<2?s+a:"border"+a+s});ba[t>1?"border"+s:s]=function(a,l,c,h,f){var d,u;if(arguments.length<4)return d=o.map(function(p){return sn(a,p,c)}),u=d.join(" "),u.split(d[0]).length===5?d[0]:u;d=(h+"").split(" "),u={},o.forEach(function(p,g){return u[p]=d[g]=d[g]||d[(g-1)/2|0]}),a.init(l,u,f)}});var Pd={name:"css",register:$l,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,i,n,r){var o=this._props,a=t.style,l=i.vars.startAt,c,h,f,d,u,p,g,_,m,b,w,y,x,k,v,M;Rc||$l(),this.styles=this.styles||yd(t),M=this.styles.props,this.tween=i;for(g in e)if(g!=="autoRound"&&(h=e[g],!(ai[g]&&cd(g,e,i,n,t,r)))){if(u=typeof h,p=ba[g],u==="function"&&(h=h.call(i,n,t,r),u=typeof h),u==="string"&&~h.indexOf("random(")&&(h=io(h)),p)p(this,t,g,h,i)&&(v=1);else if(g.substr(0,2)==="--")c=(getComputedStyle(t).getPropertyValue(g)+"").trim(),h+="",Ln.lastIndex=0,Ln.test(c)||(_=Oe(c),m=Oe(h)),m?_!==m&&(c=Bn(t,g,c,m)+m):_&&(h+=_),this.add(a,"setProperty",c,h,n,r,0,0,g),o.push(g),M.push(g,0,a[g]);else if(u!=="undefined"){if(l&&g in l?(c=typeof l[g]=="function"?l[g].call(i,n,t,r):l[g],pe(c)&&~c.indexOf("random(")&&(c=io(c)),Oe(c+"")||c==="auto"||(c+=di.units[g]||Oe(sn(t,g))||""),(c+"").charAt(1)==="="&&(c=sn(t,g))):c=sn(t,g),d=parseFloat(c),b=u==="string"&&h.charAt(1)==="="&&h.substr(0,2),b&&(h=h.substr(2)),f=parseFloat(h),g in ji&&(g==="autoAlpha"&&(d===1&&sn(t,"visibility")==="hidden"&&f&&(d=0),M.push("visibility",0,a.visibility),Tn(this,a,"visibility",d?"inherit":"hidden",f?"inherit":"hidden",!f)),g!=="scale"&&g!=="transform"&&(g=ji[g],~g.indexOf(",")&&(g=g.split(",")[0]))),w=g in _n,w){if(this.styles.save(g),u==="string"&&h.substring(0,6)==="var(--"&&(h=Pi(t,h.substring(4,h.indexOf(")"))),f=parseFloat(h)),y||(x=t._gsap,x.renderTransform&&!e.parseTransform||oo(t,e.parseTransform),k=e.smoothOrigin!==!1&&x.smooth,y=this._pt=new Qe(this._pt,a,Yt,0,1,x.renderTransform,x,0,-1),y.dep=1),g==="scale")this._pt=new Qe(this._pt,x,"scaleY",x.scaleY,(b?Vs(x.scaleY,b+f):f)-x.scaleY||0,Hl),this._pt.u=0,o.push("scaleY",g),g+="X";else if(g==="transformOrigin"){M.push(Ze,0,a[Ze]),h=p_(h),x.svg?Xl(t,h,0,k,0,this):(m=parseFloat(h.split(" ")[2])||0,m!==x.zOrigin&&Tn(this,x,"zOrigin",x.zOrigin,m),Tn(this,a,g,xa(c),xa(h)));continue}else if(g==="svgOrigin"){Xl(t,h,1,k,0,this);continue}else if(g in Md){x_(this,x,g,d,b?Vs(d,b+h):h);continue}else if(g==="smoothOrigin"){Tn(this,x,"smooth",x.smooth,h);continue}else if(g==="force3D"){x[g]=h;continue}else if(g==="transform"){y_(this,h,t);continue}}else g in a||(g=ir(g)||g);if(w||(f||f===0)&&(d||d===0)&&!e_.test(h)&&g in a)_=(c+"").substr((d+"").length),f||(f=0),m=Oe(h)||(g in di.units?di.units[g]:_),_!==m&&(d=Bn(t,g,c,m)),this._pt=new Qe(this._pt,w?x:a,g,d,(b?Vs(d,b+f):f)-d,!w&&(m==="px"||g==="zIndex")&&e.autoRound!==!1?s_:Hl),this._pt.u=m||0,_!==m&&m!=="%"&&(this._pt.b=c,this._pt.r=n_);else if(g in a)g_.call(this,t,g,c,b?b+h:h);else if(g in t)this.add(t,g,c||t[g],b?b+h:h,n,r);else if(g!=="parseTransform"){Sc(g,h);continue}w||(g in a?M.push(g,0,a[g]):typeof t[g]=="function"?M.push(g,2,t[g]()):M.push(g,1,c||t[g])),o.push(g)}}v&&pd(this)},render:function(t,e){if(e.tween._time||!Lc())for(var i=e._pt;i;)i.r(t,i.d),i=i._next;else e.styles.revert()},get:sn,aliases:ji,getSetter:function(t,e,i){var n=ji[e];return n&&n.indexOf(",")<0&&(e=n),e in _n&&e!==Ze&&(t._gsap.x||sn(t,"x"))?i&&vh===i?e==="scale"?l_:a_:(vh=i||{})&&(e==="scale"?c_:h_):t.style&&!yc(t.style[e])?r_:~e.indexOf("-")?o_:Ac(t,e)},core:{_removeProperty:xs,_getMatrix:Ic}};Je.utils.checkPrefix=ir;Je.core.getStyleSaver=yd;(function(s,t,e,i){var n=Ke(s+","+t+","+e,function(r){_n[r]=1});Ke(t,function(r){di.units[r]="deg",Md[r]=1}),ji[n[13]]=s+","+t,Ke(i,function(r){var o=r.split(":");ji[o[1]]=n[o[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");Ke("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(s){di.units[s]="px"});Je.registerPlugin(Pd);var ue=Je.registerPlugin(Pd)||Je;ue.core.Tween;function v_(s,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(s,i.key,i)}}function w_(s,t,e){return t&&v_(s.prototype,t),s}/*!
 * Observer 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var ye,ia,hi,Pn,Cn,$s,Cd,es,Br,Dd,ln,Ai,Od,Ad=function(){return ye||typeof window<"u"&&(ye=window.gsap)&&ye.registerPlugin&&ye},Ed=1,Ws=[],ct=[],qi=[],Nr=Date.now,jl=function(t,e){return e},S_=function(){var t=Br.core,e=t.bridge||{},i=t._scrollers,n=t._proxies;i.push.apply(i,ct),n.push.apply(n,qi),ct=i,qi=n,jl=function(o,a){return e[o](a)}},Fn=function(t,e){return~qi.indexOf(t)&&qi[qi.indexOf(t)+1][e]},Wr=function(t){return!!~Dd.indexOf(t)},Be=function(t,e,i,n,r){return t.addEventListener(e,i,{passive:n!==!1,capture:!!r})},ze=function(t,e,i,n){return t.removeEventListener(e,i,!!n)},ko="scrollLeft",To="scrollTop",Ul=function(){return ln&&ln.isPressed||ct.cache++},ya=function(t,e){var i=function n(r){if(r||r===0){Ed&&(hi.history.scrollRestoration="manual");var o=ln&&ln.isPressed;r=n.v=Math.round(r)||(ln&&ln.iOS?1:0),t(r),n.cacheID=ct.cache,o&&jl("ss",r)}else(e||ct.cache!==n.cacheID||jl("ref"))&&(n.cacheID=ct.cache,n.v=t());return n.v+n.offset};return i.offset=0,t&&i},Ye={s:ko,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:ya(function(s){return arguments.length?hi.scrollTo(s,ce.sc()):hi.pageXOffset||Pn[ko]||Cn[ko]||$s[ko]||0})},ce={s:To,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Ye,sc:ya(function(s){return arguments.length?hi.scrollTo(Ye.sc(),s):hi.pageYOffset||Pn[To]||Cn[To]||$s[To]||0})},Ue=function(t,e){return(e&&e._ctx&&e._ctx.selector||ye.utils.toArray)(t)[0]||(typeof t=="string"&&ye.config().nullTargetWarn!==!1?console.warn("Element not found:",t):null)},M_=function(t,e){for(var i=e.length;i--;)if(e[i]===t||e[i].contains(t))return!0;return!1},Nn=function(t,e){var i=e.s,n=e.sc;Wr(t)&&(t=Pn.scrollingElement||Cn);var r=ct.indexOf(t),o=n===ce.sc?1:2;!~r&&(r=ct.push(t)-1),ct[r+o]||Be(t,"scroll",Ul);var a=ct[r+o],l=a||(ct[r+o]=ya(Fn(t,i),!0)||(Wr(t)?n:ya(function(c){return arguments.length?t[i]=c:t[i]})));return l.target=t,a||(l.smooth=ye.getProperty(t,"scrollBehavior")==="smooth"),l},Gl=function(t,e,i){var n=t,r=t,o=Nr(),a=o,l=e||50,c=Math.max(500,l*3),h=function(p,g){var _=Nr();g||_-o>l?(r=n,n=p,a=o,o=_):i?n+=p:n=r+(p-r)/(_-a)*(o-a)},f=function(){r=n=i?0:n,a=o=0},d=function(p){var g=a,_=r,m=Nr();return(p||p===0)&&p!==n&&h(p),o===a||m-a>c?0:(n+(i?_:-_))/((i?m:o)-g)*1e3};return{update:h,reset:f,getVelocity:d}},fr=function(t,e){return e&&!t._gsapAllow&&t.preventDefault(),t.changedTouches?t.changedTouches[0]:t},Oh=function(t){var e=Math.max.apply(Math,t),i=Math.min.apply(Math,t);return Math.abs(e)>=Math.abs(i)?e:i},Rd=function(){Br=ye.core.globals().ScrollTrigger,Br&&Br.core&&S_()},Ld=function(t){return ye=t||Ad(),!ia&&ye&&typeof document<"u"&&document.body&&(hi=window,Pn=document,Cn=Pn.documentElement,$s=Pn.body,Dd=[hi,Pn,Cn,$s],ye.utils.clamp,Od=ye.core.context||function(){},es="onpointerenter"in $s?"pointer":"mouse",Cd=Zt.isTouch=hi.matchMedia&&hi.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in hi||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?2:0,Ai=Zt.eventTypes=("ontouchstart"in Cn?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Cn?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return Ed=0},500),Rd(),ia=1),ia};Ye.op=ce;ct.cache=0;var Zt=function(){function s(e){this.init(e)}var t=s.prototype;return t.init=function(i){ia||Ld(ye)||console.warn("Please gsap.registerPlugin(Observer)"),Br||Rd();var n=i.tolerance,r=i.dragMinimum,o=i.type,a=i.target,l=i.lineHeight,c=i.debounce,h=i.preventDefault,f=i.onStop,d=i.onStopDelay,u=i.ignore,p=i.wheelSpeed,g=i.event,_=i.onDragStart,m=i.onDragEnd,b=i.onDrag,w=i.onPress,y=i.onRelease,x=i.onRight,k=i.onLeft,v=i.onUp,M=i.onDown,C=i.onChangeX,L=i.onChangeY,D=i.onChange,O=i.onToggleX,F=i.onToggleY,Y=i.onHover,N=i.onHoverEnd,V=i.onMove,W=i.ignoreCheck,G=i.isNormalizer,Z=i.onGestureStart,P=i.onGestureEnd,q=i.onWheel,J=i.onEnable,ht=i.onDisable,Q=i.onClick,Pt=i.scrollSpeed,ut=i.capture,Ot=i.allowClicks,xt=i.lockAxis,te=i.onLockAxis;this.target=a=Ue(a)||Cn,this.vars=i,u&&(u=ye.utils.toArray(u)),n=n||1e-9,r=r||0,p=p||1,Pt=Pt||1,o=o||"wheel,touch,pointer",c=c!==!1,l||(l=parseFloat(hi.getComputedStyle($s).lineHeight)||22);var _e,It,$t,rt,ot,et,we,A=this,Rt=0,ti=0,_i=i.passive||!h&&i.passive!==!1,Lt=Nn(a,Ye),Wt=Nn(a,ce),Di=Lt(),mi=Wt(),qt=~o.indexOf("touch")&&!~o.indexOf("pointer")&&Ai[0]==="pointerdown",re=Wr(a),Mt=a.ownerDocument||Pn,Ie=[0,0,0],$e=[0,0,0],ei=0,Xn=function(){return ei=Nr()},Xt=function(E,z){return(A.event=E)&&u&&M_(E.target,u)||z&&qt&&E.pointerType!=="touch"||W&&W(E,z)},bn=function(){A._vx.reset(),A._vy.reset(),It.pause(),f&&f(A)},bi=function(){var E=A.deltaX=Oh(Ie),z=A.deltaY=Oh($e),R=Math.abs(E)>=n,H=Math.abs(z)>=n;D&&(R||H)&&D(A,E,z,Ie,$e),R&&(x&&A.deltaX>0&&x(A),k&&A.deltaX<0&&k(A),C&&C(A),O&&A.deltaX<0!=Rt<0&&O(A),Rt=A.deltaX,Ie[0]=Ie[1]=Ie[2]=0),H&&(M&&A.deltaY>0&&M(A),v&&A.deltaY<0&&v(A),L&&L(A),F&&A.deltaY<0!=ti<0&&F(A),ti=A.deltaY,$e[0]=$e[1]=$e[2]=0),(rt||$t)&&(V&&V(A),$t&&(_&&$t===1&&_(A),b&&b(A),$t=0),rt=!1),et&&!(et=!1)&&te&&te(A),ot&&(q(A),ot=!1),_e=0},Oi=function(E,z,R){Ie[R]+=E,$e[R]+=z,A._vx.update(E),A._vy.update(z),c?_e||(_e=requestAnimationFrame(bi)):bi()},xi=function(E,z){xt&&!we&&(A.axis=we=Math.abs(E)>Math.abs(z)?"x":"y",et=!0),we!=="y"&&(Ie[2]+=E,A._vx.update(E,!0)),we!=="x"&&($e[2]+=z,A._vy.update(z,!0)),c?_e||(_e=requestAnimationFrame(bi)):bi()},zi=function(E){if(!Xt(E,1)){E=fr(E,h);var z=E.clientX,R=E.clientY,H=z-A.x,I=R-A.y,$=A.isDragging;A.x=z,A.y=R,($||(H||I)&&(Math.abs(A.startX-z)>=r||Math.abs(A.startY-R)>=r))&&($t=$?2:1,$||(A.isDragging=!0),xi(H,I))}},ii=A.onPress=function(T){Xt(T,1)||T&&T.button||(A.axis=we=null,It.pause(),A.isPressed=!0,T=fr(T),Rt=ti=0,A.startX=A.x=T.clientX,A.startY=A.y=T.clientY,A._vx.reset(),A._vy.reset(),Be(G?a:Mt,Ai[1],zi,_i,!0),A.deltaX=A.deltaY=0,w&&w(A))},tt=A.onRelease=function(T){if(!Xt(T,1)){ze(G?a:Mt,Ai[1],zi,!0);var E=!isNaN(A.y-A.startY),z=A.isDragging,R=z&&(Math.abs(A.x-A.startX)>3||Math.abs(A.y-A.startY)>3),H=fr(T);!R&&E&&(A._vx.reset(),A._vy.reset(),h&&Ot&&ye.delayedCall(.08,function(){if(Nr()-ei>300&&!T.defaultPrevented){if(T.target.click)T.target.click();else if(Mt.createEvent){var I=Mt.createEvent("MouseEvents");I.initMouseEvent("click",!0,!0,hi,1,H.screenX,H.screenY,H.clientX,H.clientY,!1,!1,!1,!1,0,null),T.target.dispatchEvent(I)}}})),A.isDragging=A.isGesturing=A.isPressed=!1,f&&z&&!G&&It.restart(!0),$t&&bi(),m&&z&&m(A),y&&y(A,R)}},ni=function(E){return E.touches&&E.touches.length>1&&(A.isGesturing=!0)&&Z(E,A.isDragging)},Vt=function(){return(A.isGesturing=!1)||P(A)},Se=function(E){if(!Xt(E)){var z=Lt(),R=Wt();Oi((z-Di)*Pt,(R-mi)*Pt,1),Di=z,mi=R,f&&It.restart(!0)}},Xe=function(E){if(!Xt(E)){E=fr(E,h),q&&(ot=!0);var z=(E.deltaMode===1?l:E.deltaMode===2?hi.innerHeight:1)*p;Oi(E.deltaX*z,E.deltaY*z,0),f&&!G&&It.restart(!0)}},Bi=function(E){if(!Xt(E)){var z=E.clientX,R=E.clientY,H=z-A.x,I=R-A.y;A.x=z,A.y=R,rt=!0,f&&It.restart(!0),(H||I)&&xi(H,I)}},U=function(E){A.event=E,Y(A)},S=function(E){A.event=E,N(A)},B=function(E){return Xt(E)||fr(E,h)&&Q(A)};It=A._dc=ye.delayedCall(d||.25,bn).pause(),A.deltaX=A.deltaY=0,A._vx=Gl(0,50,!0),A._vy=Gl(0,50,!0),A.scrollX=Lt,A.scrollY=Wt,A.isDragging=A.isGesturing=A.isPressed=!1,Od(this),A.enable=function(T){return A.isEnabled||(Be(re?Mt:a,"scroll",Ul),o.indexOf("scroll")>=0&&Be(re?Mt:a,"scroll",Se,_i,ut),o.indexOf("wheel")>=0&&Be(a,"wheel",Xe,_i,ut),(o.indexOf("touch")>=0&&Cd||o.indexOf("pointer")>=0)&&(Be(a,Ai[0],ii,_i,ut),Be(Mt,Ai[2],tt),Be(Mt,Ai[3],tt),Ot&&Be(a,"click",Xn,!0,!0),Q&&Be(a,"click",B),Z&&Be(Mt,"gesturestart",ni),P&&Be(Mt,"gestureend",Vt),Y&&Be(a,es+"enter",U),N&&Be(a,es+"leave",S),V&&Be(a,es+"move",Bi)),A.isEnabled=!0,A.isDragging=A.isGesturing=A.isPressed=rt=$t=!1,A._vx.reset(),A._vy.reset(),Di=Lt(),mi=Wt(),T&&T.type&&ii(T),J&&J(A)),A},A.disable=function(){A.isEnabled&&(Ws.filter(function(T){return T!==A&&Wr(T.target)}).length||ze(re?Mt:a,"scroll",Ul),A.isPressed&&(A._vx.reset(),A._vy.reset(),ze(G?a:Mt,Ai[1],zi,!0)),ze(re?Mt:a,"scroll",Se,ut),ze(a,"wheel",Xe,ut),ze(a,Ai[0],ii,ut),ze(Mt,Ai[2],tt),ze(Mt,Ai[3],tt),ze(a,"click",Xn,!0),ze(a,"click",B),ze(Mt,"gesturestart",ni),ze(Mt,"gestureend",Vt),ze(a,es+"enter",U),ze(a,es+"leave",S),ze(a,es+"move",Bi),A.isEnabled=A.isPressed=A.isDragging=!1,ht&&ht(A))},A.kill=A.revert=function(){A.disable();var T=Ws.indexOf(A);T>=0&&Ws.splice(T,1),ln===A&&(ln=0)},Ws.push(A),G&&Wr(a)&&(ln=A),A.enable(g)},w_(s,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),s}();Zt.version="3.13.0";Zt.create=function(s){return new Zt(s)};Zt.register=Ld;Zt.getAll=function(){return Ws.slice()};Zt.getById=function(s){return Ws.filter(function(t){return t.vars.id===s})[0]};Ad()&&ye.registerPlugin(Zt);/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var X,Is,lt,At,li,St,zc,va,ao,Vr,wr,Po,Te,Ba,ql,We,Ah,Eh,zs,Fd,nl,Id,Ne,Kl,zd,Bd,wn,Ql,Bc,Xs,Nc,wa,Zl,sl,Co=1,Ce=Date.now,rl=Ce(),Ci=0,Sr=0,Rh=function(t,e,i){var n=oi(t)&&(t.substr(0,6)==="clamp("||t.indexOf("max")>-1);return i["_"+e+"Clamp"]=n,n?t.substr(6,t.length-7):t},Lh=function(t,e){return e&&(!oi(t)||t.substr(0,6)!=="clamp(")?"clamp("+t+")":t},k_=function s(){return Sr&&requestAnimationFrame(s)},Fh=function(){return Ba=1},Ih=function(){return Ba=0},$i=function(t){return t},Mr=function(t){return Math.round(t*1e5)/1e5||0},Nd=function(){return typeof window<"u"},Wd=function(){return X||Nd()&&(X=window.gsap)&&X.registerPlugin&&X},ys=function(t){return!!~zc.indexOf(t)},Vd=function(t){return(t==="Height"?Nc:lt["inner"+t])||li["client"+t]||St["client"+t]},Hd=function(t){return Fn(t,"getBoundingClientRect")||(ys(t)?function(){return aa.width=lt.innerWidth,aa.height=Nc,aa}:function(){return an(t)})},T_=function(t,e,i){var n=i.d,r=i.d2,o=i.a;return(o=Fn(t,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(e?Vd(r):t["client"+r])||0}},P_=function(t,e){return!e||~qi.indexOf(t)?Hd(t):function(){return aa}},Ui=function(t,e){var i=e.s,n=e.d2,r=e.d,o=e.a;return Math.max(0,(i="scroll"+n)&&(o=Fn(t,i))?o()-Hd(t)()[r]:ys(t)?(li[i]||St[i])-Vd(n):t[i]-t["offset"+n])},Do=function(t,e){for(var i=0;i<zs.length;i+=3)(!e||~e.indexOf(zs[i+1]))&&t(zs[i],zs[i+1],zs[i+2])},oi=function(t){return typeof t=="string"},Ae=function(t){return typeof t=="function"},kr=function(t){return typeof t=="number"},is=function(t){return typeof t=="object"},dr=function(t,e,i){return t&&t.progress(e?0:1)&&i&&t.pause()},ol=function(t,e){if(t.enabled){var i=t._ctx?t._ctx.add(function(){return e(t)}):e(t);i&&i.totalTime&&(t.callbackAnimation=i)}},Ds=Math.abs,Yd="left",$d="top",Wc="right",Vc="bottom",fs="width",ds="height",Hr="Right",Yr="Left",$r="Top",Xr="Bottom",ie="padding",Mi="margin",nr="Width",Hc="Height",ae="px",ki=function(t){return lt.getComputedStyle(t)},C_=function(t){var e=ki(t).position;t.style.position=e==="absolute"||e==="fixed"?e:"relative"},zh=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},an=function(t,e){var i=e&&ki(t)[ql]!=="matrix(1, 0, 0, 1, 0, 0)"&&X.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=t.getBoundingClientRect();return i&&i.progress(0).kill(),n},Sa=function(t,e){var i=e.d2;return t["offset"+i]||t["client"+i]||0},Xd=function(t){var e=[],i=t.labels,n=t.duration(),r;for(r in i)e.push(i[r]/n);return e},D_=function(t){return function(e){return X.utils.snap(Xd(t),e)}},Yc=function(t){var e=X.utils.snap(t),i=Array.isArray(t)&&t.slice(0).sort(function(n,r){return n-r});return i?function(n,r,o){o===void 0&&(o=.001);var a;if(!r)return e(n);if(r>0){for(n-=o,a=0;a<i.length;a++)if(i[a]>=n)return i[a];return i[a-1]}else for(a=i.length,n+=o;a--;)if(i[a]<=n)return i[a];return i[0]}:function(n,r,o){o===void 0&&(o=.001);var a=e(n);return!r||Math.abs(a-n)<o||a-n<0==r<0?a:e(r<0?n-t:n+t)}},O_=function(t){return function(e,i){return Yc(Xd(t))(e,i.direction)}},Oo=function(t,e,i,n){return i.split(",").forEach(function(r){return t(e,r,n)})},de=function(t,e,i,n,r){return t.addEventListener(e,i,{passive:!n,capture:!!r})},fe=function(t,e,i,n){return t.removeEventListener(e,i,!!n)},Ao=function(t,e,i){i=i&&i.wheelHandler,i&&(t(e,"wheel",i),t(e,"touchmove",i))},Bh={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Eo={toggleActions:"play",anticipatePin:0},Ma={top:0,left:0,center:.5,bottom:1,right:1},na=function(t,e){if(oi(t)){var i=t.indexOf("="),n=~i?+(t.charAt(i-1)+1)*parseFloat(t.substr(i+1)):0;~i&&(t.indexOf("%")>i&&(n*=e/100),t=t.substr(0,i-1)),t=n+(t in Ma?Ma[t]*e:~t.indexOf("%")?parseFloat(t)*e/100:parseFloat(t)||0)}return t},Ro=function(t,e,i,n,r,o,a,l){var c=r.startColor,h=r.endColor,f=r.fontSize,d=r.indent,u=r.fontWeight,p=At.createElement("div"),g=ys(i)||Fn(i,"pinType")==="fixed",_=t.indexOf("scroller")!==-1,m=g?St:i,b=t.indexOf("start")!==-1,w=b?c:h,y="border-color:"+w+";font-size:"+f+";color:"+w+";font-weight:"+u+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((_||l)&&g?"fixed;":"absolute;"),(_||l||!g)&&(y+=(n===ce?Wc:Vc)+":"+(o+parseFloat(d))+"px;"),a&&(y+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),p._isStart=b,p.setAttribute("class","gsap-marker-"+t+(e?" marker-"+e:"")),p.style.cssText=y,p.innerText=e||e===0?t+"-"+e:t,m.children[0]?m.insertBefore(p,m.children[0]):m.appendChild(p),p._offset=p["offset"+n.op.d2],sa(p,0,n,b),p},sa=function(t,e,i,n){var r={display:"block"},o=i[n?"os2":"p2"],a=i[n?"p2":"os2"];t._isFlipped=n,r[i.a+"Percent"]=n?-100:0,r[i.a]=n?"1px":0,r["border"+o+nr]=1,r["border"+a+nr]=0,r[i.p]=e+"px",X.set(t,r)},at=[],Jl={},lo,Nh=function(){return Ce()-Ci>34&&(lo||(lo=requestAnimationFrame(gn)))},Os=function(){(!Ne||!Ne.isPressed||Ne.startX>St.clientWidth)&&(ct.cache++,Ne?lo||(lo=requestAnimationFrame(gn)):gn(),Ci||ws("scrollStart"),Ci=Ce())},al=function(){Bd=lt.innerWidth,zd=lt.innerHeight},Tr=function(t){ct.cache++,(t===!0||!Te&&!Id&&!At.fullscreenElement&&!At.webkitFullscreenElement&&(!Kl||Bd!==lt.innerWidth||Math.abs(lt.innerHeight-zd)>lt.innerHeight*.25))&&va.restart(!0)},vs={},A_=[],jd=function s(){return fe(gt,"scrollEnd",s)||as(!0)},ws=function(t){return vs[t]&&vs[t].map(function(e){return e()})||A_},ri=[],Ud=function(t){for(var e=0;e<ri.length;e+=5)(!t||ri[e+4]&&ri[e+4].query===t)&&(ri[e].style.cssText=ri[e+1],ri[e].getBBox&&ri[e].setAttribute("transform",ri[e+2]||""),ri[e+3].uncache=1)},$c=function(t,e){var i;for(We=0;We<at.length;We++)i=at[We],i&&(!e||i._ctx===e)&&(t?i.kill(1):i.revert(!0,!0));wa=!0,e&&Ud(e),e||ws("revert")},Gd=function(t,e){ct.cache++,(e||!Ve)&&ct.forEach(function(i){return Ae(i)&&i.cacheID++&&(i.rec=0)}),oi(t)&&(lt.history.scrollRestoration=Bc=t)},Ve,gs=0,Wh,E_=function(){if(Wh!==gs){var t=Wh=gs;requestAnimationFrame(function(){return t===gs&&as(!0)})}},qd=function(){St.appendChild(Xs),Nc=!Ne&&Xs.offsetHeight||lt.innerHeight,St.removeChild(Xs)},Vh=function(t){return ao(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})},as=function(t,e){if(li=At.documentElement,St=At.body,zc=[lt,At,li,St],Ci&&!t&&!wa){de(gt,"scrollEnd",jd);return}qd(),Ve=gt.isRefreshing=!0,ct.forEach(function(n){return Ae(n)&&++n.cacheID&&(n.rec=n())});var i=ws("refreshInit");Fd&&gt.sort(),e||$c(),ct.forEach(function(n){Ae(n)&&(n.smooth&&(n.target.style.scrollBehavior="auto"),n(0))}),at.slice(0).forEach(function(n){return n.refresh()}),wa=!1,at.forEach(function(n){if(n._subPinOffset&&n.pin){var r=n.vars.horizontal?"offsetWidth":"offsetHeight",o=n.pin[r];n.revert(!0,1),n.adjustPinSpacing(n.pin[r]-o),n.refresh()}}),Zl=1,Vh(!0),at.forEach(function(n){var r=Ui(n.scroller,n._dir),o=n.vars.end==="max"||n._endClamp&&n.end>r,a=n._startClamp&&n.start>=r;(o||a)&&n.setPositions(a?r-1:n.start,o?Math.max(a?r:n.start+1,r):n.end,!0)}),Vh(!1),Zl=0,i.forEach(function(n){return n&&n.render&&n.render(-1)}),ct.forEach(function(n){Ae(n)&&(n.smooth&&requestAnimationFrame(function(){return n.target.style.scrollBehavior="smooth"}),n.rec&&n(n.rec))}),Gd(Bc,1),va.pause(),gs++,Ve=2,gn(2),at.forEach(function(n){return Ae(n.vars.onRefresh)&&n.vars.onRefresh(n)}),Ve=gt.isRefreshing=!1,ws("refresh")},tc=0,ra=1,jr,gn=function(t){if(t===2||!Ve&&!wa){gt.isUpdating=!0,jr&&jr.update(0);var e=at.length,i=Ce(),n=i-rl>=50,r=e&&at[0].scroll();if(ra=tc>r?-1:1,Ve||(tc=r),n&&(Ci&&!Ba&&i-Ci>200&&(Ci=0,ws("scrollEnd")),wr=rl,rl=i),ra<0){for(We=e;We-- >0;)at[We]&&at[We].update(0,n);ra=1}else for(We=0;We<e;We++)at[We]&&at[We].update(0,n);gt.isUpdating=!1}lo=0},ec=[Yd,$d,Vc,Wc,Mi+Xr,Mi+Hr,Mi+$r,Mi+Yr,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],oa=ec.concat([fs,ds,"boxSizing","max"+nr,"max"+Hc,"position",Mi,ie,ie+$r,ie+Hr,ie+Xr,ie+Yr]),R_=function(t,e,i){js(i);var n=t._gsap;if(n.spacerIsNative)js(n.spacerState);else if(t._gsap.swappedIn){var r=e.parentNode;r&&(r.insertBefore(t,e),r.removeChild(e))}t._gsap.swappedIn=!1},ll=function(t,e,i,n){if(!t._gsap.swappedIn){for(var r=ec.length,o=e.style,a=t.style,l;r--;)l=ec[r],o[l]=i[l];o.position=i.position==="absolute"?"absolute":"relative",i.display==="inline"&&(o.display="inline-block"),a[Vc]=a[Wc]="auto",o.flexBasis=i.flexBasis||"auto",o.overflow="visible",o.boxSizing="border-box",o[fs]=Sa(t,Ye)+ae,o[ds]=Sa(t,ce)+ae,o[ie]=a[Mi]=a[$d]=a[Yd]="0",js(n),a[fs]=a["max"+nr]=i[fs],a[ds]=a["max"+Hc]=i[ds],a[ie]=i[ie],t.parentNode!==e&&(t.parentNode.insertBefore(e,t),e.appendChild(t)),t._gsap.swappedIn=!0}},L_=/([A-Z])/g,js=function(t){if(t){var e=t.t.style,i=t.length,n=0,r,o;for((t.t._gsap||X.core.getCache(t.t)).uncache=1;n<i;n+=2)o=t[n+1],r=t[n],o?e[r]=o:e[r]&&e.removeProperty(r.replace(L_,"-$1").toLowerCase())}},Lo=function(t){for(var e=oa.length,i=t.style,n=[],r=0;r<e;r++)n.push(oa[r],i[oa[r]]);return n.t=t,n},F_=function(t,e,i){for(var n=[],r=t.length,o=i?8:0,a;o<r;o+=2)a=t[o],n.push(a,a in e?e[a]:t[o+1]);return n.t=t.t,n},aa={left:0,top:0},Hh=function(t,e,i,n,r,o,a,l,c,h,f,d,u,p){Ae(t)&&(t=t(l)),oi(t)&&t.substr(0,3)==="max"&&(t=d+(t.charAt(4)==="="?na("0"+t.substr(3),i):0));var g=u?u.time():0,_,m,b;if(u&&u.seek(0),isNaN(t)||(t=+t),kr(t))u&&(t=X.utils.mapRange(u.scrollTrigger.start,u.scrollTrigger.end,0,d,t)),a&&sa(a,i,n,!0);else{Ae(e)&&(e=e(l));var w=(t||"0").split(" "),y,x,k,v;b=Ue(e,l)||St,y=an(b)||{},(!y||!y.left&&!y.top)&&ki(b).display==="none"&&(v=b.style.display,b.style.display="block",y=an(b),v?b.style.display=v:b.style.removeProperty("display")),x=na(w[0],y[n.d]),k=na(w[1]||"0",i),t=y[n.p]-c[n.p]-h+x+r-k,a&&sa(a,k,n,i-k<20||a._isStart&&k>20),i-=i-k}if(p&&(l[p]=t||-.001,t<0&&(t=0)),o){var M=t+i,C=o._isStart;_="scroll"+n.d2,sa(o,M,n,C&&M>20||!C&&(f?Math.max(St[_],li[_]):o.parentNode[_])<=M+1),f&&(c=an(a),f&&(o.style[n.op.p]=c[n.op.p]-n.op.m-o._offset+ae))}return u&&b&&(_=an(b),u.seek(d),m=an(b),u._caScrollDist=_[n.p]-m[n.p],t=t/u._caScrollDist*d),u&&u.seek(g),u?t:Math.round(t)},I_=/(webkit|moz|length|cssText|inset)/i,Yh=function(t,e,i,n){if(t.parentNode!==e){var r=t.style,o,a;if(e===St){t._stOrig=r.cssText,a=ki(t);for(o in a)!+o&&!I_.test(o)&&a[o]&&typeof r[o]=="string"&&o!=="0"&&(r[o]=a[o]);r.top=i,r.left=n}else r.cssText=t._stOrig;X.core.getCache(t).uncache=1,e.appendChild(t)}},Kd=function(t,e,i){var n=e,r=n;return function(o){var a=Math.round(t());return a!==n&&a!==r&&Math.abs(a-n)>3&&Math.abs(a-r)>3&&(o=a,i&&i()),r=n,n=Math.round(o),n}},Fo=function(t,e,i){var n={};n[e.p]="+="+i,X.set(t,n)},$h=function(t,e){var i=Nn(t,e),n="_scroll"+e.p2,r=function o(a,l,c,h,f){var d=o.tween,u=l.onComplete,p={};c=c||i();var g=Kd(i,c,function(){d.kill(),o.tween=0});return f=h&&f||0,h=h||a-c,d&&d.kill(),l[n]=a,l.inherit=!1,l.modifiers=p,p[n]=function(){return g(c+h*d.ratio+f*d.ratio*d.ratio)},l.onUpdate=function(){ct.cache++,o.tween&&gn()},l.onComplete=function(){o.tween=0,u&&u.call(d)},d=o.tween=X.to(t,l),d};return t[n]=i,i.wheelHandler=function(){return r.tween&&r.tween.kill()&&(r.tween=0)},de(t,"wheel",i.wheelHandler),gt.isTouch&&de(t,"touchmove",i.wheelHandler),r},gt=function(){function s(e,i){Is||s.register(X)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),Ql(this),this.init(e,i)}var t=s.prototype;return t.init=function(i,n){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!Sr){this.update=this.refresh=this.kill=$i;return}i=zh(oi(i)||kr(i)||i.nodeType?{trigger:i}:i,Eo);var r=i,o=r.onUpdate,a=r.toggleClass,l=r.id,c=r.onToggle,h=r.onRefresh,f=r.scrub,d=r.trigger,u=r.pin,p=r.pinSpacing,g=r.invalidateOnRefresh,_=r.anticipatePin,m=r.onScrubComplete,b=r.onSnapComplete,w=r.once,y=r.snap,x=r.pinReparent,k=r.pinSpacer,v=r.containerAnimation,M=r.fastScrollEnd,C=r.preventOverlaps,L=i.horizontal||i.containerAnimation&&i.horizontal!==!1?Ye:ce,D=!f&&f!==0,O=Ue(i.scroller||lt),F=X.core.getCache(O),Y=ys(O),N=("pinType"in i?i.pinType:Fn(O,"pinType")||Y&&"fixed")==="fixed",V=[i.onEnter,i.onLeave,i.onEnterBack,i.onLeaveBack],W=D&&i.toggleActions.split(" "),G="markers"in i?i.markers:Eo.markers,Z=Y?0:parseFloat(ki(O)["border"+L.p2+nr])||0,P=this,q=i.onRefreshInit&&function(){return i.onRefreshInit(P)},J=T_(O,Y,L),ht=P_(O,Y),Q=0,Pt=0,ut=0,Ot=Nn(O,L),xt,te,_e,It,$t,rt,ot,et,we,A,Rt,ti,_i,Lt,Wt,Di,mi,qt,re,Mt,Ie,$e,ei,Xn,Xt,bn,bi,Oi,xi,zi,ii,tt,ni,Vt,Se,Xe,Bi,U,S;if(P._startClamp=P._endClamp=!1,P._dir=L,_*=45,P.scroller=O,P.scroll=v?v.time.bind(v):Ot,It=Ot(),P.vars=i,n=n||i.animation,"refreshPriority"in i&&(Fd=1,i.refreshPriority===-9999&&(jr=P)),F.tweenScroll=F.tweenScroll||{top:$h(O,ce),left:$h(O,Ye)},P.tweenTo=xt=F.tweenScroll[L.p],P.scrubDuration=function(R){ni=kr(R)&&R,ni?tt?tt.duration(R):tt=X.to(n,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:ni,paused:!0,onComplete:function(){return m&&m(P)}}):(tt&&tt.progress(1).kill(),tt=0)},n&&(n.vars.lazy=!1,n._initted&&!P.isReverted||n.vars.immediateRender!==!1&&i.immediateRender!==!1&&n.duration()&&n.render(0,!0,!0),P.animation=n.pause(),n.scrollTrigger=P,P.scrubDuration(f),zi=0,l||(l=n.vars.id)),y&&((!is(y)||y.push)&&(y={snapTo:y}),"scrollBehavior"in St.style&&X.set(Y?[St,li]:O,{scrollBehavior:"auto"}),ct.forEach(function(R){return Ae(R)&&R.target===(Y?At.scrollingElement||li:O)&&(R.smooth=!1)}),_e=Ae(y.snapTo)?y.snapTo:y.snapTo==="labels"?D_(n):y.snapTo==="labelsDirectional"?O_(n):y.directional!==!1?function(R,H){return Yc(y.snapTo)(R,Ce()-Pt<500?0:H.direction)}:X.utils.snap(y.snapTo),Vt=y.duration||{min:.1,max:2},Vt=is(Vt)?Vr(Vt.min,Vt.max):Vr(Vt,Vt),Se=X.delayedCall(y.delay||ni/2||.1,function(){var R=Ot(),H=Ce()-Pt<500,I=xt.tween;if((H||Math.abs(P.getVelocity())<10)&&!I&&!Ba&&Q!==R){var $=(R-rt)/Lt,K=n&&!D?n.totalProgress():$,j=H?0:(K-ii)/(Ce()-wr)*1e3||0,st=X.utils.clamp(-$,1-$,Ds(j/2)*j/.185),yt=$+(y.inertia===!1?0:st),vt,ft,pt=y,kt=pt.onStart,it=pt.onInterrupt,me=pt.onComplete;if(vt=_e(yt,P),kr(vt)||(vt=yt),ft=Math.max(0,Math.round(rt+vt*Lt)),R<=ot&&R>=rt&&ft!==R){if(I&&!I._initted&&I.data<=Ds(ft-R))return;y.inertia===!1&&(st=vt-$),xt(ft,{duration:Vt(Ds(Math.max(Ds(yt-K),Ds(vt-K))*.185/j/.05||0)),ease:y.ease||"power3",data:Ds(ft-R),onInterrupt:function(){return Se.restart(!0)&&it&&it(P)},onComplete:function(){P.update(),Q=Ot(),n&&!D&&(tt?tt.resetTo("totalProgress",vt,n._tTime/n._tDur):n.progress(vt)),zi=ii=n&&!D?n.totalProgress():P.progress,b&&b(P),me&&me(P)}},R,st*Lt,ft-R-st*Lt),kt&&kt(P,xt.tween)}}else P.isActive&&Q!==R&&Se.restart(!0)}).pause()),l&&(Jl[l]=P),d=P.trigger=Ue(d||u!==!0&&u),S=d&&d._gsap&&d._gsap.stRevert,S&&(S=S(P)),u=u===!0?d:Ue(u),oi(a)&&(a={targets:d,className:a}),u&&(p===!1||p===Mi||(p=!p&&u.parentNode&&u.parentNode.style&&ki(u.parentNode).display==="flex"?!1:ie),P.pin=u,te=X.core.getCache(u),te.spacer?Wt=te.pinState:(k&&(k=Ue(k),k&&!k.nodeType&&(k=k.current||k.nativeElement),te.spacerIsNative=!!k,k&&(te.spacerState=Lo(k))),te.spacer=qt=k||At.createElement("div"),qt.classList.add("pin-spacer"),l&&qt.classList.add("pin-spacer-"+l),te.pinState=Wt=Lo(u)),i.force3D!==!1&&X.set(u,{force3D:!0}),P.spacer=qt=te.spacer,xi=ki(u),Xn=xi[p+L.os2],Mt=X.getProperty(u),Ie=X.quickSetter(u,L.a,ae),ll(u,qt,xi),mi=Lo(u)),G){ti=is(G)?zh(G,Bh):Bh,A=Ro("scroller-start",l,O,L,ti,0),Rt=Ro("scroller-end",l,O,L,ti,0,A),re=A["offset"+L.op.d2];var B=Ue(Fn(O,"content")||O);et=this.markerStart=Ro("start",l,B,L,ti,re,0,v),we=this.markerEnd=Ro("end",l,B,L,ti,re,0,v),v&&(U=X.quickSetter([et,we],L.a,ae)),!N&&!(qi.length&&Fn(O,"fixedMarkers")===!0)&&(C_(Y?St:O),X.set([A,Rt],{force3D:!0}),bn=X.quickSetter(A,L.a,ae),Oi=X.quickSetter(Rt,L.a,ae))}if(v){var T=v.vars.onUpdate,E=v.vars.onUpdateParams;v.eventCallback("onUpdate",function(){P.update(0,0,1),T&&T.apply(v,E||[])})}if(P.previous=function(){return at[at.indexOf(P)-1]},P.next=function(){return at[at.indexOf(P)+1]},P.revert=function(R,H){if(!H)return P.kill(!0);var I=R!==!1||!P.enabled,$=Te;I!==P.isReverted&&(I&&(Xe=Math.max(Ot(),P.scroll.rec||0),ut=P.progress,Bi=n&&n.progress()),et&&[et,we,A,Rt].forEach(function(K){return K.style.display=I?"none":"block"}),I&&(Te=P,P.update(I)),u&&(!x||!P.isActive)&&(I?R_(u,qt,Wt):ll(u,qt,ki(u),Xt)),I||P.update(I),Te=$,P.isReverted=I)},P.refresh=function(R,H,I,$){if(!((Te||!P.enabled)&&!H)){if(u&&R&&Ci){de(s,"scrollEnd",jd);return}!Ve&&q&&q(P),Te=P,xt.tween&&!I&&(xt.tween.kill(),xt.tween=0),tt&&tt.pause(),g&&n&&(n.revert({kill:!1}).invalidate(),n.getChildren&&n.getChildren(!0,!0,!1).forEach(function(xn){return xn.vars.immediateRender&&xn.render(0,!0,!0)})),P.isReverted||P.revert(!0,!0),P._subPinOffset=!1;var K=J(),j=ht(),st=v?v.duration():Ui(O,L),yt=Lt<=.01||!Lt,vt=0,ft=$||0,pt=is(I)?I.end:i.end,kt=i.endTrigger||d,it=is(I)?I.start:i.start||(i.start===0||!d?0:u?"0 0":"0 100%"),me=P.pinnedContainer=i.pinnedContainer&&Ue(i.pinnedContainer,P),jt=d&&Math.max(0,at.indexOf(P))||0,be=jt,xe,Me,jn,wo,ke,oe,Ni,Ga,fh,cr,Wi,hr,So;for(G&&is(I)&&(hr=X.getProperty(A,L.p),So=X.getProperty(Rt,L.p));be-- >0;)oe=at[be],oe.end||oe.refresh(0,1)||(Te=P),Ni=oe.pin,Ni&&(Ni===d||Ni===u||Ni===me)&&!oe.isReverted&&(cr||(cr=[]),cr.unshift(oe),oe.revert(!0,!0)),oe!==at[be]&&(jt--,be--);for(Ae(it)&&(it=it(P)),it=Rh(it,"start",P),rt=Hh(it,d,K,L,Ot(),et,A,P,j,Z,N,st,v,P._startClamp&&"_startClamp")||(u?-.001:0),Ae(pt)&&(pt=pt(P)),oi(pt)&&!pt.indexOf("+=")&&(~pt.indexOf(" ")?pt=(oi(it)?it.split(" ")[0]:"")+pt:(vt=na(pt.substr(2),K),pt=oi(it)?it:(v?X.utils.mapRange(0,v.duration(),v.scrollTrigger.start,v.scrollTrigger.end,rt):rt)+vt,kt=d)),pt=Rh(pt,"end",P),ot=Math.max(rt,Hh(pt||(kt?"100% 0":st),kt,K,L,Ot()+vt,we,Rt,P,j,Z,N,st,v,P._endClamp&&"_endClamp"))||-.001,vt=0,be=jt;be--;)oe=at[be],Ni=oe.pin,Ni&&oe.start-oe._pinPush<=rt&&!v&&oe.end>0&&(xe=oe.end-(P._startClamp?Math.max(0,oe.start):oe.start),(Ni===d&&oe.start-oe._pinPush<rt||Ni===me)&&isNaN(it)&&(vt+=xe*(1-oe.progress)),Ni===u&&(ft+=xe));if(rt+=vt,ot+=vt,P._startClamp&&(P._startClamp+=vt),P._endClamp&&!Ve&&(P._endClamp=ot||-.001,ot=Math.min(ot,Ui(O,L))),Lt=ot-rt||(rt-=.01)&&.001,yt&&(ut=X.utils.clamp(0,1,X.utils.normalize(rt,ot,Xe))),P._pinPush=ft,et&&vt&&(xe={},xe[L.a]="+="+vt,me&&(xe[L.p]="-="+Ot()),X.set([et,we],xe)),u&&!(Zl&&P.end>=Ui(O,L)))xe=ki(u),wo=L===ce,jn=Ot(),$e=parseFloat(Mt(L.a))+ft,!st&&ot>1&&(Wi=(Y?At.scrollingElement||li:O).style,Wi={style:Wi,value:Wi["overflow"+L.a.toUpperCase()]},Y&&ki(St)["overflow"+L.a.toUpperCase()]!=="scroll"&&(Wi.style["overflow"+L.a.toUpperCase()]="scroll")),ll(u,qt,xe),mi=Lo(u),Me=an(u,!0),Ga=N&&Nn(O,wo?Ye:ce)(),p?(Xt=[p+L.os2,Lt+ft+ae],Xt.t=qt,be=p===ie?Sa(u,L)+Lt+ft:0,be&&(Xt.push(L.d,be+ae),qt.style.flexBasis!=="auto"&&(qt.style.flexBasis=be+ae)),js(Xt),me&&at.forEach(function(xn){xn.pin===me&&xn.vars.pinSpacing!==!1&&(xn._subPinOffset=!0)}),N&&Ot(Xe)):(be=Sa(u,L),be&&qt.style.flexBasis!=="auto"&&(qt.style.flexBasis=be+ae)),N&&(ke={top:Me.top+(wo?jn-rt:Ga)+ae,left:Me.left+(wo?Ga:jn-rt)+ae,boxSizing:"border-box",position:"fixed"},ke[fs]=ke["max"+nr]=Math.ceil(Me.width)+ae,ke[ds]=ke["max"+Hc]=Math.ceil(Me.height)+ae,ke[Mi]=ke[Mi+$r]=ke[Mi+Hr]=ke[Mi+Xr]=ke[Mi+Yr]="0",ke[ie]=xe[ie],ke[ie+$r]=xe[ie+$r],ke[ie+Hr]=xe[ie+Hr],ke[ie+Xr]=xe[ie+Xr],ke[ie+Yr]=xe[ie+Yr],Di=F_(Wt,ke,x),Ve&&Ot(0)),n?(fh=n._initted,nl(1),n.render(n.duration(),!0,!0),ei=Mt(L.a)-$e+Lt+ft,bi=Math.abs(Lt-ei)>1,N&&bi&&Di.splice(Di.length-2,2),n.render(0,!0,!0),fh||n.invalidate(!0),n.parent||n.totalTime(n.totalTime()),nl(0)):ei=Lt,Wi&&(Wi.value?Wi.style["overflow"+L.a.toUpperCase()]=Wi.value:Wi.style.removeProperty("overflow-"+L.a));else if(d&&Ot()&&!v)for(Me=d.parentNode;Me&&Me!==St;)Me._pinOffset&&(rt-=Me._pinOffset,ot-=Me._pinOffset),Me=Me.parentNode;cr&&cr.forEach(function(xn){return xn.revert(!1,!0)}),P.start=rt,P.end=ot,It=$t=Ve?Xe:Ot(),!v&&!Ve&&(It<Xe&&Ot(Xe),P.scroll.rec=0),P.revert(!1,!0),Pt=Ce(),Se&&(Q=-1,Se.restart(!0)),Te=0,n&&D&&(n._initted||Bi)&&n.progress()!==Bi&&n.progress(Bi||0,!0).render(n.time(),!0,!0),(yt||ut!==P.progress||v||g||n&&!n._initted)&&(n&&!D&&(n._initted||ut||n.vars.immediateRender!==!1)&&n.totalProgress(v&&rt<-.001&&!ut?X.utils.normalize(rt,ot,0):ut,!0),P.progress=yt||(It-rt)/Lt===ut?0:ut),u&&p&&(qt._pinOffset=Math.round(P.progress*ei)),tt&&tt.invalidate(),isNaN(hr)||(hr-=X.getProperty(A,L.p),So-=X.getProperty(Rt,L.p),Fo(A,L,hr),Fo(et,L,hr-($||0)),Fo(Rt,L,So),Fo(we,L,So-($||0))),yt&&!Ve&&P.update(),h&&!Ve&&!_i&&(_i=!0,h(P),_i=!1)}},P.getVelocity=function(){return(Ot()-$t)/(Ce()-wr)*1e3||0},P.endAnimation=function(){dr(P.callbackAnimation),n&&(tt?tt.progress(1):n.paused()?D||dr(n,P.direction<0,1):dr(n,n.reversed()))},P.labelToScroll=function(R){return n&&n.labels&&(rt||P.refresh()||rt)+n.labels[R]/n.duration()*Lt||0},P.getTrailing=function(R){var H=at.indexOf(P),I=P.direction>0?at.slice(0,H).reverse():at.slice(H+1);return(oi(R)?I.filter(function($){return $.vars.preventOverlaps===R}):I).filter(function($){return P.direction>0?$.end<=rt:$.start>=ot})},P.update=function(R,H,I){if(!(v&&!I&&!R)){var $=Ve===!0?Xe:P.scroll(),K=R?0:($-rt)/Lt,j=K<0?0:K>1?1:K||0,st=P.progress,yt,vt,ft,pt,kt,it,me,jt;if(H&&($t=It,It=v?Ot():$,y&&(ii=zi,zi=n&&!D?n.totalProgress():j)),_&&u&&!Te&&!Co&&Ci&&(!j&&rt<$+($-$t)/(Ce()-wr)*_?j=1e-4:j===1&&ot>$+($-$t)/(Ce()-wr)*_&&(j=.9999)),j!==st&&P.enabled){if(yt=P.isActive=!!j&&j<1,vt=!!st&&st<1,it=yt!==vt,kt=it||!!j!=!!st,P.direction=j>st?1:-1,P.progress=j,kt&&!Te&&(ft=j&&!st?0:j===1?1:st===1?2:3,D&&(pt=!it&&W[ft+1]!=="none"&&W[ft+1]||W[ft],jt=n&&(pt==="complete"||pt==="reset"||pt in n))),C&&(it||jt)&&(jt||f||!n)&&(Ae(C)?C(P):P.getTrailing(C).forEach(function(jn){return jn.endAnimation()})),D||(tt&&!Te&&!Co?(tt._dp._time-tt._start!==tt._time&&tt.render(tt._dp._time-tt._start),tt.resetTo?tt.resetTo("totalProgress",j,n._tTime/n._tDur):(tt.vars.totalProgress=j,tt.invalidate().restart())):n&&n.totalProgress(j,!!(Te&&(Pt||R)))),u){if(R&&p&&(qt.style[p+L.os2]=Xn),!N)Ie(Mr($e+ei*j));else if(kt){if(me=!R&&j>st&&ot+1>$&&$+1>=Ui(O,L),x)if(!R&&(yt||me)){var be=an(u,!0),xe=$-rt;Yh(u,St,be.top+(L===ce?xe:0)+ae,be.left+(L===ce?0:xe)+ae)}else Yh(u,qt);js(yt||me?Di:mi),bi&&j<1&&yt||Ie($e+(j===1&&!me?ei:0))}}y&&!xt.tween&&!Te&&!Co&&Se.restart(!0),a&&(it||w&&j&&(j<1||!sl))&&ao(a.targets).forEach(function(jn){return jn.classList[yt||w?"add":"remove"](a.className)}),o&&!D&&!R&&o(P),kt&&!Te?(D&&(jt&&(pt==="complete"?n.pause().totalProgress(1):pt==="reset"?n.restart(!0).pause():pt==="restart"?n.restart(!0):n[pt]()),o&&o(P)),(it||!sl)&&(c&&it&&ol(P,c),V[ft]&&ol(P,V[ft]),w&&(j===1?P.kill(!1,1):V[ft]=0),it||(ft=j===1?1:3,V[ft]&&ol(P,V[ft]))),M&&!yt&&Math.abs(P.getVelocity())>(kr(M)?M:2500)&&(dr(P.callbackAnimation),tt?tt.progress(1):dr(n,pt==="reverse"?1:!j,1))):D&&o&&!Te&&o(P)}if(Oi){var Me=v?$/v.duration()*(v._caScrollDist||0):$;bn(Me+(A._isFlipped?1:0)),Oi(Me)}U&&U(-$/v.duration()*(v._caScrollDist||0))}},P.enable=function(R,H){P.enabled||(P.enabled=!0,de(O,"resize",Tr),Y||de(O,"scroll",Os),q&&de(s,"refreshInit",q),R!==!1&&(P.progress=ut=0,It=$t=Q=Ot()),H!==!1&&P.refresh())},P.getTween=function(R){return R&&xt?xt.tween:tt},P.setPositions=function(R,H,I,$){if(v){var K=v.scrollTrigger,j=v.duration(),st=K.end-K.start;R=K.start+st*R/j,H=K.start+st*H/j}P.refresh(!1,!1,{start:Lh(R,I&&!!P._startClamp),end:Lh(H,I&&!!P._endClamp)},$),P.update()},P.adjustPinSpacing=function(R){if(Xt&&R){var H=Xt.indexOf(L.d)+1;Xt[H]=parseFloat(Xt[H])+R+ae,Xt[1]=parseFloat(Xt[1])+R+ae,js(Xt)}},P.disable=function(R,H){if(P.enabled&&(R!==!1&&P.revert(!0,!0),P.enabled=P.isActive=!1,H||tt&&tt.pause(),Xe=0,te&&(te.uncache=1),q&&fe(s,"refreshInit",q),Se&&(Se.pause(),xt.tween&&xt.tween.kill()&&(xt.tween=0)),!Y)){for(var I=at.length;I--;)if(at[I].scroller===O&&at[I]!==P)return;fe(O,"resize",Tr),Y||fe(O,"scroll",Os)}},P.kill=function(R,H){P.disable(R,H),tt&&!H&&tt.kill(),l&&delete Jl[l];var I=at.indexOf(P);I>=0&&at.splice(I,1),I===We&&ra>0&&We--,I=0,at.forEach(function($){return $.scroller===P.scroller&&(I=1)}),I||Ve||(P.scroll.rec=0),n&&(n.scrollTrigger=null,R&&n.revert({kill:!1}),H||n.kill()),et&&[et,we,A,Rt].forEach(function($){return $.parentNode&&$.parentNode.removeChild($)}),jr===P&&(jr=0),u&&(te&&(te.uncache=1),I=0,at.forEach(function($){return $.pin===u&&I++}),I||(te.spacer=0)),i.onKill&&i.onKill(P)},at.push(P),P.enable(!1,!1),S&&S(P),n&&n.add&&!Lt){var z=P.update;P.update=function(){P.update=z,ct.cache++,rt||ot||P.refresh()},X.delayedCall(.01,P.update),Lt=.01,rt=ot=0}else P.refresh();u&&E_()},s.register=function(i){return Is||(X=i||Wd(),Nd()&&window.document&&s.enable(),Is=Sr),Is},s.defaults=function(i){if(i)for(var n in i)Eo[n]=i[n];return Eo},s.disable=function(i,n){Sr=0,at.forEach(function(o){return o[n?"kill":"disable"](i)}),fe(lt,"wheel",Os),fe(At,"scroll",Os),clearInterval(Po),fe(At,"touchcancel",$i),fe(St,"touchstart",$i),Oo(fe,At,"pointerdown,touchstart,mousedown",Fh),Oo(fe,At,"pointerup,touchend,mouseup",Ih),va.kill(),Do(fe);for(var r=0;r<ct.length;r+=3)Ao(fe,ct[r],ct[r+1]),Ao(fe,ct[r],ct[r+2])},s.enable=function(){if(lt=window,At=document,li=At.documentElement,St=At.body,X&&(ao=X.utils.toArray,Vr=X.utils.clamp,Ql=X.core.context||$i,nl=X.core.suppressOverwrites||$i,Bc=lt.history.scrollRestoration||"auto",tc=lt.pageYOffset||0,X.core.globals("ScrollTrigger",s),St)){Sr=1,Xs=document.createElement("div"),Xs.style.height="100vh",Xs.style.position="absolute",qd(),k_(),Zt.register(X),s.isTouch=Zt.isTouch,wn=Zt.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),Kl=Zt.isTouch===1,de(lt,"wheel",Os),zc=[lt,At,li,St],X.matchMedia?(s.matchMedia=function(c){var h=X.matchMedia(),f;for(f in c)h.add(f,c[f]);return h},X.addEventListener("matchMediaInit",function(){return $c()}),X.addEventListener("matchMediaRevert",function(){return Ud()}),X.addEventListener("matchMedia",function(){as(0,1),ws("matchMedia")}),X.matchMedia().add("(orientation: portrait)",function(){return al(),al})):console.warn("Requires GSAP 3.11.0 or later"),al(),de(At,"scroll",Os);var i=St.hasAttribute("style"),n=St.style,r=n.borderTopStyle,o=X.core.Animation.prototype,a,l;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",a=an(St),ce.m=Math.round(a.top+ce.sc())||0,Ye.m=Math.round(a.left+Ye.sc())||0,r?n.borderTopStyle=r:n.removeProperty("border-top-style"),i||(St.setAttribute("style",""),St.removeAttribute("style")),Po=setInterval(Nh,250),X.delayedCall(.5,function(){return Co=0}),de(At,"touchcancel",$i),de(St,"touchstart",$i),Oo(de,At,"pointerdown,touchstart,mousedown",Fh),Oo(de,At,"pointerup,touchend,mouseup",Ih),ql=X.utils.checkPrefix("transform"),oa.push(ql),Is=Ce(),va=X.delayedCall(.2,as).pause(),zs=[At,"visibilitychange",function(){var c=lt.innerWidth,h=lt.innerHeight;At.hidden?(Ah=c,Eh=h):(Ah!==c||Eh!==h)&&Tr()},At,"DOMContentLoaded",as,lt,"load",as,lt,"resize",Tr],Do(de),at.forEach(function(c){return c.enable(0,1)}),l=0;l<ct.length;l+=3)Ao(fe,ct[l],ct[l+1]),Ao(fe,ct[l],ct[l+2])}},s.config=function(i){"limitCallbacks"in i&&(sl=!!i.limitCallbacks);var n=i.syncInterval;n&&clearInterval(Po)||(Po=n)&&setInterval(Nh,n),"ignoreMobileResize"in i&&(Kl=s.isTouch===1&&i.ignoreMobileResize),"autoRefreshEvents"in i&&(Do(fe)||Do(de,i.autoRefreshEvents||"none"),Id=(i.autoRefreshEvents+"").indexOf("resize")===-1)},s.scrollerProxy=function(i,n){var r=Ue(i),o=ct.indexOf(r),a=ys(r);~o&&ct.splice(o,a?6:2),n&&(a?qi.unshift(lt,n,St,n,li,n):qi.unshift(r,n))},s.clearMatchMedia=function(i){at.forEach(function(n){return n._ctx&&n._ctx.query===i&&n._ctx.kill(!0,!0)})},s.isInViewport=function(i,n,r){var o=(oi(i)?Ue(i):i).getBoundingClientRect(),a=o[r?fs:ds]*n||0;return r?o.right-a>0&&o.left+a<lt.innerWidth:o.bottom-a>0&&o.top+a<lt.innerHeight},s.positionInViewport=function(i,n,r){oi(i)&&(i=Ue(i));var o=i.getBoundingClientRect(),a=o[r?fs:ds],l=n==null?a/2:n in Ma?Ma[n]*a:~n.indexOf("%")?parseFloat(n)*a/100:parseFloat(n)||0;return r?(o.left+l)/lt.innerWidth:(o.top+l)/lt.innerHeight},s.killAll=function(i){if(at.slice(0).forEach(function(r){return r.vars.id!=="ScrollSmoother"&&r.kill()}),i!==!0){var n=vs.killAll||[];vs={},n.forEach(function(r){return r()})}},s}();gt.version="3.13.0";gt.saveStyles=function(s){return s?ao(s).forEach(function(t){if(t&&t.style){var e=ri.indexOf(t);e>=0&&ri.splice(e,5),ri.push(t,t.style.cssText,t.getBBox&&t.getAttribute("transform"),X.core.getCache(t),Ql())}}):ri};gt.revert=function(s,t){return $c(!s,t)};gt.create=function(s,t){return new gt(s,t)};gt.refresh=function(s){return s?Tr(!0):(Is||gt.register())&&as(!0)};gt.update=function(s){return++ct.cache&&gn(s===!0?2:0)};gt.clearScrollMemory=Gd;gt.maxScroll=function(s,t){return Ui(s,t?Ye:ce)};gt.getScrollFunc=function(s,t){return Nn(Ue(s),t?Ye:ce)};gt.getById=function(s){return Jl[s]};gt.getAll=function(){return at.filter(function(s){return s.vars.id!=="ScrollSmoother"})};gt.isScrolling=function(){return!!Ci};gt.snapDirectional=Yc;gt.addEventListener=function(s,t){var e=vs[s]||(vs[s]=[]);~e.indexOf(t)||e.push(t)};gt.removeEventListener=function(s,t){var e=vs[s],i=e&&e.indexOf(t);i>=0&&e.splice(i,1)};gt.batch=function(s,t){var e=[],i={},n=t.interval||.016,r=t.batchMax||1e9,o=function(c,h){var f=[],d=[],u=X.delayedCall(n,function(){h(f,d),f=[],d=[]}).pause();return function(p){f.length||u.restart(!0),f.push(p.trigger),d.push(p),r<=f.length&&u.progress(1)}},a;for(a in t)i[a]=a.substr(0,2)==="on"&&Ae(t[a])&&a!=="onRefreshInit"?o(a,t[a]):t[a];return Ae(r)&&(r=r(),de(gt,"refresh",function(){return r=t.batchMax()})),ao(s).forEach(function(l){var c={};for(a in i)c[a]=i[a];c.trigger=l,e.push(gt.create(c))}),e};var Xh=function(t,e,i,n){return e>n?t(n):e<0&&t(0),i>n?(n-e)/(i-e):i<0?e/(e-i):1},cl=function s(t,e){e===!0?t.style.removeProperty("touch-action"):t.style.touchAction=e===!0?"auto":e?"pan-"+e+(Zt.isTouch?" pinch-zoom":""):"none",t===li&&s(St,e)},Io={auto:1,scroll:1},z_=function(t){var e=t.event,i=t.target,n=t.axis,r=(e.changedTouches?e.changedTouches[0]:e).target,o=r._gsap||X.core.getCache(r),a=Ce(),l;if(!o._isScrollT||a-o._isScrollT>2e3){for(;r&&r!==St&&(r.scrollHeight<=r.clientHeight&&r.scrollWidth<=r.clientWidth||!(Io[(l=ki(r)).overflowY]||Io[l.overflowX]));)r=r.parentNode;o._isScroll=r&&r!==i&&!ys(r)&&(Io[(l=ki(r)).overflowY]||Io[l.overflowX]),o._isScrollT=a}(o._isScroll||n==="x")&&(e.stopPropagation(),e._gsapAllow=!0)},Qd=function(t,e,i,n){return Zt.create({target:t,capture:!0,debounce:!1,lockAxis:!0,type:e,onWheel:n=n&&z_,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return i&&de(At,Zt.eventTypes[0],Uh,!1,!0)},onDisable:function(){return fe(At,Zt.eventTypes[0],Uh,!0)}})},B_=/(input|label|select|textarea)/i,jh,Uh=function(t){var e=B_.test(t.target.tagName);(e||jh)&&(t._gsapAllow=!0,jh=e)},N_=function(t){is(t)||(t={}),t.preventDefault=t.isNormalizer=t.allowClicks=!0,t.type||(t.type="wheel,touch"),t.debounce=!!t.debounce,t.id=t.id||"normalizer";var e=t,i=e.normalizeScrollX,n=e.momentum,r=e.allowNestedScroll,o=e.onRelease,a,l,c=Ue(t.target)||li,h=X.core.globals().ScrollSmoother,f=h&&h.get(),d=wn&&(t.content&&Ue(t.content)||f&&t.content!==!1&&!f.smooth()&&f.content()),u=Nn(c,ce),p=Nn(c,Ye),g=1,_=(Zt.isTouch&&lt.visualViewport?lt.visualViewport.scale*lt.visualViewport.width:lt.outerWidth)/lt.innerWidth,m=0,b=Ae(n)?function(){return n(a)}:function(){return n||2.8},w,y,x=Qd(c,t.type,!0,r),k=function(){return y=!1},v=$i,M=$i,C=function(){l=Ui(c,ce),M=Vr(wn?1:0,l),i&&(v=Vr(0,Ui(c,Ye))),w=gs},L=function(){d._gsap.y=Mr(parseFloat(d._gsap.y)+u.offset)+"px",d.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(d._gsap.y)+", 0, 1)",u.offset=u.cacheID=0},D=function(){if(y){requestAnimationFrame(k);var G=Mr(a.deltaY/2),Z=M(u.v-G);if(d&&Z!==u.v+u.offset){u.offset=Z-u.v;var P=Mr((parseFloat(d&&d._gsap.y)||0)-u.offset);d.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+P+", 0, 1)",d._gsap.y=P+"px",u.cacheID=ct.cache,gn()}return!0}u.offset&&L(),y=!0},O,F,Y,N,V=function(){C(),O.isActive()&&O.vars.scrollY>l&&(u()>l?O.progress(1)&&u(l):O.resetTo("scrollY",l))};return d&&X.set(d,{y:"+=0"}),t.ignoreCheck=function(W){return wn&&W.type==="touchmove"&&D()||g>1.05&&W.type!=="touchstart"||a.isGesturing||W.touches&&W.touches.length>1},t.onPress=function(){y=!1;var W=g;g=Mr((lt.visualViewport&&lt.visualViewport.scale||1)/_),O.pause(),W!==g&&cl(c,g>1.01?!0:i?!1:"x"),F=p(),Y=u(),C(),w=gs},t.onRelease=t.onGestureStart=function(W,G){if(u.offset&&L(),!G)N.restart(!0);else{ct.cache++;var Z=b(),P,q;i&&(P=p(),q=P+Z*.05*-W.velocityX/.227,Z*=Xh(p,P,q,Ui(c,Ye)),O.vars.scrollX=v(q)),P=u(),q=P+Z*.05*-W.velocityY/.227,Z*=Xh(u,P,q,Ui(c,ce)),O.vars.scrollY=M(q),O.invalidate().duration(Z).play(.01),(wn&&O.vars.scrollY>=l||P>=l-1)&&X.to({},{onUpdate:V,duration:Z})}o&&o(W)},t.onWheel=function(){O._ts&&O.pause(),Ce()-m>1e3&&(w=0,m=Ce())},t.onChange=function(W,G,Z,P,q){if(gs!==w&&C(),G&&i&&p(v(P[2]===G?F+(W.startX-W.x):p()+G-P[1])),Z){u.offset&&L();var J=q[2]===Z,ht=J?Y+W.startY-W.y:u()+Z-q[1],Q=M(ht);J&&ht!==Q&&(Y+=Q-ht),u(Q)}(Z||G)&&gn()},t.onEnable=function(){cl(c,i?!1:"x"),gt.addEventListener("refresh",V),de(lt,"resize",V),u.smooth&&(u.target.style.scrollBehavior="auto",u.smooth=p.smooth=!1),x.enable()},t.onDisable=function(){cl(c,!0),fe(lt,"resize",V),gt.removeEventListener("refresh",V),x.kill()},t.lockAxis=t.lockAxis!==!1,a=new Zt(t),a.iOS=wn,wn&&!u()&&u(1),wn&&X.ticker.add($i),N=a._dc,O=X.to(a,{ease:"power4",paused:!0,inherit:!1,scrollX:i?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:Kd(u,u(),function(){return O.pause()})},onUpdate:gn,onComplete:N.vars.onComplete}),a};gt.sort=function(s){if(Ae(s))return at.sort(s);var t=lt.pageYOffset||0;return gt.getAll().forEach(function(e){return e._sortY=e.trigger?t+e.trigger.getBoundingClientRect().top:e.start+lt.innerHeight}),at.sort(s||function(e,i){return(e.vars.refreshPriority||0)*-1e6+(e.vars.containerAnimation?1e6:e._sortY)-((i.vars.containerAnimation?1e6:i._sortY)+(i.vars.refreshPriority||0)*-1e6)})};gt.observe=function(s){return new Zt(s)};gt.normalizeScroll=function(s){if(typeof s>"u")return Ne;if(s===!0&&Ne)return Ne.enable();if(s===!1){Ne&&Ne.kill(),Ne=s;return}var t=s instanceof Zt?s:N_(s);return Ne&&Ne.target===t.target&&Ne.kill(),ys(t.target)&&(Ne=t),t};gt.core={_getVelocityProp:Gl,_inputObserver:Qd,_scrollers:ct,_proxies:qi,bridge:{ss:function(){Ci||ws("scrollStart"),Ci=Ce()},ref:function(){return Te}}};Wd()&&X.registerPlugin(gt);/*!
 * matrix 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var cn,ps,Xc,Na,Pr,la,ka,Ur,Li="transform",ic=Li+"Origin",Zd,Jd=function(t){var e=t.ownerDocument||t;for(!(Li in t.style)&&("msTransform"in t.style)&&(Li="msTransform",ic=Li+"Origin");e.parentNode&&(e=e.parentNode););if(ps=window,ka=new Ss,e){cn=e,Xc=e.documentElement,Na=e.body,Ur=cn.createElementNS("http://www.w3.org/2000/svg","g"),Ur.style.transform="none";var i=e.createElement("div"),n=e.createElement("div"),r=e&&(e.body||e.firstElementChild);r&&r.appendChild&&(r.appendChild(i),i.appendChild(n),i.setAttribute("style","position:static;transform:translate3d(0,0,1px)"),Zd=n.offsetParent!==i,r.removeChild(i))}return e},W_=function(t){for(var e,i;t&&t!==Na;)i=t._gsap,i&&i.uncache&&i.get(t,"x"),i&&!i.scaleX&&!i.scaleY&&i.renderTransform&&(i.scaleX=i.scaleY=1e-4,i.renderTransform(1,i),e?e.push(i):e=[i]),t=t.parentNode;return e},tg=[],eg=[],V_=function(){return ps.pageYOffset||cn.scrollTop||Xc.scrollTop||Na.scrollTop||0},H_=function(){return ps.pageXOffset||cn.scrollLeft||Xc.scrollLeft||Na.scrollLeft||0},jc=function(t){return t.ownerSVGElement||((t.tagName+"").toLowerCase()==="svg"?t:null)},Y_=function s(t){if(ps.getComputedStyle(t).position==="fixed")return!0;if(t=t.parentNode,t&&t.nodeType===1)return s(t)},hl=function s(t,e){if(t.parentNode&&(cn||Jd(t))){var i=jc(t),n=i?i.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",r=i?e?"rect":"g":"div",o=e!==2?0:100,a=e===3?100:0,l="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",c=cn.createElementNS?cn.createElementNS(n.replace(/^https/,"http"),r):cn.createElement(r);return e&&(i?(la||(la=s(t)),c.setAttribute("width",.01),c.setAttribute("height",.01),c.setAttribute("transform","translate("+o+","+a+")"),la.appendChild(c)):(Pr||(Pr=s(t),Pr.style.cssText=l),c.style.cssText=l+"width:0.1px;height:0.1px;top:"+a+"px;left:"+o+"px",Pr.appendChild(c))),c}throw"Need document and parent."},$_=function(t){for(var e=new Ss,i=0;i<t.numberOfItems;i++)e.multiply(t.getItem(i).matrix);return e},X_=function(t){var e=t.getCTM(),i;return e||(i=t.style[Li],t.style[Li]="none",t.appendChild(Ur),e=Ur.getCTM(),t.removeChild(Ur),i?t.style[Li]=i:t.style.removeProperty(Li.replace(/([A-Z])/g,"-$1").toLowerCase())),e||ka.clone()},j_=function(t,e){var i=jc(t),n=t===i,r=i?tg:eg,o=t.parentNode,a=o&&!i&&o.shadowRoot&&o.shadowRoot.appendChild?o.shadowRoot:o,l,c,h,f,d,u;if(t===ps)return t;if(r.length||r.push(hl(t,1),hl(t,2),hl(t,3)),l=i?la:Pr,i)n?(h=X_(t),f=-h.e/h.a,d=-h.f/h.d,c=ka):t.getBBox?(h=t.getBBox(),c=t.transform?t.transform.baseVal:{},c=c.numberOfItems?c.numberOfItems>1?$_(c):c.getItem(0).matrix:ka,f=c.a*h.x+c.c*h.y,d=c.b*h.x+c.d*h.y):(c=new Ss,f=d=0),(n?i:o).appendChild(l),l.setAttribute("transform","matrix("+c.a+","+c.b+","+c.c+","+c.d+","+(c.e+f)+","+(c.f+d)+")");else{if(f=d=0,Zd)for(c=t.offsetParent,h=t;h&&(h=h.parentNode)&&h!==c&&h.parentNode;)(ps.getComputedStyle(h)[Li]+"").length>4&&(f=h.offsetLeft,d=h.offsetTop,h=0);if(u=ps.getComputedStyle(t),u.position!=="absolute"&&u.position!=="fixed")for(c=t.offsetParent;o&&o!==c;)f+=o.scrollLeft||0,d+=o.scrollTop||0,o=o.parentNode;h=l.style,h.top=t.offsetTop-d+"px",h.left=t.offsetLeft-f+"px",h[Li]=u[Li],h[ic]=u[ic],h.position=u.position==="fixed"?"fixed":"absolute",a.appendChild(l)}return l},ul=function(t,e,i,n,r,o,a){return t.a=e,t.b=i,t.c=n,t.d=r,t.e=o,t.f=a,t},Ss=function(){function s(e,i,n,r,o,a){e===void 0&&(e=1),i===void 0&&(i=0),n===void 0&&(n=0),r===void 0&&(r=1),o===void 0&&(o=0),a===void 0&&(a=0),ul(this,e,i,n,r,o,a)}var t=s.prototype;return t.inverse=function(){var i=this.a,n=this.b,r=this.c,o=this.d,a=this.e,l=this.f,c=i*o-n*r||1e-10;return ul(this,o/c,-n/c,-r/c,i/c,(r*l-o*a)/c,-(i*l-n*a)/c)},t.multiply=function(i){var n=this.a,r=this.b,o=this.c,a=this.d,l=this.e,c=this.f,h=i.a,f=i.c,d=i.b,u=i.d,p=i.e,g=i.f;return ul(this,h*n+d*o,h*r+d*a,f*n+u*o,f*r+u*a,l+p*n+g*o,c+p*r+g*a)},t.clone=function(){return new s(this.a,this.b,this.c,this.d,this.e,this.f)},t.equals=function(i){var n=this.a,r=this.b,o=this.c,a=this.d,l=this.e,c=this.f;return n===i.a&&r===i.b&&o===i.c&&a===i.d&&l===i.e&&c===i.f},t.apply=function(i,n){n===void 0&&(n={});var r=i.x,o=i.y,a=this.a,l=this.b,c=this.c,h=this.d,f=this.e,d=this.f;return n.x=r*a+o*c+f||0,n.y=r*l+o*h+d||0,n},s}();function rs(s,t,e,i){if(!s||!s.parentNode||(cn||Jd(s)).documentElement===s)return new Ss;var n=W_(s),r=jc(s),o=r?tg:eg,a=j_(s),l=o[0].getBoundingClientRect(),c=o[1].getBoundingClientRect(),h=o[2].getBoundingClientRect(),f=a.parentNode,d=Y_(s),u=new Ss((c.left-l.left)/100,(c.top-l.top)/100,(h.left-l.left)/100,(h.top-l.top)/100,l.left+(d?0:H_()),l.top+(d?0:V_()));if(f.removeChild(a),n)for(l=n.length;l--;)c=n[l],c.scaleX=c.scaleY=0,c.renderTransform(1,c);return t?u.inverse():u}function Gh(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function U_(s,t){s.prototype=Object.create(t.prototype),s.prototype.constructor=s,s.__proto__=t}var mt,Dt,ui,Ii,hn,fl,rn,nc,Cr,Dn,ig,sc,co,Uc,Dr,Ei,Or,ca,ng,rc,Ta=0,sg=function(){return typeof window<"u"},rg=function(){return mt||sg()&&(mt=window.gsap)&&mt.registerPlugin&&mt},Sn=function(t){return typeof t=="function"},Gr=function(t){return typeof t=="object"},Ri=function(t){return typeof t>"u"},ha=function(){return!1},qr="transform",oc="transformOrigin",yn=function(t){return Math.round(t*1e4)/1e4},gr=Array.isArray,zo=function(t,e){var i=ui.createElementNS?ui.createElementNS("http://www.w3.org/1999/xhtml".replace(/^https/,"http"),t):ui.createElement(t);return i.style?i:ui.createElement(t)},qh=180/Math.PI,As=1e20,G_=new Ss,vn=Date.now||function(){return new Date().getTime()},_s=[],Us={},q_=0,K_=/^(?:a|input|textarea|button|select)$/i,Kh=0,Es={},Ji={},og=function(t,e){var i={},n;for(n in t)i[n]=e?t[n]*e:t[n];return i},Q_=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},Qh=function s(t,e){for(var i=t.length,n;i--;)e?t[i].style.touchAction=e:t[i].style.removeProperty("touch-action"),n=t[i].children,n&&n.length&&s(n,e)},ag=function(){return _s.forEach(function(t){return t()})},Z_=function(t){_s.push(t),_s.length===1&&mt.ticker.add(ag)},Zh=function(){return!_s.length&&mt.ticker.remove(ag)},Jh=function(t){for(var e=_s.length;e--;)_s[e]===t&&_s.splice(e,1);mt.to(Zh,{overwrite:!0,delay:15,duration:0,onComplete:Zh,data:"_draggable"})},J_=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},le=function(t,e,i,n){if(t.addEventListener){var r=co[e];n=n||(ig?{passive:!1}:null),t.addEventListener(r||e,i,n),r&&e!==r&&t.addEventListener(e,i,n)}},ee=function(t,e,i,n){if(t.removeEventListener){var r=co[e];t.removeEventListener(r||e,i,n),r&&e!==r&&t.removeEventListener(e,i,n)}},yi=function(t){t.preventDefault&&t.preventDefault(),t.preventManipulation&&t.preventManipulation()},tm=function(t,e){for(var i=t.length;i--;)if(t[i].identifier===e)return!0},em=function s(t){Uc=t.touches&&Ta<t.touches.length,ee(t.target,"touchend",s)},tu=function(t){Uc=t.touches&&Ta<t.touches.length,le(t.target,"touchend",em)},Gs=function(t){return Dt.pageYOffset||t.scrollTop||t.documentElement.scrollTop||t.body.scrollTop||0},qs=function(t){return Dt.pageXOffset||t.scrollLeft||t.documentElement.scrollLeft||t.body.scrollLeft||0},eu=function s(t,e){le(t,"scroll",e),sr(t.parentNode)||s(t.parentNode,e)},iu=function s(t,e){ee(t,"scroll",e),sr(t.parentNode)||s(t.parentNode,e)},sr=function(t){return!t||t===Ii||t.nodeType===9||t===ui.body||t===Dt||!t.nodeType||!t.parentNode},nu=function(t,e){var i=e==="x"?"Width":"Height",n="scroll"+i,r="client"+i;return Math.max(0,sr(t)?Math.max(Ii[n],hn[n])-(Dt["inner"+i]||Ii[r]||hn[r]):t[n]-t[r])},dl=function s(t,e){var i=nu(t,"x"),n=nu(t,"y");sr(t)?t=Ji:s(t.parentNode,e),t._gsMaxScrollX=i,t._gsMaxScrollY=n,e||(t._gsScrollX=t.scrollLeft||0,t._gsScrollY=t.scrollTop||0)},gl=function(t,e,i){var n=t.style;n&&(Ri(n[e])&&(e=Cr(e,t)||e),i==null?n.removeProperty&&n.removeProperty(e.replace(/([A-Z])/g,"-$1").toLowerCase()):n[e]=i)},ho=function(t){return Dt.getComputedStyle(t instanceof Element?t:t.host||(t.parentNode||{}).host||t)},qn={},Rs=function(t){if(t===Dt)return qn.left=qn.top=0,qn.width=qn.right=Ii.clientWidth||t.innerWidth||hn.clientWidth||0,qn.height=qn.bottom=(t.innerHeight||0)-20<Ii.clientHeight?Ii.clientHeight:t.innerHeight||hn.clientHeight||0,qn;var e=t.ownerDocument||ui,i=Ri(t.pageX)?!t.nodeType&&!Ri(t.left)&&!Ri(t.top)?t:Dn(t)[0].getBoundingClientRect():{left:t.pageX-qs(e),top:t.pageY-Gs(e),right:t.pageX-qs(e)+1,bottom:t.pageY-Gs(e)+1};return Ri(i.right)&&!Ri(i.width)?(i.right=i.left+i.width,i.bottom=i.top+i.height):Ri(i.width)&&(i={width:i.right-i.left,height:i.bottom-i.top,right:i.right,left:i.left,bottom:i.bottom,top:i.top}),i},Kt=function(t,e,i){var n=t.vars,r=n[i],o=t._listeners[e],a;return Sn(r)&&(a=r.apply(n.callbackScope||t,n[i+"Params"]||[t.pointerEvent])),o&&t.dispatchEvent(e)===!1&&(a=!1),a},su=function(t,e){var i=Dn(t)[0],n,r,o;return!i.nodeType&&i!==Dt?Ri(t.left)?(r=t.min||t.minX||t.minRotation||0,n=t.min||t.minY||0,{left:r,top:n,width:(t.max||t.maxX||t.maxRotation||0)-r,height:(t.max||t.maxY||0)-n}):(o={x:0,y:0},{left:t.left-o.x,top:t.top-o.y,width:t.width,height:t.height}):im(i,e)},vi={},im=function(t,e){e=Dn(e)[0];var i=t.getBBox&&t.ownerSVGElement,n=t.ownerDocument||ui,r,o,a,l,c,h,f,d,u,p,g,_,m;if(t===Dt)a=Gs(n),r=qs(n),o=r+(n.documentElement.clientWidth||t.innerWidth||n.body.clientWidth||0),l=a+((t.innerHeight||0)-20<n.documentElement.clientHeight?n.documentElement.clientHeight:t.innerHeight||n.body.clientHeight||0);else{if(e===Dt||Ri(e))return t.getBoundingClientRect();r=a=0,i?(p=t.getBBox(),g=p.width,_=p.height):(t.viewBox&&(p=t.viewBox.baseVal)&&(r=p.x||0,a=p.y||0,g=p.width,_=p.height),g||(m=ho(t),p=m.boxSizing==="border-box",g=(parseFloat(m.width)||t.clientWidth||0)+(p?0:parseFloat(m.borderLeftWidth)+parseFloat(m.borderRightWidth)),_=(parseFloat(m.height)||t.clientHeight||0)+(p?0:parseFloat(m.borderTopWidth)+parseFloat(m.borderBottomWidth)))),o=g,l=_}return t===e?{left:r,top:a,width:o-r,height:l-a}:(c=rs(e,!0).multiply(rs(t)),h=c.apply({x:r,y:a}),f=c.apply({x:o,y:a}),d=c.apply({x:o,y:l}),u=c.apply({x:r,y:l}),r=Math.min(h.x,f.x,d.x,u.x),a=Math.min(h.y,f.y,d.y,u.y),{left:r,top:a,width:Math.max(h.x,f.x,d.x,u.x)-r,height:Math.max(h.y,f.y,d.y,u.y)-a})},pl=function(t,e,i,n,r,o){var a={},l,c,h;if(e)if(r!==1&&e instanceof Array){if(a.end=l=[],h=e.length,Gr(e[0]))for(c=0;c<h;c++)l[c]=og(e[c],r);else for(c=0;c<h;c++)l[c]=e[c]*r;i+=1.1,n-=1.1}else Sn(e)?a.end=function(f){var d=e.call(t,f),u,p;if(r!==1)if(Gr(d)){u={};for(p in d)u[p]=d[p]*r;d=u}else d*=r;return d}:a.end=e;return(i||i===0)&&(a.max=i),(n||n===0)&&(a.min=n),o&&(a.velocity=0),a},nm=function s(t){var e;return!t||!t.getAttribute||t===hn?!1:(e=t.getAttribute("data-clickable"))==="true"||e!=="false"&&(K_.test(t.nodeName+"")||t.getAttribute("contentEditable")==="true")?!0:s(t.parentNode)},Bo=function(t,e){for(var i=t.length,n;i--;)n=t[i],n.ondragstart=n.onselectstart=e?null:ha,mt.set(n,{lazy:!0,userSelect:e?"text":"none"})},sm=function s(t){if(ho(t).position==="fixed")return!0;if(t=t.parentNode,t&&t.nodeType===1)return s(t)},lg,ac,rm=function(t,e){t=mt.utils.toArray(t)[0],e=e||{};var i=document.createElement("div"),n=i.style,r=t.firstChild,o=0,a=0,l=t.scrollTop,c=t.scrollLeft,h=t.scrollWidth,f=t.scrollHeight,d=0,u=0,p=0,g,_,m,b,w,y;lg&&e.force3D!==!1?(w="translate3d(",y="px,0px)"):qr&&(w="translate(",y="px)"),this.scrollTop=function(x,k){if(!arguments.length)return-this.top();this.top(-x,k)},this.scrollLeft=function(x,k){if(!arguments.length)return-this.left();this.left(-x,k)},this.left=function(x,k){if(!arguments.length)return-(t.scrollLeft+a);var v=t.scrollLeft-c,M=a;if((v>2||v<-2)&&!k){c=t.scrollLeft,mt.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-c),e.onKill&&e.onKill();return}x=-x,x<0?(a=x-.5|0,x=0):x>u?(a=x-u|0,x=u):a=0,(a||M)&&(this._skip||(n[qr]=w+-a+"px,"+-o+y),a+d>=0&&(n.paddingRight=a+d+"px")),t.scrollLeft=x|0,c=t.scrollLeft},this.top=function(x,k){if(!arguments.length)return-(t.scrollTop+o);var v=t.scrollTop-l,M=o;if((v>2||v<-2)&&!k){l=t.scrollTop,mt.killTweensOf(this,{top:1,scrollTop:1}),this.top(-l),e.onKill&&e.onKill();return}x=-x,x<0?(o=x-.5|0,x=0):x>p?(o=x-p|0,x=p):o=0,(o||M)&&(this._skip||(n[qr]=w+-a+"px,"+-o+y)),t.scrollTop=x|0,l=t.scrollTop},this.maxScrollTop=function(){return p},this.maxScrollLeft=function(){return u},this.disable=function(){for(r=i.firstChild;r;)b=r.nextSibling,t.appendChild(r),r=b;t===i.parentNode&&t.removeChild(i)},this.enable=function(){if(r=t.firstChild,r!==i){for(;r;)b=r.nextSibling,i.appendChild(r),r=b;t.appendChild(i),this.calibrate()}},this.calibrate=function(x){var k=t.clientWidth===g,v,M,C;l=t.scrollTop,c=t.scrollLeft,!(k&&t.clientHeight===_&&i.offsetHeight===m&&h===t.scrollWidth&&f===t.scrollHeight&&!x)&&((o||a)&&(M=this.left(),C=this.top(),this.left(-t.scrollLeft),this.top(-t.scrollTop)),v=ho(t),(!k||x)&&(n.display="block",n.width="auto",n.paddingRight="0px",d=Math.max(0,t.scrollWidth-t.clientWidth),d&&(d+=parseFloat(v.paddingLeft)+(ac?parseFloat(v.paddingRight):0))),n.display="inline-block",n.position="relative",n.overflow="visible",n.verticalAlign="top",n.boxSizing="content-box",n.width="100%",n.paddingRight=d+"px",ac&&(n.paddingBottom=v.paddingBottom),g=t.clientWidth,_=t.clientHeight,h=t.scrollWidth,f=t.scrollHeight,u=t.scrollWidth-g,p=t.scrollHeight-_,m=i.offsetHeight,n.display="block",(M||C)&&(this.left(M),this.top(C)))},this.content=i,this.element=t,this._skip=!1,this.enable()},_l=function(t){if(sg()&&document.body){var e=window&&window.navigator;Dt=window,ui=document,Ii=ui.documentElement,hn=ui.body,fl=zo("div"),ca=!!window.PointerEvent,rn=zo("div"),rn.style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",Or=rn.style.cursor==="grab"?"grab":"move",Dr=e&&e.userAgent.toLowerCase().indexOf("android")!==-1,sc="ontouchstart"in Ii&&"orientation"in Dt||e&&(e.MaxTouchPoints>0||e.msMaxTouchPoints>0),ac=function(){var i=zo("div"),n=zo("div"),r=n.style,o=hn,a;return r.display="inline-block",r.position="relative",i.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",i.appendChild(n),o.appendChild(i),a=n.offsetHeight+18>i.scrollHeight,o.removeChild(i),a}(),co=function(i){for(var n=i.split(","),r=("onpointerdown"in fl?"pointerdown,pointermove,pointerup,pointercancel":"onmspointerdown"in fl?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":i).split(","),o={},a=4;--a>-1;)o[n[a]]=r[a],o[r[a]]=n[a];try{Ii.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){ig=1}}))}catch{}return o}("touchstart,touchmove,touchend,touchcancel"),le(ui,"touchcancel",ha),le(Dt,"touchmove",ha),hn&&hn.addEventListener("touchstart",ha),le(ui,"contextmenu",function(){for(var i in Us)Us[i].isPressed&&Us[i].endDrag()}),mt=nc=rg()}mt?(Ei=mt.plugins.inertia,ng=mt.core.context||function(){},Cr=mt.utils.checkPrefix,qr=Cr(qr),oc=Cr(oc),Dn=mt.utils.toArray,rc=mt.core.getStyleSaver,lg=!!Cr("perspective")):t&&console.warn("Please gsap.registerPlugin(Draggable)")},om=function(){function s(e){this._listeners={},this.target=e||this}var t=s.prototype;return t.addEventListener=function(i,n){var r=this._listeners[i]||(this._listeners[i]=[]);~r.indexOf(n)||r.push(n)},t.removeEventListener=function(i,n){var r=this._listeners[i],o=r&&r.indexOf(n);o>=0&&r.splice(o,1)},t.dispatchEvent=function(i){var n=this,r;return(this._listeners[i]||[]).forEach(function(o){return o.call(n,{type:i,target:n.target})===!1&&(r=!1)}),r},s}(),lr=function(s){U_(t,s);function t(e,i){var n;n=s.call(this)||this,nc||_l(1),e=Dn(e)[0],n.styles=rc&&rc(e,"transform,left,top"),Ei||(Ei=mt.plugins.inertia),n.vars=i=og(i||{}),n.target=e,n.x=n.y=n.rotation=0,n.dragResistance=parseFloat(i.dragResistance)||0,n.edgeResistance=isNaN(i.edgeResistance)?1:parseFloat(i.edgeResistance)||0,n.lockAxis=i.lockAxis,n.autoScroll=i.autoScroll||0,n.lockedAxis=null,n.allowEventDefault=!!i.allowEventDefault,mt.getProperty(e,"x");var r=(i.type||"x,y").toLowerCase(),o=~r.indexOf("x")||~r.indexOf("y"),a=r.indexOf("rotation")!==-1,l=a?"rotation":o?"x":"left",c=o?"y":"top",h=!!(~r.indexOf("x")||~r.indexOf("left")||r==="scroll"),f=!!(~r.indexOf("y")||~r.indexOf("top")||r==="scroll"),d=i.minimumMovement||2,u=Gh(n),p=Dn(i.trigger||i.handle||e),g={},_=0,m=!1,b=i.autoScrollMarginTop||40,w=i.autoScrollMarginRight||40,y=i.autoScrollMarginBottom||40,x=i.autoScrollMarginLeft||40,k=i.clickableTest||nm,v=0,M=e._gsap||mt.core.getCache(e),C=sm(e),L=function(S,B){return parseFloat(M.get(e,S,B))},D=e.ownerDocument||ui,O,F,Y,N,V,W,G,Z,P,q,J,ht,Q,Pt,ut,Ot,xt,te,_e,It,$t,rt,ot,et,we,A,Rt,ti,_i,Lt,Wt,Di,mi,qt=function(S){return yi(S),S.stopImmediatePropagation&&S.stopImmediatePropagation(),!1},re=function U(S){if(u.autoScroll&&u.isDragging&&(m||xt)){var B=e,T=u.autoScroll*15,E,z,R,H,I,$,K,j;for(m=!1,Ji.scrollTop=Dt.pageYOffset!=null?Dt.pageYOffset:D.documentElement.scrollTop!=null?D.documentElement.scrollTop:D.body.scrollTop,Ji.scrollLeft=Dt.pageXOffset!=null?Dt.pageXOffset:D.documentElement.scrollLeft!=null?D.documentElement.scrollLeft:D.body.scrollLeft,H=u.pointerX-Ji.scrollLeft,I=u.pointerY-Ji.scrollTop;B&&!z;)z=sr(B.parentNode),E=z?Ji:B.parentNode,R=z?{bottom:Math.max(Ii.clientHeight,Dt.innerHeight||0),right:Math.max(Ii.clientWidth,Dt.innerWidth||0),left:0,top:0}:E.getBoundingClientRect(),$=K=0,f&&(j=E._gsMaxScrollY-E.scrollTop,j<0?K=j:I>R.bottom-y&&j?(m=!0,K=Math.min(j,T*(1-Math.max(0,R.bottom-I)/y)|0)):I<R.top+b&&E.scrollTop&&(m=!0,K=-Math.min(E.scrollTop,T*(1-Math.max(0,I-R.top)/b)|0)),K&&(E.scrollTop+=K)),h&&(j=E._gsMaxScrollX-E.scrollLeft,j<0?$=j:H>R.right-w&&j?(m=!0,$=Math.min(j,T*(1-Math.max(0,R.right-H)/w)|0)):H<R.left+x&&E.scrollLeft&&(m=!0,$=-Math.min(E.scrollLeft,T*(1-Math.max(0,H-R.left)/x)|0)),$&&(E.scrollLeft+=$)),z&&($||K)&&(Dt.scrollTo(E.scrollLeft,E.scrollTop),ni(u.pointerX+$,u.pointerY+K)),B=E}if(xt){var st=u.x,yt=u.y;a?(u.deltaX=st-parseFloat(M.rotation),u.rotation=st,M.rotation=st+"deg",M.renderTransform(1,M)):F?(f&&(u.deltaY=yt-F.top(),F.top(yt)),h&&(u.deltaX=st-F.left(),F.left(st))):o?(f&&(u.deltaY=yt-parseFloat(M.y),M.y=yt+"px"),h&&(u.deltaX=st-parseFloat(M.x),M.x=st+"px"),M.renderTransform(1,M)):(f&&(u.deltaY=yt-parseFloat(e.style.top||0),e.style.top=yt+"px"),h&&(u.deltaX=st-parseFloat(e.style.left||0),e.style.left=st+"px")),Z&&!S&&!ti&&(ti=!0,Kt(u,"drag","onDrag")===!1&&(h&&(u.x-=u.deltaX),f&&(u.y-=u.deltaY),U(!0)),ti=!1)}xt=!1},Mt=function(S,B){var T=u.x,E=u.y,z,R;e._gsap||(M=mt.core.getCache(e)),M.uncache&&mt.getProperty(e,"x"),o?(u.x=parseFloat(M.x),u.y=parseFloat(M.y)):a?u.x=u.rotation=parseFloat(M.rotation):F?(u.y=F.top(),u.x=F.left()):(u.y=parseFloat(e.style.top||(R=ho(e))&&R.top)||0,u.x=parseFloat(e.style.left||(R||{}).left)||0),(_e||It||$t)&&!B&&(u.isDragging||u.isThrowing)&&($t&&(Es.x=u.x,Es.y=u.y,z=$t(Es),z.x!==u.x&&(u.x=z.x,xt=!0),z.y!==u.y&&(u.y=z.y,xt=!0)),_e&&(z=_e(u.x),z!==u.x&&(u.x=z,a&&(u.rotation=z),xt=!0)),It&&(z=It(u.y),z!==u.y&&(u.y=z),xt=!0)),xt&&re(!0),S||(u.deltaX=u.x-T,u.deltaY=u.y-E,Kt(u,"throwupdate","onThrowUpdate"))},Ie=function(S,B,T,E){return B==null&&(B=-1e20),T==null&&(T=As),Sn(S)?function(z){var R=u.isPressed?1-u.edgeResistance:1;return S.call(u,(z>T?T+(z-T)*R:z<B?B+(z-B)*R:z)*E)*E}:gr(S)?function(z){for(var R=S.length,H=0,I=As,$,K;--R>-1;)$=S[R],K=$-z,K<0&&(K=-K),K<I&&$>=B&&$<=T&&(H=R,I=K);return S[H]}:isNaN(S)?function(z){return z}:function(){return S*E}},$e=function(S,B,T,E,z,R,H){return R=R&&R<As?R*R:As,Sn(S)?function(I){var $=u.isPressed?1-u.edgeResistance:1,K=I.x,j=I.y,st,yt,vt;return I.x=K=K>T?T+(K-T)*$:K<B?B+(K-B)*$:K,I.y=j=j>z?z+(j-z)*$:j<E?E+(j-E)*$:j,st=S.call(u,I),st!==I&&(I.x=st.x,I.y=st.y),H!==1&&(I.x*=H,I.y*=H),R<As&&(yt=I.x-K,vt=I.y-j,yt*yt+vt*vt>R&&(I.x=K,I.y=j)),I}:gr(S)?function(I){for(var $=S.length,K=0,j=As,st,yt,vt,ft;--$>-1;)vt=S[$],st=vt.x-I.x,yt=vt.y-I.y,ft=st*st+yt*yt,ft<j&&(K=$,j=ft);return j<=R?S[K]:I}:function(I){return I}},ei=function(){var S,B,T,E;G=!1,F?(F.calibrate(),u.minX=J=-F.maxScrollLeft(),u.minY=Q=-F.maxScrollTop(),u.maxX=q=u.maxY=ht=0,G=!0):i.bounds&&(S=su(i.bounds,e.parentNode),a?(u.minX=J=S.left,u.maxX=q=S.left+S.width,u.minY=Q=u.maxY=ht=0):!Ri(i.bounds.maxX)||!Ri(i.bounds.maxY)?(S=i.bounds,u.minX=J=S.minX,u.minY=Q=S.minY,u.maxX=q=S.maxX,u.maxY=ht=S.maxY):(B=su(e,e.parentNode),u.minX=J=Math.round(L(l,"px")+S.left-B.left),u.minY=Q=Math.round(L(c,"px")+S.top-B.top),u.maxX=q=Math.round(J+(S.width-B.width)),u.maxY=ht=Math.round(Q+(S.height-B.height))),J>q&&(u.minX=q,u.maxX=q=J,J=u.minX),Q>ht&&(u.minY=ht,u.maxY=ht=Q,Q=u.minY),a&&(u.minRotation=J,u.maxRotation=q),G=!0),i.liveSnap&&(T=i.liveSnap===!0?i.snap||{}:i.liveSnap,E=gr(T)||Sn(T),a?(_e=Ie(E?T:T.rotation,J,q,1),It=null):T.points?$t=$e(E?T:T.points,J,q,Q,ht,T.radius,F?-1:1):(h&&(_e=Ie(E?T:T.x||T.left||T.scrollLeft,J,q,F?-1:1)),f&&(It=Ie(E?T:T.y||T.top||T.scrollTop,Q,ht,F?-1:1))))},Xn=function(){u.isThrowing=!1,Kt(u,"throwcomplete","onThrowComplete")},Xt=function(){u.isThrowing=!1},bn=function(S,B){var T,E,z,R;S&&Ei?(S===!0&&(T=i.snap||i.liveSnap||{},E=gr(T)||Sn(T),S={resistance:(i.throwResistance||i.resistance||1e3)/(a?10:1)},a?S.rotation=pl(u,E?T:T.rotation,q,J,1,B):(h&&(S[l]=pl(u,E?T:T.points||T.x||T.left,q,J,F?-1:1,B||u.lockedAxis==="x")),f&&(S[c]=pl(u,E?T:T.points||T.y||T.top,ht,Q,F?-1:1,B||u.lockedAxis==="y")),(T.points||gr(T)&&Gr(T[0]))&&(S.linkedProps=l+","+c,S.radius=T.radius))),u.isThrowing=!0,R=isNaN(i.overshootTolerance)?i.edgeResistance===1?0:1-u.edgeResistance+.2:i.overshootTolerance,S.duration||(S.duration={max:Math.max(i.minDuration||0,"maxDuration"in i?i.maxDuration:2),min:isNaN(i.minDuration)?R===0||Gr(S)&&S.resistance>1e3?0:.5:i.minDuration,overshoot:R}),u.tween=z=mt.to(F||e,{inertia:S,data:"_draggable",inherit:!1,onComplete:Xn,onInterrupt:Xt,onUpdate:i.fastMode?Kt:Mt,onUpdateParams:i.fastMode?[u,"onthrowupdate","onThrowUpdate"]:T&&T.radius?[!1,!0]:[]}),i.fastMode||(F&&(F._skip=!0),z.render(1e9,!0,!0),Mt(!0,!0),u.endX=u.x,u.endY=u.y,a&&(u.endRotation=u.x),z.play(0),Mt(!0,!0),F&&(F._skip=!1))):G&&u.applyBounds()},bi=function(S){var B=et,T;et=rs(e.parentNode,!0),S&&u.isPressed&&!et.equals(B||new Ss)&&(T=B.inverse().apply({x:Y,y:N}),et.apply(T,T),Y=T.x,N=T.y),et.equals(G_)&&(et=null)},Oi=function(){var S=1-u.edgeResistance,B=C?qs(D):0,T=C?Gs(D):0,E,z,R;o&&(M.x=L(l,"px")+"px",M.y=L(c,"px")+"px",M.renderTransform()),bi(!1),vi.x=u.pointerX-B,vi.y=u.pointerY-T,et&&et.apply(vi,vi),Y=vi.x,N=vi.y,xt&&(ni(u.pointerX,u.pointerY),re(!0)),Di=rs(e),F?(ei(),W=F.top(),V=F.left()):(xi()?(Mt(!0,!0),ei()):u.applyBounds(),a?(E=e.ownerSVGElement?[M.xOrigin-e.getBBox().x,M.yOrigin-e.getBBox().y]:(ho(e)[oc]||"0 0").split(" "),Ot=u.rotationOrigin=rs(e).apply({x:parseFloat(E[0])||0,y:parseFloat(E[1])||0}),Mt(!0,!0),z=u.pointerX-Ot.x-B,R=Ot.y-u.pointerY+T,V=u.x,W=u.y=Math.atan2(R,z)*qh):(W=L(c,"px"),V=L(l,"px"))),G&&S&&(V>q?V=q+(V-q)/S:V<J&&(V=J-(J-V)/S),a||(W>ht?W=ht+(W-ht)/S:W<Q&&(W=Q-(Q-W)/S))),u.startX=V=yn(V),u.startY=W=yn(W)},xi=function(){return u.tween&&u.tween.isActive()},zi=function(){rn.parentNode&&!xi()&&!u.isDragging&&rn.parentNode.removeChild(rn)},ii=function(S,B){var T;if(!O||u.isPressed||!S||(S.type==="mousedown"||S.type==="pointerdown")&&!B&&vn()-v<30&&co[u.pointerEvent.type]){Wt&&S&&O&&yi(S);return}if(we=xi(),mi=!1,u.pointerEvent=S,co[S.type]?(ot=~S.type.indexOf("touch")?S.currentTarget||S.target:D,le(ot,"touchend",Vt),le(ot,"touchmove",tt),le(ot,"touchcancel",Vt),le(D,"touchstart",tu)):(ot=null,le(D,"mousemove",tt)),Rt=null,(!ca||!ot)&&(le(D,"mouseup",Vt),S&&S.target&&le(S.target,"mouseup",Vt)),rt=k.call(u,S.target)&&i.dragClickables===!1&&!B,rt){le(S.target,"change",Vt),Kt(u,"pressInit","onPressInit"),Kt(u,"press","onPress"),Bo(p,!0),Wt=!1;return}if(A=!ot||h===f||u.vars.allowNativeTouchScrolling===!1||u.vars.allowContextMenu&&S&&(S.ctrlKey||S.which>2)?!1:h?"y":"x",Wt=!A&&!u.allowEventDefault,Wt&&(yi(S),le(Dt,"touchforcechange",yi)),S.changedTouches?(S=Pt=S.changedTouches[0],ut=S.identifier):S.pointerId?ut=S.pointerId:Pt=ut=null,Ta++,Z_(re),N=u.pointerY=S.pageY,Y=u.pointerX=S.pageX,Kt(u,"pressInit","onPressInit"),(A||u.autoScroll)&&dl(e.parentNode),e.parentNode&&u.autoScroll&&!F&&!a&&e.parentNode._gsMaxScrollX&&!rn.parentNode&&!e.getBBox&&(rn.style.width=e.parentNode.scrollWidth+"px",e.parentNode.appendChild(rn)),Oi(),u.tween&&u.tween.kill(),u.isThrowing=!1,mt.killTweensOf(F||e,g,!0),F&&mt.killTweensOf(e,{scrollTo:1},!0),u.tween=u.lockedAxis=null,(i.zIndexBoost||!a&&!F&&i.zIndexBoost!==!1)&&(e.style.zIndex=t.zIndex++),u.isPressed=!0,Z=!!(i.onDrag||u._listeners.drag),P=!!(i.onMove||u._listeners.move),i.cursor!==!1||i.activeCursor)for(T=p.length;--T>-1;)mt.set(p[T],{cursor:i.activeCursor||i.cursor||(Or==="grab"?"grabbing":Or)});Kt(u,"press","onPress")},tt=function(S){var B=S,T,E,z,R,H,I;if(!O||Uc||!u.isPressed||!S){Wt&&S&&O&&yi(S);return}if(u.pointerEvent=S,T=S.changedTouches,T){if(S=T[0],S!==Pt&&S.identifier!==ut){for(R=T.length;--R>-1&&(S=T[R]).identifier!==ut&&S.target!==e;);if(R<0)return}}else if(S.pointerId&&ut&&S.pointerId!==ut)return;if(ot&&A&&!Rt&&(vi.x=S.pageX-(C?qs(D):0),vi.y=S.pageY-(C?Gs(D):0),et&&et.apply(vi,vi),E=vi.x,z=vi.y,H=Math.abs(E-Y),I=Math.abs(z-N),(H!==I&&(H>d||I>d)||Dr&&A===Rt)&&(Rt=H>I&&h?"x":"y",A&&Rt!==A&&le(Dt,"touchforcechange",yi),u.vars.lockAxisOnTouchScroll!==!1&&h&&f&&(u.lockedAxis=Rt==="x"?"y":"x",Sn(u.vars.onLockAxis)&&u.vars.onLockAxis.call(u,B)),Dr&&A===Rt))){Vt(B);return}!u.allowEventDefault&&(!A||Rt&&A!==Rt)&&B.cancelable!==!1?(yi(B),Wt=!0):Wt&&(Wt=!1),u.autoScroll&&(m=!0),ni(S.pageX,S.pageY,P)},ni=function(S,B,T){var E=1-u.dragResistance,z=1-u.edgeResistance,R=u.pointerX,H=u.pointerY,I=W,$=u.x,K=u.y,j=u.endX,st=u.endY,yt=u.endRotation,vt=xt,ft,pt,kt,it,me,jt;u.pointerX=S,u.pointerY=B,C&&(S-=qs(D),B-=Gs(D)),a?(it=Math.atan2(Ot.y-B,S-Ot.x)*qh,me=u.y-it,me>180?(W-=360,u.y=it):me<-180&&(W+=360,u.y=it),u.x!==V||Math.max(Math.abs(Y-S),Math.abs(N-B))>d?(u.y=it,kt=V+(W-it)*E):kt=V):(et&&(jt=S*et.a+B*et.c+et.e,B=S*et.b+B*et.d+et.f,S=jt),pt=B-N,ft=S-Y,pt<d&&pt>-d&&(pt=0),ft<d&&ft>-d&&(ft=0),(u.lockAxis||u.lockedAxis)&&(ft||pt)&&(jt=u.lockedAxis,jt||(u.lockedAxis=jt=h&&Math.abs(ft)>Math.abs(pt)?"y":f?"x":null,jt&&Sn(u.vars.onLockAxis)&&u.vars.onLockAxis.call(u,u.pointerEvent)),jt==="y"?pt=0:jt==="x"&&(ft=0)),kt=yn(V+ft*E),it=yn(W+pt*E)),(_e||It||$t)&&(u.x!==kt||u.y!==it&&!a)&&($t&&(Es.x=kt,Es.y=it,jt=$t(Es),kt=yn(jt.x),it=yn(jt.y)),_e&&(kt=yn(_e(kt))),It&&(it=yn(It(it)))),G&&(kt>q?kt=q+Math.round((kt-q)*z):kt<J&&(kt=J+Math.round((kt-J)*z)),a||(it>ht?it=Math.round(ht+(it-ht)*z):it<Q&&(it=Math.round(Q+(it-Q)*z)))),(u.x!==kt||u.y!==it&&!a)&&(a?(u.endRotation=u.x=u.endX=kt,xt=!0):(f&&(u.y=u.endY=it,xt=!0),h&&(u.x=u.endX=kt,xt=!0)),!T||Kt(u,"move","onMove")!==!1?!u.isDragging&&u.isPressed&&(u.isDragging=mi=!0,Kt(u,"dragstart","onDragStart")):(u.pointerX=R,u.pointerY=H,W=I,u.x=$,u.y=K,u.endX=j,u.endY=st,u.endRotation=yt,xt=vt))},Vt=function U(S,B){if(!O||!u.isPressed||S&&ut!=null&&!B&&(S.pointerId&&S.pointerId!==ut&&S.target!==e||S.changedTouches&&!tm(S.changedTouches,ut))){Wt&&S&&O&&yi(S);return}u.isPressed=!1;var T=S,E=u.isDragging,z=u.vars.allowContextMenu&&S&&(S.ctrlKey||S.which>2),R=mt.delayedCall(.001,zi),H,I,$,K,j;if(ot?(ee(ot,"touchend",U),ee(ot,"touchmove",tt),ee(ot,"touchcancel",U),ee(D,"touchstart",tu)):ee(D,"mousemove",tt),ee(Dt,"touchforcechange",yi),(!ca||!ot)&&(ee(D,"mouseup",U),S&&S.target&&ee(S.target,"mouseup",U)),xt=!1,E&&(_=Kh=vn(),u.isDragging=!1),Jh(re),rt&&!z){S&&(ee(S.target,"change",U),u.pointerEvent=T),Bo(p,!1),Kt(u,"release","onRelease"),Kt(u,"click","onClick"),rt=!1;return}for(I=p.length;--I>-1;)gl(p[I],"cursor",i.cursor||(i.cursor!==!1?Or:null));if(Ta--,S){if(H=S.changedTouches,H&&(S=H[0],S!==Pt&&S.identifier!==ut)){for(I=H.length;--I>-1&&(S=H[I]).identifier!==ut&&S.target!==e;);if(I<0&&!B)return}u.pointerEvent=T,u.pointerX=S.pageX,u.pointerY=S.pageY}return z&&T?(yi(T),Wt=!0,Kt(u,"release","onRelease")):T&&!E?(Wt=!1,we&&(i.snap||i.bounds)&&bn(i.inertia||i.throwProps),Kt(u,"release","onRelease"),(!Dr||T.type!=="touchmove")&&T.type.indexOf("cancel")===-1&&(Kt(u,"click","onClick"),vn()-v<300&&Kt(u,"doubleclick","onDoubleClick"),K=T.target||e,v=vn(),j=function(){v!==_i&&u.enabled()&&!u.isPressed&&!T.defaultPrevented&&(K.click?K.click():D.createEvent&&($=D.createEvent("MouseEvents"),$.initMouseEvent("click",!0,!0,Dt,1,u.pointerEvent.screenX,u.pointerEvent.screenY,u.pointerX,u.pointerY,!1,!1,!1,!1,0,null),K.dispatchEvent($)))},!Dr&&!T.defaultPrevented&&mt.delayedCall(.05,j))):(bn(i.inertia||i.throwProps),!u.allowEventDefault&&T&&(i.dragClickables!==!1||!k.call(u,T.target))&&E&&(!A||Rt&&A===Rt)&&T.cancelable!==!1?(Wt=!0,yi(T)):Wt=!1,Kt(u,"release","onRelease")),xi()&&R.duration(u.tween.duration()),E&&Kt(u,"dragend","onDragEnd"),!0},Se=function(S){if(S&&u.isDragging&&!F){var B=S.target||e.parentNode,T=B.scrollLeft-B._gsScrollX,E=B.scrollTop-B._gsScrollY;(T||E)&&(et?(Y-=T*et.a+E*et.c,N-=E*et.d+T*et.b):(Y-=T,N-=E),B._gsScrollX+=T,B._gsScrollY+=E,ni(u.pointerX,u.pointerY))}},Xe=function(S){var B=vn(),T=B-v<100,E=B-_<50,z=T&&_i===v,R=u.pointerEvent&&u.pointerEvent.defaultPrevented,H=T&&Lt===v,I=S.isTrusted||S.isTrusted==null&&T&&z;if((z||E&&u.vars.suppressClickOnDrag!==!1)&&S.stopImmediatePropagation&&S.stopImmediatePropagation(),T&&!(u.pointerEvent&&u.pointerEvent.defaultPrevented)&&(!z||I&&!H)){I&&z&&(Lt=v),_i=v;return}(u.isPressed||E||T)&&(!I||!S.detail||!T||R)&&yi(S),!T&&!E&&!mi&&(S&&S.target&&(u.pointerEvent=S),Kt(u,"click","onClick"))},Bi=function(S){return et?{x:S.x*et.a+S.y*et.c+et.e,y:S.x*et.b+S.y*et.d+et.f}:{x:S.x,y:S.y}};return te=t.get(e),te&&te.kill(),n.startDrag=function(U,S){var B,T,E,z;ii(U||u.pointerEvent,!0),S&&!u.hitTest(U||u.pointerEvent)&&(B=Rs(U||u.pointerEvent),T=Rs(e),E=Bi({x:B.left+B.width/2,y:B.top+B.height/2}),z=Bi({x:T.left+T.width/2,y:T.top+T.height/2}),Y-=E.x-z.x,N-=E.y-z.y),u.isDragging||(u.isDragging=mi=!0,Kt(u,"dragstart","onDragStart"))},n.drag=tt,n.endDrag=function(U){return Vt(U||u.pointerEvent,!0)},n.timeSinceDrag=function(){return u.isDragging?0:(vn()-_)/1e3},n.timeSinceClick=function(){return(vn()-v)/1e3},n.hitTest=function(U,S){return t.hitTest(u.target,U,S)},n.getDirection=function(U,S){var B=U==="velocity"&&Ei?U:Gr(U)&&!a?"element":"start",T,E,z,R,H,I;return B==="element"&&(H=Rs(u.target),I=Rs(U)),T=B==="start"?u.x-V:B==="velocity"?Ei.getVelocity(e,l):H.left+H.width/2-(I.left+I.width/2),a?T<0?"counter-clockwise":"clockwise":(S=S||2,E=B==="start"?u.y-W:B==="velocity"?Ei.getVelocity(e,c):H.top+H.height/2-(I.top+I.height/2),z=Math.abs(T/E),R=z<1/S?"":T<0?"left":"right",z<S&&(R!==""&&(R+="-"),R+=E<0?"up":"down"),R)},n.applyBounds=function(U,S){var B,T,E,z,R,H;if(U&&i.bounds!==U)return i.bounds=U,u.update(!0,S);if(Mt(!0),ei(),G&&!xi()){if(B=u.x,T=u.y,B>q?B=q:B<J&&(B=J),T>ht?T=ht:T<Q&&(T=Q),(u.x!==B||u.y!==T)&&(E=!0,u.x=u.endX=B,a?u.endRotation=B:u.y=u.endY=T,xt=!0,re(!0),u.autoScroll&&!u.isDragging))for(dl(e.parentNode),z=e,Ji.scrollTop=Dt.pageYOffset!=null?Dt.pageYOffset:D.documentElement.scrollTop!=null?D.documentElement.scrollTop:D.body.scrollTop,Ji.scrollLeft=Dt.pageXOffset!=null?Dt.pageXOffset:D.documentElement.scrollLeft!=null?D.documentElement.scrollLeft:D.body.scrollLeft;z&&!H;)H=sr(z.parentNode),R=H?Ji:z.parentNode,f&&R.scrollTop>R._gsMaxScrollY&&(R.scrollTop=R._gsMaxScrollY),h&&R.scrollLeft>R._gsMaxScrollX&&(R.scrollLeft=R._gsMaxScrollX),z=R;u.isThrowing&&(E||u.endX>q||u.endX<J||u.endY>ht||u.endY<Q)&&bn(i.inertia||i.throwProps,E)}return u},n.update=function(U,S,B){if(S&&u.isPressed){var T=rs(e),E=Di.apply({x:u.x-V,y:u.y-W}),z=rs(e.parentNode,!0);z.apply({x:T.e-E.x,y:T.f-E.y},E),u.x-=E.x-z.e,u.y-=E.y-z.f,re(!0),Oi()}var R=u.x,H=u.y;return bi(!S),U?u.applyBounds():(xt&&B&&re(!0),Mt(!0)),S&&(ni(u.pointerX,u.pointerY),xt&&re(!0)),u.isPressed&&!S&&(h&&Math.abs(R-u.x)>.01||f&&Math.abs(H-u.y)>.01&&!a)&&Oi(),u.autoScroll&&(dl(e.parentNode,u.isDragging),m=u.isDragging,re(!0),iu(e,Se),eu(e,Se)),u},n.enable=function(U){var S={lazy:!0},B,T,E;if(i.cursor!==!1&&(S.cursor=i.cursor||Or),mt.utils.checkPrefix("touchCallout")&&(S.touchCallout="none"),U!=="soft"){for(Qh(p,h===f?"none":i.allowNativeTouchScrolling&&e.scrollHeight===e.clientHeight==(e.scrollWidth===e.clientHeight)||i.allowEventDefault?"manipulation":h?"pan-y":"pan-x"),T=p.length;--T>-1;)E=p[T],ca||le(E,"mousedown",ii),le(E,"touchstart",ii),le(E,"click",Xe,!0),mt.set(E,S),E.getBBox&&E.ownerSVGElement&&h!==f&&mt.set(E.ownerSVGElement,{touchAction:i.allowNativeTouchScrolling||i.allowEventDefault?"manipulation":h?"pan-y":"pan-x"}),i.allowContextMenu||le(E,"contextmenu",qt);Bo(p,!1)}return eu(e,Se),O=!0,Ei&&U!=="soft"&&Ei.track(F||e,o?"x,y":a?"rotation":"top,left"),e._gsDragID=B=e._gsDragID||"d"+q_++,Us[B]=u,F&&(F.enable(),F.element._gsDragID=B),(i.bounds||a)&&Oi(),i.bounds&&u.applyBounds(),u},n.disable=function(U){for(var S=u.isDragging,B=p.length,T;--B>-1;)gl(p[B],"cursor",null);if(U!=="soft"){for(Qh(p,null),B=p.length;--B>-1;)T=p[B],gl(T,"touchCallout",null),ee(T,"mousedown",ii),ee(T,"touchstart",ii),ee(T,"click",Xe,!0),ee(T,"contextmenu",qt);Bo(p,!0),ot&&(ee(ot,"touchcancel",Vt),ee(ot,"touchend",Vt),ee(ot,"touchmove",tt)),ee(D,"mouseup",Vt),ee(D,"mousemove",tt)}return iu(e,Se),O=!1,Ei&&U!=="soft"&&(Ei.untrack(F||e,o?"x,y":a?"rotation":"top,left"),u.tween&&u.tween.kill()),F&&F.disable(),Jh(re),u.isDragging=u.isPressed=rt=!1,S&&Kt(u,"dragend","onDragEnd"),u},n.enabled=function(U,S){return arguments.length?U?u.enable(S):u.disable(S):O},n.kill=function(){return u.isThrowing=!1,u.tween&&u.tween.kill(),u.disable(),mt.set(p,{clearProps:"userSelect"}),delete Us[e._gsDragID],u},n.revert=function(){this.kill(),this.styles&&this.styles.revert()},~r.indexOf("scroll")&&(F=n.scrollProxy=new rm(e,Q_({onKill:function(){u.isPressed&&Vt(null)}},i)),e.style.overflowY=f&&!sc?"auto":"hidden",e.style.overflowX=h&&!sc?"auto":"hidden",e=F.content),a?g.rotation=1:(h&&(g[l]=1),f&&(g[c]=1)),M.force3D="force3D"in i?i.force3D:!0,ng(Gh(n)),n.enable(),n}return t.register=function(i){mt=i,_l()},t.create=function(i,n){return nc||_l(!0),Dn(i).map(function(r){return new t(r,n)})},t.get=function(i){return Us[(Dn(i)[0]||{})._gsDragID]},t.timeSinceDrag=function(){return(vn()-Kh)/1e3},t.hitTest=function(i,n,r){if(i===n)return!1;var o=Rs(i),a=Rs(n),l=o.top,c=o.left,h=o.right,f=o.bottom,d=o.width,u=o.height,p=a.left>h||a.right<c||a.top>f||a.bottom<l,g,_,m;return p||!r?!p:(m=(r+"").indexOf("%")!==-1,r=parseFloat(r)||0,g={left:Math.max(c,a.left),top:Math.max(l,a.top)},g.width=Math.min(h,a.right)-g.left,g.height=Math.min(f,a.bottom)-g.top,g.width<0||g.height<0?!1:m?(r*=.01,_=g.width*g.height,_>=d*u*r||_>=a.width*a.height*r):g.width>r&&g.height>r)},t}(om);J_(lr.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1});lr.zIndex=1e3;lr.version="3.13.0";rg()&&mt.registerPlugin(lr);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function yo(s){return s+.5|0}const On=(s,t,e)=>Math.max(Math.min(s,e),t);function Ar(s){return On(yo(s*2.55),0,255)}function In(s){return On(yo(s*255),0,255)}function on(s){return On(yo(s/2.55)/100,0,1)}function ru(s){return On(yo(s*100),0,100)}const wi={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},lc=[..."0123456789ABCDEF"],am=s=>lc[s&15],lm=s=>lc[(s&240)>>4]+lc[s&15],No=s=>(s&240)>>4===(s&15),cm=s=>No(s.r)&&No(s.g)&&No(s.b)&&No(s.a);function hm(s){var t=s.length,e;return s[0]==="#"&&(t===4||t===5?e={r:255&wi[s[1]]*17,g:255&wi[s[2]]*17,b:255&wi[s[3]]*17,a:t===5?wi[s[4]]*17:255}:(t===7||t===9)&&(e={r:wi[s[1]]<<4|wi[s[2]],g:wi[s[3]]<<4|wi[s[4]],b:wi[s[5]]<<4|wi[s[6]],a:t===9?wi[s[7]]<<4|wi[s[8]]:255})),e}const um=(s,t)=>s<255?t(s):"";function fm(s){var t=cm(s)?am:lm;return s?"#"+t(s.r)+t(s.g)+t(s.b)+um(s.a,t):void 0}const dm=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function cg(s,t,e){const i=t*Math.min(e,1-e),n=(r,o=(r+s/30)%12)=>e-i*Math.max(Math.min(o-3,9-o,1),-1);return[n(0),n(8),n(4)]}function gm(s,t,e){const i=(n,r=(n+s/60)%6)=>e-e*t*Math.max(Math.min(r,4-r,1),0);return[i(5),i(3),i(1)]}function pm(s,t,e){const i=cg(s,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)i[n]*=1-t-e,i[n]+=t;return i}function _m(s,t,e,i,n){return s===n?(t-e)/i+(t<e?6:0):t===n?(e-s)/i+2:(s-t)/i+4}function Gc(s){const e=s.r/255,i=s.g/255,n=s.b/255,r=Math.max(e,i,n),o=Math.min(e,i,n),a=(r+o)/2;let l,c,h;return r!==o&&(h=r-o,c=a>.5?h/(2-r-o):h/(r+o),l=_m(e,i,n,h,r),l=l*60+.5),[l|0,c||0,a]}function qc(s,t,e,i){return(Array.isArray(t)?s(t[0],t[1],t[2]):s(t,e,i)).map(In)}function Kc(s,t,e){return qc(cg,s,t,e)}function mm(s,t,e){return qc(pm,s,t,e)}function bm(s,t,e){return qc(gm,s,t,e)}function hg(s){return(s%360+360)%360}function xm(s){const t=dm.exec(s);let e=255,i;if(!t)return;t[5]!==i&&(e=t[6]?Ar(+t[5]):In(+t[5]));const n=hg(+t[2]),r=+t[3]/100,o=+t[4]/100;return t[1]==="hwb"?i=mm(n,r,o):t[1]==="hsv"?i=bm(n,r,o):i=Kc(n,r,o),{r:i[0],g:i[1],b:i[2],a:e}}function ym(s,t){var e=Gc(s);e[0]=hg(e[0]+t),e=Kc(e),s.r=e[0],s.g=e[1],s.b=e[2]}function vm(s){if(!s)return;const t=Gc(s),e=t[0],i=ru(t[1]),n=ru(t[2]);return s.a<255?`hsla(${e}, ${i}%, ${n}%, ${on(s.a)})`:`hsl(${e}, ${i}%, ${n}%)`}const ou={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},au={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function wm(){const s={},t=Object.keys(au),e=Object.keys(ou);let i,n,r,o,a;for(i=0;i<t.length;i++){for(o=a=t[i],n=0;n<e.length;n++)r=e[n],a=a.replace(r,ou[r]);r=parseInt(au[o],16),s[a]=[r>>16&255,r>>8&255,r&255]}return s}let Wo;function Sm(s){Wo||(Wo=wm(),Wo.transparent=[0,0,0,0]);const t=Wo[s.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Mm=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function km(s){const t=Mm.exec(s);let e=255,i,n,r;if(t){if(t[7]!==i){const o=+t[7];e=t[8]?Ar(o):On(o*255,0,255)}return i=+t[1],n=+t[3],r=+t[5],i=255&(t[2]?Ar(i):On(i,0,255)),n=255&(t[4]?Ar(n):On(n,0,255)),r=255&(t[6]?Ar(r):On(r,0,255)),{r:i,g:n,b:r,a:e}}}function Tm(s){return s&&(s.a<255?`rgba(${s.r}, ${s.g}, ${s.b}, ${on(s.a)})`:`rgb(${s.r}, ${s.g}, ${s.b})`)}const ml=s=>s<=.0031308?s*12.92:Math.pow(s,1/2.4)*1.055-.055,Ls=s=>s<=.04045?s/12.92:Math.pow((s+.055)/1.055,2.4);function Pm(s,t,e){const i=Ls(on(s.r)),n=Ls(on(s.g)),r=Ls(on(s.b));return{r:In(ml(i+e*(Ls(on(t.r))-i))),g:In(ml(n+e*(Ls(on(t.g))-n))),b:In(ml(r+e*(Ls(on(t.b))-r))),a:s.a+e*(t.a-s.a)}}function Vo(s,t,e){if(s){let i=Gc(s);i[t]=Math.max(0,Math.min(i[t]+i[t]*e,t===0?360:1)),i=Kc(i),s.r=i[0],s.g=i[1],s.b=i[2]}}function ug(s,t){return s&&Object.assign(t||{},s)}function lu(s){var t={r:0,g:0,b:0,a:255};return Array.isArray(s)?s.length>=3&&(t={r:s[0],g:s[1],b:s[2],a:255},s.length>3&&(t.a=In(s[3]))):(t=ug(s,{r:0,g:0,b:0,a:1}),t.a=In(t.a)),t}function Cm(s){return s.charAt(0)==="r"?km(s):xm(s)}class uo{constructor(t){if(t instanceof uo)return t;const e=typeof t;let i;e==="object"?i=lu(t):e==="string"&&(i=hm(t)||Sm(t)||Cm(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=ug(this._rgb);return t&&(t.a=on(t.a)),t}set rgb(t){this._rgb=lu(t)}rgbString(){return this._valid?Tm(this._rgb):void 0}hexString(){return this._valid?fm(this._rgb):void 0}hslString(){return this._valid?vm(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,n=t.rgb;let r;const o=e===r?.5:e,a=2*o-1,l=i.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;r=1-c,i.r=255&c*i.r+r*n.r+.5,i.g=255&c*i.g+r*n.g+.5,i.b=255&c*i.b+r*n.b+.5,i.a=o*i.a+(1-o)*n.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=Pm(this._rgb,t._rgb,e)),this}clone(){return new uo(this.rgb)}alpha(t){return this._rgb.a=In(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=yo(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Vo(this._rgb,2,t),this}darken(t){return Vo(this._rgb,2,-t),this}saturate(t){return Vo(this._rgb,1,t),this}desaturate(t){return Vo(this._rgb,1,-t),this}rotate(t){return ym(this._rgb,t),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Zi(){}const Dm=(()=>{let s=0;return()=>s++})();function dt(s){return s==null}function Bt(s){if(Array.isArray&&Array.isArray(s))return!0;const t=Object.prototype.toString.call(s);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function _t(s){return s!==null&&Object.prototype.toString.call(s)==="[object Object]"}function Gt(s){return(typeof s=="number"||s instanceof Number)&&isFinite(+s)}function si(s,t){return Gt(s)?s:t}function nt(s,t){return typeof s>"u"?t:s}const Om=(s,t)=>typeof s=="string"&&s.endsWith("%")?parseFloat(s)/100:+s/t,fg=(s,t)=>typeof s=="string"&&s.endsWith("%")?parseFloat(s)/100*t:+s;function Et(s,t,e){if(s&&typeof s.call=="function")return s.apply(e,t)}function Tt(s,t,e,i){let n,r,o;if(Bt(s))for(r=s.length,n=0;n<r;n++)t.call(e,s[n],n);else if(_t(s))for(o=Object.keys(s),r=o.length,n=0;n<r;n++)t.call(e,s[o[n]],o[n])}function Pa(s,t){let e,i,n,r;if(!s||!t||s.length!==t.length)return!1;for(e=0,i=s.length;e<i;++e)if(n=s[e],r=t[e],n.datasetIndex!==r.datasetIndex||n.index!==r.index)return!1;return!0}function Ca(s){if(Bt(s))return s.map(Ca);if(_t(s)){const t=Object.create(null),e=Object.keys(s),i=e.length;let n=0;for(;n<i;++n)t[e[n]]=Ca(s[e[n]]);return t}return s}function dg(s){return["__proto__","prototype","constructor"].indexOf(s)===-1}function Am(s,t,e,i){if(!dg(s))return;const n=t[s],r=e[s];_t(n)&&_t(r)?fo(n,r,i):t[s]=Ca(r)}function fo(s,t,e){const i=Bt(t)?t:[t],n=i.length;if(!_t(s))return s;e=e||{};const r=e.merger||Am;let o;for(let a=0;a<n;++a){if(o=i[a],!_t(o))continue;const l=Object.keys(o);for(let c=0,h=l.length;c<h;++c)r(l[c],s,o,e)}return s}function Kr(s,t){return fo(s,t,{merger:Em})}function Em(s,t,e){if(!dg(s))return;const i=t[s],n=e[s];_t(i)&&_t(n)?Kr(i,n):Object.prototype.hasOwnProperty.call(t,s)||(t[s]=Ca(n))}const cu={"":s=>s,x:s=>s.x,y:s=>s.y};function Rm(s){const t=s.split("."),e=[];let i="";for(const n of t)i+=n,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function Lm(s){const t=Rm(s);return e=>{for(const i of t){if(i==="")break;e=e&&e[i]}return e}}function Wn(s,t){return(cu[t]||(cu[t]=Lm(t)))(s)}function Qc(s){return s.charAt(0).toUpperCase()+s.slice(1)}const go=s=>typeof s<"u",Vn=s=>typeof s=="function",hu=(s,t)=>{if(s.size!==t.size)return!1;for(const e of s)if(!t.has(e))return!1;return!0};function Fm(s){return s.type==="mouseup"||s.type==="click"||s.type==="contextmenu"}const wt=Math.PI,Ft=2*wt,Im=Ft+wt,Da=Number.POSITIVE_INFINITY,zm=wt/180,Jt=wt/2,Kn=wt/4,uu=wt*2/3,An=Math.log10,Ki=Math.sign;function Qr(s,t,e){return Math.abs(s-t)<e}function fu(s){const t=Math.round(s);s=Qr(s,t,s/1e3)?t:s;const e=Math.pow(10,Math.floor(An(s))),i=s/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function Bm(s){const t=[],e=Math.sqrt(s);let i;for(i=1;i<e;i++)s%i===0&&(t.push(i),t.push(s/i));return e===(e|0)&&t.push(e),t.sort((n,r)=>n-r).pop(),t}function Nm(s){return typeof s=="symbol"||typeof s=="object"&&s!==null&&!(Symbol.toPrimitive in s||"toString"in s||"valueOf"in s)}function rr(s){return!Nm(s)&&!isNaN(parseFloat(s))&&isFinite(s)}function Wm(s,t){const e=Math.round(s);return e-t<=s&&e+t>=s}function gg(s,t,e){let i,n,r;for(i=0,n=s.length;i<n;i++)r=s[i][e],isNaN(r)||(t.min=Math.min(t.min,r),t.max=Math.max(t.max,r))}function Fi(s){return s*(wt/180)}function Zc(s){return s*(180/wt)}function du(s){if(!Gt(s))return;let t=1,e=0;for(;Math.round(s*t)/t!==s;)t*=10,e++;return e}function pg(s,t){const e=t.x-s.x,i=t.y-s.y,n=Math.sqrt(e*e+i*i);let r=Math.atan2(i,e);return r<-.5*wt&&(r+=Ft),{angle:r,distance:n}}function cc(s,t){return Math.sqrt(Math.pow(t.x-s.x,2)+Math.pow(t.y-s.y,2))}function Vm(s,t){return(s-t+Im)%Ft-wt}function De(s){return(s%Ft+Ft)%Ft}function po(s,t,e,i){const n=De(s),r=De(t),o=De(e),a=De(r-n),l=De(o-n),c=De(n-r),h=De(n-o);return n===r||n===o||i&&r===o||a>l&&c<h}function ge(s,t,e){return Math.max(t,Math.min(e,s))}function Hm(s){return ge(s,-32768,32767)}function un(s,t,e,i=1e-6){return s>=Math.min(t,e)-i&&s<=Math.max(t,e)+i}function Jc(s,t,e){e=e||(o=>s[o]<t);let i=s.length-1,n=0,r;for(;i-n>1;)r=n+i>>1,e(r)?n=r:i=r;return{lo:n,hi:i}}const fn=(s,t,e,i)=>Jc(s,e,i?n=>{const r=s[n][t];return r<e||r===e&&s[n+1][t]===e}:n=>s[n][t]<e),Ym=(s,t,e)=>Jc(s,e,i=>s[i][t]>=e);function $m(s,t,e){let i=0,n=s.length;for(;i<n&&s[i]<t;)i++;for(;n>i&&s[n-1]>e;)n--;return i>0||n<s.length?s.slice(i,n):s}const _g=["push","pop","shift","splice","unshift"];function Xm(s,t){if(s._chartjs){s._chartjs.listeners.push(t);return}Object.defineProperty(s,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),_g.forEach(e=>{const i="_onData"+Qc(e),n=s[e];Object.defineProperty(s,e,{configurable:!0,enumerable:!1,value(...r){const o=n.apply(this,r);return s._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...r)}),o}})})}function gu(s,t){const e=s._chartjs;if(!e)return;const i=e.listeners,n=i.indexOf(t);n!==-1&&i.splice(n,1),!(i.length>0)&&(_g.forEach(r=>{delete s[r]}),delete s._chartjs)}function mg(s){const t=new Set(s);return t.size===s.length?s:Array.from(t)}const bg=function(){return typeof window>"u"?function(s){return s()}:window.requestAnimationFrame}();function xg(s,t){let e=[],i=!1;return function(...n){e=n,i||(i=!0,bg.call(window,()=>{i=!1,s.apply(t,e)}))}}function jm(s,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(s,t,i)):s.apply(this,i),t}}const th=s=>s==="start"?"left":s==="end"?"right":"center",Pe=(s,t,e)=>s==="start"?t:s==="end"?e:(t+e)/2,Um=(s,t,e,i)=>s===(i?"left":"right")?e:s==="center"?(t+e)/2:t;function yg(s,t,e){const i=t.length;let n=0,r=i;if(s._sorted){const{iScale:o,vScale:a,_parsed:l}=s,c=s.dataset&&s.dataset.options?s.dataset.options.spanGaps:null,h=o.axis,{min:f,max:d,minDefined:u,maxDefined:p}=o.getUserBounds();if(u){if(n=Math.min(fn(l,h,f).lo,e?i:fn(t,h,o.getPixelForValue(f)).lo),c){const g=l.slice(0,n+1).reverse().findIndex(_=>!dt(_[a.axis]));n-=Math.max(0,g)}n=ge(n,0,i-1)}if(p){let g=Math.max(fn(l,o.axis,d,!0).hi+1,e?0:fn(t,h,o.getPixelForValue(d),!0).hi+1);if(c){const _=l.slice(g-1).findIndex(m=>!dt(m[a.axis]));g+=Math.max(0,_)}r=ge(g,n,i)-n}else r=i-n}return{start:n,count:r}}function vg(s){const{xScale:t,yScale:e,_scaleRanges:i}=s,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return s._scaleRanges=n,!0;const r=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,n),r}const Ho=s=>s===0||s===1,pu=(s,t,e)=>-(Math.pow(2,10*(s-=1))*Math.sin((s-t)*Ft/e)),_u=(s,t,e)=>Math.pow(2,-10*s)*Math.sin((s-t)*Ft/e)+1,Zr={linear:s=>s,easeInQuad:s=>s*s,easeOutQuad:s=>-s*(s-2),easeInOutQuad:s=>(s/=.5)<1?.5*s*s:-.5*(--s*(s-2)-1),easeInCubic:s=>s*s*s,easeOutCubic:s=>(s-=1)*s*s+1,easeInOutCubic:s=>(s/=.5)<1?.5*s*s*s:.5*((s-=2)*s*s+2),easeInQuart:s=>s*s*s*s,easeOutQuart:s=>-((s-=1)*s*s*s-1),easeInOutQuart:s=>(s/=.5)<1?.5*s*s*s*s:-.5*((s-=2)*s*s*s-2),easeInQuint:s=>s*s*s*s*s,easeOutQuint:s=>(s-=1)*s*s*s*s+1,easeInOutQuint:s=>(s/=.5)<1?.5*s*s*s*s*s:.5*((s-=2)*s*s*s*s+2),easeInSine:s=>-Math.cos(s*Jt)+1,easeOutSine:s=>Math.sin(s*Jt),easeInOutSine:s=>-.5*(Math.cos(wt*s)-1),easeInExpo:s=>s===0?0:Math.pow(2,10*(s-1)),easeOutExpo:s=>s===1?1:-Math.pow(2,-10*s)+1,easeInOutExpo:s=>Ho(s)?s:s<.5?.5*Math.pow(2,10*(s*2-1)):.5*(-Math.pow(2,-10*(s*2-1))+2),easeInCirc:s=>s>=1?s:-(Math.sqrt(1-s*s)-1),easeOutCirc:s=>Math.sqrt(1-(s-=1)*s),easeInOutCirc:s=>(s/=.5)<1?-.5*(Math.sqrt(1-s*s)-1):.5*(Math.sqrt(1-(s-=2)*s)+1),easeInElastic:s=>Ho(s)?s:pu(s,.075,.3),easeOutElastic:s=>Ho(s)?s:_u(s,.075,.3),easeInOutElastic(s){return Ho(s)?s:s<.5?.5*pu(s*2,.1125,.45):.5+.5*_u(s*2-1,.1125,.45)},easeInBack(s){return s*s*((1.70158+1)*s-1.70158)},easeOutBack(s){return(s-=1)*s*((1.70158+1)*s********)+1},easeInOutBack(s){let t=1.70158;return(s/=.5)<1?.5*(s*s*(((t*=1.525)+1)*s-t)):.5*((s-=2)*s*(((t*=1.525)+1)*s+t)+2)},easeInBounce:s=>1-Zr.easeOutBounce(1-s),easeOutBounce(s){return s<1/2.75?7.5625*s*s:s<2/2.75?7.5625*(s-=1.5/2.75)*s+.75:s<2.5/2.75?7.5625*(s-=2.25/2.75)*s+.9375:7.5625*(s-=2.625/2.75)*s+.984375},easeInOutBounce:s=>s<.5?Zr.easeInBounce(s*2)*.5:Zr.easeOutBounce(s*2-1)*.5+.5};function eh(s){if(s&&typeof s=="object"){const t=s.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function mu(s){return eh(s)?s:new uo(s)}function bl(s){return eh(s)?s:new uo(s).saturate(.5).darken(.1).hexString()}const Gm=["x","y","borderWidth","radius","tension"],qm=["color","borderColor","backgroundColor"];function Km(s){s.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),s.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),s.set("animations",{colors:{type:"color",properties:qm},numbers:{type:"number",properties:Gm}}),s.describe("animations",{_fallback:"animation"}),s.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Qm(s){s.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const bu=new Map;function Zm(s,t){t=t||{};const e=s+JSON.stringify(t);let i=bu.get(e);return i||(i=new Intl.NumberFormat(s,t),bu.set(e,i)),i}function vo(s,t,e){return Zm(t,e).format(s)}const wg={values(s){return Bt(s)?s:""+s},numeric(s,t,e){if(s===0)return"0";const i=this.chart.options.locale;let n,r=s;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),r=Jm(s,e)}const o=An(Math.abs(r)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),vo(s,i,l)},logarithmic(s,t,e){if(s===0)return"0";const i=e[t].significand||s/Math.pow(10,Math.floor(An(s)));return[1,2,3,5,10,15].includes(i)||t>.8*e.length?wg.numeric.call(this,s,t,e):""}};function Jm(s,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&s!==Math.floor(s)&&(e=s-Math.floor(s)),e}var Wa={formatters:wg};function t0(s){s.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Wa.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),s.route("scale.ticks","color","","color"),s.route("scale.grid","color","","borderColor"),s.route("scale.border","color","","borderColor"),s.route("scale.title","color","","color"),s.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),s.describe("scales",{_fallback:"scale"}),s.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Ms=Object.create(null),hc=Object.create(null);function Jr(s,t){if(!t)return s;const e=t.split(".");for(let i=0,n=e.length;i<n;++i){const r=e[i];s=s[r]||(s[r]=Object.create(null))}return s}function xl(s,t,e){return typeof t=="string"?fo(Jr(s,t),e):fo(Jr(s,""),t)}class e0{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,n)=>bl(n.backgroundColor),this.hoverBorderColor=(i,n)=>bl(n.borderColor),this.hoverColor=(i,n)=>bl(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return xl(this,t,e)}get(t){return Jr(this,t)}describe(t,e){return xl(hc,t,e)}override(t,e){return xl(Ms,t,e)}route(t,e,i,n){const r=Jr(this,t),o=Jr(this,i),a="_"+e;Object.defineProperties(r,{[a]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=o[n];return _t(l)?Object.assign({},c,l):nt(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var Nt=new e0({_scriptable:s=>!s.startsWith("on"),_indexable:s=>s!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Km,Qm,t0]);function i0(s){return!s||dt(s.size)||dt(s.family)?null:(s.style?s.style+" ":"")+(s.weight?s.weight+" ":"")+s.size+"px "+s.family}function Oa(s,t,e,i,n){let r=t[n];return r||(r=t[n]=s.measureText(n).width,e.push(n)),r>i&&(i=r),i}function n0(s,t,e,i){i=i||{};let n=i.data=i.data||{},r=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(n=i.data={},r=i.garbageCollect=[],i.font=t),s.save(),s.font=t;let o=0;const a=e.length;let l,c,h,f,d;for(l=0;l<a;l++)if(f=e[l],f!=null&&!Bt(f))o=Oa(s,n,r,o,f);else if(Bt(f))for(c=0,h=f.length;c<h;c++)d=f[c],d!=null&&!Bt(d)&&(o=Oa(s,n,r,o,d));s.restore();const u=r.length/2;if(u>e.length){for(l=0;l<u;l++)delete n[r[l]];r.splice(0,u)}return o}function Qn(s,t,e){const i=s.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*i)/i+n}function xu(s,t){!t&&!s||(t=t||s.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,s.width,s.height),t.restore())}function uc(s,t,e,i){Sg(s,t,e,i,null)}function Sg(s,t,e,i,n){let r,o,a,l,c,h,f,d;const u=t.pointStyle,p=t.rotation,g=t.radius;let _=(p||0)*zm;if(u&&typeof u=="object"&&(r=u.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){s.save(),s.translate(e,i),s.rotate(_),s.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),s.restore();return}if(!(isNaN(g)||g<=0)){switch(s.beginPath(),u){default:n?s.ellipse(e,i,n/2,g,0,0,Ft):s.arc(e,i,g,0,Ft),s.closePath();break;case"triangle":h=n?n/2:g,s.moveTo(e+Math.sin(_)*h,i-Math.cos(_)*g),_+=uu,s.lineTo(e+Math.sin(_)*h,i-Math.cos(_)*g),_+=uu,s.lineTo(e+Math.sin(_)*h,i-Math.cos(_)*g),s.closePath();break;case"rectRounded":c=g*.516,l=g-c,o=Math.cos(_+Kn)*l,f=Math.cos(_+Kn)*(n?n/2-c:l),a=Math.sin(_+Kn)*l,d=Math.sin(_+Kn)*(n?n/2-c:l),s.arc(e-f,i-a,c,_-wt,_-Jt),s.arc(e+d,i-o,c,_-Jt,_),s.arc(e+f,i+a,c,_,_+Jt),s.arc(e-d,i+o,c,_+Jt,_+wt),s.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,h=n?n/2:l,s.rect(e-h,i-l,2*h,2*l);break}_+=Kn;case"rectRot":f=Math.cos(_)*(n?n/2:g),o=Math.cos(_)*g,a=Math.sin(_)*g,d=Math.sin(_)*(n?n/2:g),s.moveTo(e-f,i-a),s.lineTo(e+d,i-o),s.lineTo(e+f,i+a),s.lineTo(e-d,i+o),s.closePath();break;case"crossRot":_+=Kn;case"cross":f=Math.cos(_)*(n?n/2:g),o=Math.cos(_)*g,a=Math.sin(_)*g,d=Math.sin(_)*(n?n/2:g),s.moveTo(e-f,i-a),s.lineTo(e+f,i+a),s.moveTo(e+d,i-o),s.lineTo(e-d,i+o);break;case"star":f=Math.cos(_)*(n?n/2:g),o=Math.cos(_)*g,a=Math.sin(_)*g,d=Math.sin(_)*(n?n/2:g),s.moveTo(e-f,i-a),s.lineTo(e+f,i+a),s.moveTo(e+d,i-o),s.lineTo(e-d,i+o),_+=Kn,f=Math.cos(_)*(n?n/2:g),o=Math.cos(_)*g,a=Math.sin(_)*g,d=Math.sin(_)*(n?n/2:g),s.moveTo(e-f,i-a),s.lineTo(e+f,i+a),s.moveTo(e+d,i-o),s.lineTo(e-d,i+o);break;case"line":o=n?n/2:Math.cos(_)*g,a=Math.sin(_)*g,s.moveTo(e-o,i-a),s.lineTo(e+o,i+a);break;case"dash":s.moveTo(e,i),s.lineTo(e+Math.cos(_)*(n?n/2:g),i+Math.sin(_)*g);break;case!1:s.closePath();break}s.fill(),t.borderWidth>0&&s.stroke()}}function dn(s,t,e){return e=e||.5,!t||s&&s.x>t.left-e&&s.x<t.right+e&&s.y>t.top-e&&s.y<t.bottom+e}function Va(s,t){s.save(),s.beginPath(),s.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),s.clip()}function Ha(s){s.restore()}function s0(s,t,e,i,n){if(!t)return s.lineTo(e.x,e.y);if(n==="middle"){const r=(t.x+e.x)/2;s.lineTo(r,t.y),s.lineTo(r,e.y)}else n==="after"!=!!i?s.lineTo(t.x,e.y):s.lineTo(e.x,t.y);s.lineTo(e.x,e.y)}function r0(s,t,e,i){if(!t)return s.lineTo(e.x,e.y);s.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function o0(s,t){t.translation&&s.translate(t.translation[0],t.translation[1]),dt(t.rotation)||s.rotate(t.rotation),t.color&&(s.fillStyle=t.color),t.textAlign&&(s.textAlign=t.textAlign),t.textBaseline&&(s.textBaseline=t.textBaseline)}function a0(s,t,e,i,n){if(n.strikethrough||n.underline){const r=s.measureText(i),o=t-r.actualBoundingBoxLeft,a=t+r.actualBoundingBoxRight,l=e-r.actualBoundingBoxAscent,c=e+r.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;s.strokeStyle=s.fillStyle,s.beginPath(),s.lineWidth=n.decorationWidth||2,s.moveTo(o,h),s.lineTo(a,h),s.stroke()}}function l0(s,t){const e=s.fillStyle;s.fillStyle=t.color,s.fillRect(t.left,t.top,t.width,t.height),s.fillStyle=e}function ks(s,t,e,i,n,r={}){const o=Bt(t)?t:[t],a=r.strokeWidth>0&&r.strokeColor!=="";let l,c;for(s.save(),s.font=n.string,o0(s,r),l=0;l<o.length;++l)c=o[l],r.backdrop&&l0(s,r.backdrop),a&&(r.strokeColor&&(s.strokeStyle=r.strokeColor),dt(r.strokeWidth)||(s.lineWidth=r.strokeWidth),s.strokeText(c,e,i,r.maxWidth)),s.fillText(c,e,i,r.maxWidth),a0(s,e,i,c,r),i+=Number(n.lineHeight);s.restore()}function _o(s,t){const{x:e,y:i,w:n,h:r,radius:o}=t;s.arc(e+o.topLeft,i+o.topLeft,o.topLeft,1.5*wt,wt,!0),s.lineTo(e,i+r-o.bottomLeft),s.arc(e+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,wt,Jt,!0),s.lineTo(e+n-o.bottomRight,i+r),s.arc(e+n-o.bottomRight,i+r-o.bottomRight,o.bottomRight,Jt,0,!0),s.lineTo(e+n,i+o.topRight),s.arc(e+n-o.topRight,i+o.topRight,o.topRight,0,-Jt,!0),s.lineTo(e+o.topLeft,i)}const c0=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,h0=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function u0(s,t){const e=(""+s).match(c0);if(!e||e[1]==="normal")return t*1.2;switch(s=+e[2],e[3]){case"px":return s;case"%":s/=100;break}return t*s}const f0=s=>+s||0;function ih(s,t){const e={},i=_t(t),n=i?Object.keys(t):t,r=_t(s)?i?o=>nt(s[o],s[t[o]]):o=>s[o]:()=>s;for(const o of n)e[o]=f0(r(o));return e}function Mg(s){return ih(s,{top:"y",right:"x",bottom:"y",left:"x"})}function ms(s){return ih(s,["topLeft","topRight","bottomLeft","bottomRight"])}function Fe(s){const t=Mg(s);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function he(s,t){s=s||{},t=t||Nt.font;let e=nt(s.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=nt(s.style,t.style);i&&!(""+i).match(h0)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const n={family:nt(s.family,t.family),lineHeight:u0(nt(s.lineHeight,t.lineHeight),e),size:e,style:i,weight:nt(s.weight,t.weight),string:""};return n.string=i0(n),n}function Er(s,t,e,i){let n,r,o;for(n=0,r=s.length;n<r;++n)if(o=s[n],o!==void 0&&o!==void 0)return o}function d0(s,t,e){const{min:i,max:n}=s,r=fg(t,(n-i)/2),o=(a,l)=>e&&a===0?0:a+l;return{min:o(i,-Math.abs(r)),max:o(n,r)}}function Yn(s,t){return Object.assign(Object.create(s),t)}function nh(s,t=[""],e,i,n=()=>s[0]){const r=e||s;typeof i>"u"&&(i=Cg("_fallback",s));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:s,_rootScopes:r,_fallback:i,_getTarget:n,override:a=>nh([a,...s],t,r,i)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete s[0][l],!0},get(a,l){return Tg(a,l,()=>v0(l,t,s,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(s[0])},has(a,l){return vu(a).includes(l)},ownKeys(a){return vu(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function or(s,t,e,i){const n={_cacheable:!1,_proxy:s,_context:t,_subProxy:e,_stack:new Set,_descriptors:kg(s,i),setContext:r=>or(s,r,e,i),override:r=>or(s.override(r),t,e,i)};return new Proxy(n,{deleteProperty(r,o){return delete r[o],delete s[o],!0},get(r,o,a){return Tg(r,o,()=>p0(r,o,a))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(s,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(s,o)},getPrototypeOf(){return Reflect.getPrototypeOf(s)},has(r,o){return Reflect.has(s,o)},ownKeys(){return Reflect.ownKeys(s)},set(r,o,a){return s[o]=a,delete r[o],!0}})}function kg(s,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:n=t.allKeys}=s;return{allKeys:n,scriptable:e,indexable:i,isScriptable:Vn(e)?e:()=>e,isIndexable:Vn(i)?i:()=>i}}const g0=(s,t)=>s?s+Qc(t):t,sh=(s,t)=>_t(t)&&s!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Tg(s,t,e){if(Object.prototype.hasOwnProperty.call(s,t)||t==="constructor")return s[t];const i=e();return s[t]=i,i}function p0(s,t,e){const{_proxy:i,_context:n,_subProxy:r,_descriptors:o}=s;let a=i[t];return Vn(a)&&o.isScriptable(t)&&(a=_0(t,a,s,e)),Bt(a)&&a.length&&(a=m0(t,a,s,o.isIndexable)),sh(t,a)&&(a=or(a,n,r&&r[t],o)),a}function _0(s,t,e,i){const{_proxy:n,_context:r,_subProxy:o,_stack:a}=e;if(a.has(s))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+s);a.add(s);let l=t(r,o||i);return a.delete(s),sh(s,l)&&(l=rh(n._scopes,n,s,l)),l}function m0(s,t,e,i){const{_proxy:n,_context:r,_subProxy:o,_descriptors:a}=e;if(typeof r.index<"u"&&i(s))return t[r.index%t.length];if(_t(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const f=rh(c,n,s,h);t.push(or(f,r,o&&o[s],a))}}return t}function Pg(s,t,e){return Vn(s)?s(t,e):s}const b0=(s,t)=>s===!0?t:typeof s=="string"?Wn(t,s):void 0;function x0(s,t,e,i,n){for(const r of t){const o=b0(e,r);if(o){s.add(o);const a=Pg(o._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==i)return a}else if(o===!1&&typeof i<"u"&&e!==i)return null}return!1}function rh(s,t,e,i){const n=t._rootScopes,r=Pg(t._fallback,e,i),o=[...s,...n],a=new Set;a.add(i);let l=yu(a,o,e,r||e,i);return l===null||typeof r<"u"&&r!==e&&(l=yu(a,o,r,l,i),l===null)?!1:nh(Array.from(a),[""],n,r,()=>y0(t,e,i))}function yu(s,t,e,i,n){for(;e;)e=x0(s,t,e,i,n);return e}function y0(s,t,e){const i=s._getTarget();t in i||(i[t]={});const n=i[t];return Bt(n)&&_t(e)?e:n||{}}function v0(s,t,e,i){let n;for(const r of t)if(n=Cg(g0(r,s),e),typeof n<"u")return sh(s,n)?rh(e,i,s,n):n}function Cg(s,t){for(const e of t){if(!e)continue;const i=e[s];if(typeof i<"u")return i}}function vu(s){let t=s._keys;return t||(t=s._keys=w0(s._scopes)),t}function w0(s){const t=new Set;for(const e of s)for(const i of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(i);return Array.from(t)}function Dg(s,t,e,i){const{iScale:n}=s,{key:r="r"}=this._parsing,o=new Array(i);let a,l,c,h;for(a=0,l=i;a<l;++a)c=a+e,h=t[c],o[a]={r:n.parse(Wn(h,r),c)};return o}const S0=Number.EPSILON||1e-14,ar=(s,t)=>t<s.length&&!s[t].skip&&s[t],Og=s=>s==="x"?"y":"x";function M0(s,t,e,i){const n=s.skip?t:s,r=t,o=e.skip?t:e,a=cc(r,n),l=cc(o,r);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const f=i*c,d=i*h;return{previous:{x:r.x-f*(o.x-n.x),y:r.y-f*(o.y-n.y)},next:{x:r.x+d*(o.x-n.x),y:r.y+d*(o.y-n.y)}}}function k0(s,t,e){const i=s.length;let n,r,o,a,l,c=ar(s,0);for(let h=0;h<i-1;++h)if(l=c,c=ar(s,h+1),!(!l||!c)){if(Qr(t[h],0,S0)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],r=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(r,2),!(a<=9)&&(o=3/Math.sqrt(a),e[h]=n*o*t[h],e[h+1]=r*o*t[h])}}function T0(s,t,e="x"){const i=Og(e),n=s.length;let r,o,a,l=ar(s,0);for(let c=0;c<n;++c){if(o=a,a=l,l=ar(s,c+1),!a)continue;const h=a[e],f=a[i];o&&(r=(h-o[e])/3,a[`cp1${e}`]=h-r,a[`cp1${i}`]=f-r*t[c]),l&&(r=(l[e]-h)/3,a[`cp2${e}`]=h+r,a[`cp2${i}`]=f+r*t[c])}}function P0(s,t="x"){const e=Og(t),i=s.length,n=Array(i).fill(0),r=Array(i);let o,a,l,c=ar(s,0);for(o=0;o<i;++o)if(a=l,l=c,c=ar(s,o+1),!!l){if(c){const h=c[t]-l[t];n[o]=h!==0?(c[e]-l[e])/h:0}r[o]=a?c?Ki(n[o-1])!==Ki(n[o])?0:(n[o-1]+n[o])/2:n[o-1]:n[o]}k0(s,n,r),T0(s,r,t)}function Yo(s,t,e){return Math.max(Math.min(s,e),t)}function C0(s,t){let e,i,n,r,o,a=dn(s[0],t);for(e=0,i=s.length;e<i;++e)o=r,r=a,a=e<i-1&&dn(s[e+1],t),r&&(n=s[e],o&&(n.cp1x=Yo(n.cp1x,t.left,t.right),n.cp1y=Yo(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=Yo(n.cp2x,t.left,t.right),n.cp2y=Yo(n.cp2y,t.top,t.bottom)))}function D0(s,t,e,i,n){let r,o,a,l;if(t.spanGaps&&(s=s.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")P0(s,n);else{let c=i?s[s.length-1]:s[0];for(r=0,o=s.length;r<o;++r)a=s[r],l=M0(c,a,s[Math.min(r+1,o-(i?0:1))%o],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&C0(s,e)}function oh(){return typeof window<"u"&&typeof document<"u"}function ah(s){let t=s.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Aa(s,t,e){let i;return typeof s=="string"?(i=parseInt(s,10),s.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=s,i}const Ya=s=>s.ownerDocument.defaultView.getComputedStyle(s,null);function O0(s,t){return Ya(s).getPropertyValue(t)}const A0=["top","right","bottom","left"];function bs(s,t,e){const i={};e=e?"-"+e:"";for(let n=0;n<4;n++){const r=A0[n];i[r]=parseFloat(s[t+"-"+r+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const E0=(s,t,e)=>(s>0||t>0)&&(!e||!e.shadowRoot);function R0(s,t){const e=s.touches,i=e&&e.length?e[0]:s,{offsetX:n,offsetY:r}=i;let o=!1,a,l;if(E0(n,r,s.target))a=n,l=r;else{const c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,o=!0}return{x:a,y:l,box:o}}function ns(s,t){if("native"in s)return s;const{canvas:e,currentDevicePixelRatio:i}=t,n=Ya(e),r=n.boxSizing==="border-box",o=bs(n,"padding"),a=bs(n,"border","width"),{x:l,y:c,box:h}=R0(s,e),f=o.left+(h&&a.left),d=o.top+(h&&a.top);let{width:u,height:p}=t;return r&&(u-=o.width+a.width,p-=o.height+a.height),{x:Math.round((l-f)/u*e.width/i),y:Math.round((c-d)/p*e.height/i)}}function L0(s,t,e){let i,n;if(t===void 0||e===void 0){const r=s&&ah(s);if(!r)t=s.clientWidth,e=s.clientHeight;else{const o=r.getBoundingClientRect(),a=Ya(r),l=bs(a,"border","width"),c=bs(a,"padding");t=o.width-c.width-l.width,e=o.height-c.height-l.height,i=Aa(a.maxWidth,r,"clientWidth"),n=Aa(a.maxHeight,r,"clientHeight")}}return{width:t,height:e,maxWidth:i||Da,maxHeight:n||Da}}const $o=s=>Math.round(s*10)/10;function F0(s,t,e,i){const n=Ya(s),r=bs(n,"margin"),o=Aa(n.maxWidth,s,"clientWidth")||Da,a=Aa(n.maxHeight,s,"clientHeight")||Da,l=L0(s,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const d=bs(n,"border","width"),u=bs(n,"padding");c-=u.width+d.width,h-=u.height+d.height}return c=Math.max(0,c-r.width),h=Math.max(0,i?c/i:h-r.height),c=$o(Math.min(c,o,l.maxWidth)),h=$o(Math.min(h,a,l.maxHeight)),c&&!h&&(h=$o(c/2)),(t!==void 0||e!==void 0)&&i&&l.height&&h>l.height&&(h=l.height,c=$o(Math.floor(h*i))),{width:c,height:h}}function wu(s,t,e){const i=t||1,n=Math.floor(s.height*i),r=Math.floor(s.width*i);s.height=Math.floor(s.height),s.width=Math.floor(s.width);const o=s.canvas;return o.style&&(e||!o.style.height&&!o.style.width)&&(o.style.height=`${s.height}px`,o.style.width=`${s.width}px`),s.currentDevicePixelRatio!==i||o.height!==n||o.width!==r?(s.currentDevicePixelRatio=i,o.height=n,o.width=r,s.ctx.setTransform(i,0,0,i,0,0),!0):!1}const I0=function(){let s=!1;try{const t={get passive(){return s=!0,!1}};oh()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return s}();function Su(s,t){const e=O0(s,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function ss(s,t,e,i){return{x:s.x+e*(t.x-s.x),y:s.y+e*(t.y-s.y)}}function z0(s,t,e,i){return{x:s.x+e*(t.x-s.x),y:i==="middle"?e<.5?s.y:t.y:i==="after"?e<1?s.y:t.y:e>0?t.y:s.y}}function B0(s,t,e,i){const n={x:s.cp2x,y:s.cp2y},r={x:t.cp1x,y:t.cp1y},o=ss(s,n,e),a=ss(n,r,e),l=ss(r,t,e),c=ss(o,a,e),h=ss(a,l,e);return ss(c,h,e)}const N0=function(s,t){return{x(e){return s+s+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},W0=function(){return{x(s){return s},setWidth(s){},textAlign(s){return s},xPlus(s,t){return s+t},leftForLtr(s,t){return s}}};function Ks(s,t,e){return s?N0(t,e):W0()}function Ag(s,t){let e,i;(t==="ltr"||t==="rtl")&&(e=s.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),s.prevTextDirection=i)}function Eg(s,t){t!==void 0&&(delete s.prevTextDirection,s.canvas.style.setProperty("direction",t[0],t[1]))}function Rg(s){return s==="angle"?{between:po,compare:Vm,normalize:De}:{between:un,compare:(t,e)=>t-e,normalize:t=>t}}function Mu({start:s,end:t,count:e,loop:i,style:n}){return{start:s%e,end:t%e,loop:i&&(t-s+1)%e===0,style:n}}function V0(s,t,e){const{property:i,start:n,end:r}=e,{between:o,normalize:a}=Rg(i),l=t.length;let{start:c,end:h,loop:f}=s,d,u;if(f){for(c+=l,h+=l,d=0,u=l;d<u&&o(a(t[c%l][i]),n,r);++d)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:f,style:s.style}}function Lg(s,t,e){if(!e)return[s];const{property:i,start:n,end:r}=e,o=t.length,{compare:a,between:l,normalize:c}=Rg(i),{start:h,end:f,loop:d,style:u}=V0(s,t,e),p=[];let g=!1,_=null,m,b,w;const y=()=>l(n,w,m)&&a(n,w)!==0,x=()=>a(r,m)===0||l(r,w,m),k=()=>g||y(),v=()=>!g||x();for(let M=h,C=h;M<=f;++M)b=t[M%o],!b.skip&&(m=c(b[i]),m!==w&&(g=l(m,n,r),_===null&&k()&&(_=a(m,n)===0?M:C),_!==null&&v()&&(p.push(Mu({start:_,end:M,loop:d,count:o,style:u})),_=null),C=M,w=m));return _!==null&&p.push(Mu({start:_,end:f,loop:d,count:o,style:u})),p}function Fg(s,t){const e=[],i=s.segments;for(let n=0;n<i.length;n++){const r=Lg(i[n],s.points,t);r.length&&e.push(...r)}return e}function H0(s,t,e,i){let n=0,r=t-1;if(e&&!i)for(;n<t&&!s[n].skip;)n++;for(;n<t&&s[n].skip;)n++;for(n%=t,e&&(r+=n);r>n&&s[r%t].skip;)r--;return r%=t,{start:n,end:r}}function Y0(s,t,e,i){const n=s.length,r=[];let o=t,a=s[t],l;for(l=t+1;l<=e;++l){const c=s[l%n];c.skip||c.stop?a.skip||(i=!1,r.push({start:t%n,end:(l-1)%n,loop:i}),t=o=c.stop?l:null):(o=l,a.skip&&(t=l)),a=c}return o!==null&&r.push({start:t%n,end:o%n,loop:i}),r}function $0(s,t){const e=s.points,i=s.options.spanGaps,n=e.length;if(!n)return[];const r=!!s._loop,{start:o,end:a}=H0(e,n,r,i);if(i===!0)return ku(s,[{start:o,end:a,loop:r}],e,t);const l=a<o?a+n:a,c=!!s._fullLoop&&o===0&&a===n-1;return ku(s,Y0(e,o,l,c),e,t)}function ku(s,t,e,i){return!i||!i.setContext||!e?t:X0(s,t,e,i)}function X0(s,t,e,i){const n=s._chart.getContext(),r=Tu(s.options),{_datasetIndex:o,options:{spanGaps:a}}=s,l=e.length,c=[];let h=r,f=t[0].start,d=f;function u(p,g,_,m){const b=a?-1:1;if(p!==g){for(p+=l;e[p%l].skip;)p-=b;for(;e[g%l].skip;)g+=b;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:_,style:m}),h=m,f=g%l)}}for(const p of t){f=a?f:p.start;let g=e[f%l],_;for(d=f+1;d<=p.end;d++){const m=e[d%l];_=Tu(i.setContext(Yn(n,{type:"segment",p0:g,p1:m,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:o}))),j0(_,h)&&u(f,d-1,p.loop,h),g=m,h=_}f<d-1&&u(f,d-1,p.loop,h)}return c}function Tu(s){return{backgroundColor:s.backgroundColor,borderCapStyle:s.borderCapStyle,borderDash:s.borderDash,borderDashOffset:s.borderDashOffset,borderJoinStyle:s.borderJoinStyle,borderWidth:s.borderWidth,borderColor:s.borderColor}}function j0(s,t){if(!t)return!1;const e=[],i=function(n,r){return eh(r)?(e.includes(r)||e.push(r),e.indexOf(r)):r};return JSON.stringify(s,i)!==JSON.stringify(t,i)}function Xo(s,t,e){return s.options.clip?s[e]:t[e]}function U0(s,t){const{xScale:e,yScale:i}=s;return e&&i?{left:Xo(e,t,"left"),right:Xo(e,t,"right"),top:Xo(i,t,"top"),bottom:Xo(i,t,"bottom")}:t}function Ig(s,t){const e=t._clip;if(e.disabled)return!1;const i=U0(t,s.chartArea);return{left:e.left===!1?0:i.left-(e.left===!0?0:e.left),right:e.right===!1?s.width:i.right+(e.right===!0?0:e.right),top:e.top===!1?0:i.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?s.height:i.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class G0{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,n){const r=e.listeners[n],o=e.duration;r.forEach(a=>a({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=bg.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,n)=>{if(!i.running||!i.items.length)return;const r=i.items;let o=r.length-1,a=!1,l;for(;o>=0;--o)l=r[o],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(r[o]=r[r.length-1],r.pop());a&&(n.draw(),this._notify(n,i,t,"progress")),r.length||(i.running=!1,this._notify(n,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,n)=>Math.max(i,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let n=i.length-1;for(;n>=0;--n)i[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var tn=new G0;const Pu="transparent",q0={boolean(s,t,e){return e>.5?t:s},color(s,t,e){const i=mu(s||Pu),n=i.valid&&mu(t||Pu);return n&&n.valid?n.mix(i,e).hexString():t},number(s,t,e){return s+(t-s)*e}};class K0{constructor(t,e,i,n){const r=e[i];n=Er([t.to,n,r,t.from]);const o=Er([t.from,r,n]);this._active=!0,this._fn=t.fn||q0[t.type||typeof o],this._easing=Zr[t.easing]||Zr.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const n=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=Er([t.to,e,n,t.from]),this._from=Er([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,n=this._prop,r=this._from,o=this._loop,a=this._to;let l;if(this._active=r!==a&&(o||e<i),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=r;return}l=e/i%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(r,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let n=0;n<i.length;n++)i[n][e]()}}class zg{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!_t(t))return;const e=Object.keys(Nt.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const r=t[n];if(!_t(r))return;const o={};for(const a of e)o[a]=r[a];(Bt(r.properties)&&r.properties||[n]).forEach(a=>{(a===n||!i.has(a))&&i.set(a,o)})})}_animateOptions(t,e){const i=e.options,n=Z0(t,i);if(!n)return[];const r=this._createAnimations(n,i);return i.$shared&&Q0(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){const i=this._properties,n=[],r=t.$animations||(t.$animations={}),o=Object.keys(e),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const c=o[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let f=r[c];const d=i.get(c);if(f)if(d&&f.active()){f.update(d,h,a);continue}else f.cancel();if(!d||!d.duration){t[c]=h;continue}r[c]=f=new K0(d,t,c,h),n.push(f)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const i=this._createAnimations(t,e);if(i.length)return tn.add(this._chart,i),!0}}function Q0(s,t){const e=[],i=Object.keys(t);for(let n=0;n<i.length;n++){const r=s[i[n]];r&&r.active()&&e.push(r.wait())}return Promise.all(e)}function Z0(s,t){if(!t)return;let e=s.options;if(!e){s.options=t;return}return e.$shared&&(s.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Cu(s,t){const e=s&&s.options||{},i=e.reverse,n=e.min===void 0?t:0,r=e.max===void 0?t:0;return{start:i?r:n,end:i?n:r}}function J0(s,t,e){if(e===!1)return!1;const i=Cu(s,e),n=Cu(t,e);return{top:n.end,right:i.end,bottom:n.start,left:i.start}}function tb(s){let t,e,i,n;return _t(s)?(t=s.top,e=s.right,i=s.bottom,n=s.left):t=e=i=n=s,{top:t,right:e,bottom:i,left:n,disabled:s===!1}}function Bg(s,t){const e=[],i=s._getSortedDatasetMetas(t);let n,r;for(n=0,r=i.length;n<r;++n)e.push(i[n].index);return e}function Du(s,t,e,i={}){const n=s.keys,r=i.mode==="single";let o,a,l,c;if(t===null)return;let h=!1;for(o=0,a=n.length;o<a;++o){if(l=+n[o],l===e){if(h=!0,i.all)continue;break}c=s.values[l],Gt(c)&&(r||t===0||Ki(t)===Ki(c))&&(t+=c)}return!h&&!i.all?0:t}function eb(s,t){const{iScale:e,vScale:i}=t,n=e.axis==="x"?"x":"y",r=i.axis==="x"?"x":"y",o=Object.keys(s),a=new Array(o.length);let l,c,h;for(l=0,c=o.length;l<c;++l)h=o[l],a[l]={[n]:h,[r]:s[h]};return a}function yl(s,t){const e=s&&s.options.stacked;return e||e===void 0&&t.stack!==void 0}function ib(s,t,e){return`${s.id}.${t.id}.${e.stack||e.type}`}function nb(s){const{min:t,max:e,minDefined:i,maxDefined:n}=s.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function sb(s,t,e){const i=s[t]||(s[t]={});return i[e]||(i[e]={})}function Ou(s,t,e,i){for(const n of t.getMatchingVisibleMetas(i).reverse()){const r=s[n.index];if(e&&r>0||!e&&r<0)return n.index}return null}function Au(s,t){const{chart:e,_cachedMeta:i}=s,n=e._stacks||(e._stacks={}),{iScale:r,vScale:o,index:a}=i,l=r.axis,c=o.axis,h=ib(r,o,i),f=t.length;let d;for(let u=0;u<f;++u){const p=t[u],{[l]:g,[c]:_}=p,m=p._stacks||(p._stacks={});d=m[c]=sb(n,h,g),d[a]=_,d._top=Ou(d,o,!0,i.type),d._bottom=Ou(d,o,!1,i.type);const b=d._visualValues||(d._visualValues={});b[a]=_}}function vl(s,t){const e=s.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function rb(s,t){return Yn(s,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function ob(s,t,e){return Yn(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function pr(s,t){const e=s.controller.index,i=s.vScale&&s.vScale.axis;if(i){t=t||s._parsed;for(const n of t){const r=n._stacks;if(!r||r[i]===void 0||r[i][e]===void 0)return;delete r[i][e],r[i]._visualValues!==void 0&&r[i]._visualValues[e]!==void 0&&delete r[i]._visualValues[e]}}}const wl=s=>s==="reset"||s==="none",Eu=(s,t)=>t?s:Object.assign({},s),ab=(s,t,e)=>s&&!t.hidden&&t._stacked&&{keys:Bg(e,!0),values:null};class $n{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=yl(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&pr(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),n=(f,d,u,p)=>f==="x"?d:f==="r"?p:u,r=e.xAxisID=nt(i.xAxisID,vl(t,"x")),o=e.yAxisID=nt(i.yAxisID,vl(t,"y")),a=e.rAxisID=nt(i.rAxisID,vl(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,r,o,a),h=e.vAxisID=n(l,o,r,a);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&gu(this._data,this),t._stacked&&pr(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(_t(e)){const n=this._cachedMeta;this._data=eb(e,n)}else if(i!==e){if(i){gu(i,this);const n=this._cachedMeta;pr(n),n._parsed=[]}e&&Object.isExtensible(e)&&Xm(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let n=!1;this._dataCheck();const r=e._stacked;e._stacked=yl(e.vScale,e),e.stack!==i.stack&&(n=!0,pr(e),e.stack=i.stack),this._resyncElements(t),(n||r!==e._stacked)&&(Au(this,e._parsed),e._stacked=yl(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:n}=this,{iScale:r,_stacked:o}=i,a=r.axis;let l=t===0&&e===n.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,f,d;if(this._parsing===!1)i._parsed=n,i._sorted=!0,d=n;else{Bt(n[t])?d=this.parseArrayData(i,n,t,e):_t(n[t])?d=this.parseObjectData(i,n,t,e):d=this.parsePrimitiveData(i,n,t,e);const u=()=>f[a]===null||c&&f[a]<c[a];for(h=0;h<e;++h)i._parsed[h+t]=f=d[h],l&&(u()&&(l=!1),c=f);i._sorted=l}o&&Au(this,d)}parsePrimitiveData(t,e,i,n){const{iScale:r,vScale:o}=t,a=r.axis,l=o.axis,c=r.getLabels(),h=r===o,f=new Array(n);let d,u,p;for(d=0,u=n;d<u;++d)p=d+i,f[d]={[a]:h||r.parse(c[p],p),[l]:o.parse(e[p],p)};return f}parseArrayData(t,e,i,n){const{xScale:r,yScale:o}=t,a=new Array(n);let l,c,h,f;for(l=0,c=n;l<c;++l)h=l+i,f=e[h],a[l]={x:r.parse(f[0],h),y:o.parse(f[1],h)};return a}parseObjectData(t,e,i,n){const{xScale:r,yScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,f,d,u;for(h=0,f=n;h<f;++h)d=h+i,u=e[d],c[h]={x:r.parse(Wn(u,a),d),y:o.parse(Wn(u,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const n=this.chart,r=this._cachedMeta,o=e[t.axis],a={keys:Bg(n,!0),values:e._stacks[t.axis]._visualValues};return Du(a,o,r.index,{mode:i})}updateRangeFromParsed(t,e,i,n){const r=i[e.axis];let o=r===null?NaN:r;const a=n&&i._stacks[e.axis];n&&a&&(n.values=a,o=Du(n,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,n=i._parsed,r=i._sorted&&t===i.iScale,o=n.length,a=this._getOtherScale(t),l=ab(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:f}=nb(a);let d,u;function p(){u=n[d];const g=u[a.axis];return!Gt(u[t.axis])||h>g||f<g}for(d=0;d<o&&!(!p()&&(this.updateRangeFromParsed(c,t,u,l),r));++d);if(r){for(d=o-1;d>=0;--d)if(!p()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let n,r,o;for(n=0,r=e.length;n<r;++n)o=e[n][t.axis],Gt(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,n=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:n?""+n.getLabelForValue(r[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=tb(nt(this.options.clip,J0(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,n=i.data||[],r=e.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(t,r,a,l),h=a;h<a+l;++h){const f=n[h];f.hidden||(f.active&&c?o.push(f):f.draw(t,r))}for(h=0;h<o.length;++h)o[h].draw(t,r)}getStyle(t,e){const i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const n=this.getDataset();let r;if(t>=0&&t<this._cachedMeta.data.length){const o=this._cachedMeta.data[t];r=o.$context||(o.$context=ob(this.getContext(),t,o)),r.parsed=this.getParsed(t),r.raw=n.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=rb(this.chart.getContext(),this.index)),r.dataset=n,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const n=e==="active",r=this._cachedDataOpts,o=t+"-"+e,a=r[o],l=this.enableOptionSharing&&go(i);if(a)return Eu(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),f=n?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),h),u=Object.keys(Nt.elements[t]),p=()=>this.getContext(i,n,e),g=c.resolveNamedOptions(d,u,p,f);return g.$shared&&(g.$shared=l,r[o]=Object.freeze(Eu(g,l))),g}_resolveAnimations(t,e,i){const n=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,a=r[o];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,f=h.datasetAnimationScopeKeys(this._type,e),d=h.getOptionScopes(this.getDataset(),f);l=h.createResolver(d,this.getContext(t,i,e))}const c=new zg(n,l&&l.animations);return l&&l._cacheable&&(r[o]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||wl(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),n=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(e,r)||r!==n;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:o}}updateElement(t,e,i,n){wl(n)?Object.assign(t,i):this._resolveAnimations(e,n).update(t,i)}updateSharedOptions(t,e,i){t&&!wl(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,n){t.active=n;const r=this.getStyle(e,n);this._resolveAnimations(e,i,n).update(t,{options:!n&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=i.length,r=e.length,o=Math.min(r,n);o&&this.parse(0,o),r>n?this._insertElements(n,r-n,t):r<n&&this._removeElements(r,n-r)}_insertElements(t,e,i=!0){const n=this._cachedMeta,r=n.data,o=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=o;a--)c[a]=c[a-e]};for(l(r),a=t;a<o;++a)r[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,n){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const n=i._parsed.splice(t,e);i._stacked&&pr(i,n)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,n]=t;this[e](i,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function lb(s,t){if(!s._cache.$bar){const e=s.getMatchingVisibleMetas(t);let i=[];for(let n=0,r=e.length;n<r;n++)i=i.concat(e[n].controller.getAllParsedValues(s));s._cache.$bar=mg(i.sort((n,r)=>n-r))}return s._cache.$bar}function cb(s){const t=s.iScale,e=lb(t,s.type);let i=t._length,n,r,o,a;const l=()=>{o===32767||o===-32768||(go(a)&&(i=Math.min(i,Math.abs(o-a)||i)),a=o)};for(n=0,r=e.length;n<r;++n)o=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,r=t.ticks.length;n<r;++n)o=t.getPixelForTick(n),l();return i}function hb(s,t,e,i){const n=e.barThickness;let r,o;return dt(n)?(r=t.min*e.categoryPercentage,o=e.barPercentage):(r=n*i,o=1),{chunk:r/i,ratio:o,start:t.pixels[s]-r/2}}function ub(s,t,e,i){const n=t.pixels,r=n[s];let o=s>0?n[s-1]:null,a=s<n.length-1?n[s+1]:null;const l=e.categoryPercentage;o===null&&(o=r-(a===null?t.end-t.start:a-r)),a===null&&(a=r+r-o);const c=r-(r-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/i,ratio:e.barPercentage,start:c}}function fb(s,t,e,i){const n=e.parse(s[0],i),r=e.parse(s[1],i),o=Math.min(n,r),a=Math.max(n,r);let l=o,c=a;Math.abs(o)>Math.abs(a)&&(l=a,c=o),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:r,min:o,max:a}}function Ng(s,t,e,i){return Bt(s)?fb(s,t,e,i):t[e.axis]=e.parse(s,i),t}function Ru(s,t,e,i){const n=s.iScale,r=s.vScale,o=n.getLabels(),a=n===r,l=[];let c,h,f,d;for(c=e,h=e+i;c<h;++c)d=t[c],f={},f[n.axis]=a||n.parse(o[c],c),l.push(Ng(d,f,r,c));return l}function Sl(s){return s&&s.barStart!==void 0&&s.barEnd!==void 0}function db(s,t,e){return s!==0?Ki(s):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function gb(s){let t,e,i,n,r;return s.horizontal?(t=s.base>s.x,e="left",i="right"):(t=s.base<s.y,e="bottom",i="top"),t?(n="end",r="start"):(n="start",r="end"),{start:e,end:i,reverse:t,top:n,bottom:r}}function pb(s,t,e,i){let n=t.borderSkipped;const r={};if(!n){s.borderSkipped=r;return}if(n===!0){s.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:c,bottom:h}=gb(s);n==="middle"&&e&&(s.enableBorderRadius=!0,(e._top||0)===i?n=c:(e._bottom||0)===i?n=h:(r[Lu(h,o,a,l)]=!0,n=c)),r[Lu(n,o,a,l)]=!0,s.borderSkipped=r}function Lu(s,t,e,i){return i?(s=_b(s,t,e),s=Fu(s,e,t)):s=Fu(s,t,e),s}function _b(s,t,e){return s===t?e:s===e?t:s}function Fu(s,t,e){return s==="start"?t:s==="end"?e:s}function mb(s,{inflateAmount:t},e){s.inflateAmount=t==="auto"?e===1?.33:0:t}class bb extends $n{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,n){return Ru(t,e,i,n)}parseArrayData(t,e,i,n){return Ru(t,e,i,n)}parseObjectData(t,e,i,n){const{iScale:r,vScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=r.axis==="x"?a:l,h=o.axis==="x"?a:l,f=[];let d,u,p,g;for(d=i,u=i+n;d<u;++d)g=e[d],p={},p[r.axis]=r.parse(Wn(g,c),d),f.push(Ng(Wn(g,h),p,o,d));return f}updateRangeFromParsed(t,e,i,n){super.updateRangeFromParsed(t,e,i,n);const r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:i,vScale:n}=e,r=this.getParsed(t),o=r._custom,a=Sl(o)?"["+o.start+", "+o.end+"]":""+n.getLabelForValue(r[n.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,n){const r=n==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:f,includeOptions:d}=this._getSharedOptions(e,n);for(let u=e;u<e+i;u++){const p=this.getParsed(u),g=r||dt(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(u),_=this._calculateBarIndexPixels(u,h),m=(p._stacks||{})[a.axis],b={horizontal:c,base:g.base,enableBorderRadius:!m||Sl(p._custom)||o===m._top||o===m._bottom,x:c?g.head:_.center,y:c?_.center:g.head,height:c?_.size:Math.abs(g.size),width:c?Math.abs(g.size):_.size};d&&(b.options=f||this.resolveDataElementOptions(u,t[u].active?"active":n));const w=b.options||t[u].options;pb(b,w,m,o),mb(b,w,h.ratio),this.updateElement(t[u],u,b,n)}}_getStacks(t,e){const{iScale:i}=this._cachedMeta,n=i.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),r=i.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[i.axis],c=h=>{const f=h._parsed.find(u=>u[i.axis]===l),d=f&&f[h.vScale.axis];if(dt(d)||isNaN(d))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((r===!1||o.indexOf(h.stack)===-1||r===void 0&&h.stack===void 0)&&o.push(h.stack),h.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(i=>t[i].axis===e).shift()}_getAxis(){const t={},e=this.getFirstScaleIdForIndexAxis();for(const i of this.chart.data.datasets)t[nt(this.chart.options.indexAxis==="x"?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){const n=this._getStacks(t,i),r=e!==void 0?n.indexOf(e):-1;return r===-1?n.length-1:r}_getRuler(){const t=this.options,e=this._cachedMeta,i=e.iScale,n=[];let r,o;for(r=0,o=e.data.length;r<o;++r)n.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));const a=t.barThickness;return{min:a||cb(e),pixels:n,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:i,index:n},options:{base:r,minBarLength:o}}=this,a=r||0,l=this.getParsed(t),c=l._custom,h=Sl(c);let f=l[e.axis],d=0,u=i?this.applyStack(e,l,i):f,p,g;u!==f&&(d=u-f,u=f),h&&(f=c.barStart,u=c.barEnd-c.barStart,f!==0&&Ki(f)!==Ki(c.barEnd)&&(d=0),d+=f);const _=!dt(r)&&!h?r:d;let m=e.getPixelForValue(_);if(this.chart.getDataVisibility(t)?p=e.getPixelForValue(d+u):p=m,g=p-m,Math.abs(g)<o){g=db(g,e,a)*o,f===a&&(m-=g/2);const b=e.getPixelForDecimal(0),w=e.getPixelForDecimal(1),y=Math.min(b,w),x=Math.max(b,w);m=Math.max(Math.min(m,x),y),p=m+g,i&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(p)-e.getValueForPixel(m))}if(m===e.getPixelForValue(a)){const b=Ki(g)*e.getLineWidthForValue(a)/2;m+=b,g-=b}return{size:g,base:m,head:p,center:p+g/2}}_calculateBarIndexPixels(t,e){const i=e.scale,n=this.options,r=n.skipNull,o=nt(n.maxBarThickness,1/0);let a,l;const c=this._getAxisCount();if(e.grouped){const h=r?this._getStackCount(t):e.stackCount,f=n.barThickness==="flex"?ub(t,e,n,h*c):hb(t,e,n,h*c),d=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,u=this._getAxis().indexOf(nt(d,this.getFirstScaleIdForIndexAxis())),p=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0)+u;a=f.start+f.chunk*p+f.chunk/2,l=Math.min(o,f.chunk*f.ratio)}else a=i.getPixelForValue(this.getParsed(t)[i.axis],t),l=Math.min(o,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,i=t.data,n=i.length;let r=0;for(;r<n;++r)this.getParsed(r)[e.axis]!==null&&!i[r].hidden&&i[r].draw(this._ctx)}}class xb extends $n{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,n){const r=super.parsePrimitiveData(t,e,i,n);for(let o=0;o<r.length;o++)r[o]._custom=this.resolveDataElementOptions(o+i).radius;return r}parseArrayData(t,e,i,n){const r=super.parseArrayData(t,e,i,n);for(let o=0;o<r.length;o++){const a=e[i+o];r[o]._custom=nt(a[2],this.resolveDataElementOptions(o+i).radius)}return r}parseObjectData(t,e,i,n){const r=super.parseObjectData(t,e,i,n);for(let o=0;o<r.length;o++){const a=e[i+o];r[o]._custom=nt(a&&a.r&&+a.r,this.resolveDataElementOptions(o+i).radius)}return r}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:r}=e,o=this.getParsed(t),a=n.getLabelForValue(o.x),l=r.getLabelForValue(o.y),c=o._custom;return{label:i[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,n){const r=n==="reset",{iScale:o,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=o.axis,f=a.axis;for(let d=e;d<e+i;d++){const u=t[d],p=!r&&this.getParsed(d),g={},_=g[h]=r?o.getPixelForDecimal(.5):o.getPixelForValue(p[h]),m=g[f]=r?a.getBasePixel():a.getPixelForValue(p[f]);g.skip=isNaN(_)||isNaN(m),c&&(g.options=l||this.resolveDataElementOptions(d,u.active?"active":n),r&&(g.options.radius=0)),this.updateElement(u,d,g,n)}}resolveDataElementOptions(t,e){const i=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const r=n.radius;return e!=="active"&&(n.radius=0),n.radius+=nt(i&&i._custom,r),n}}function yb(s,t,e){let i=1,n=1,r=0,o=0;if(t<Ft){const a=s,l=a+t,c=Math.cos(a),h=Math.sin(a),f=Math.cos(l),d=Math.sin(l),u=(w,y,x)=>po(w,a,l,!0)?1:Math.max(y,y*e,x,x*e),p=(w,y,x)=>po(w,a,l,!0)?-1:Math.min(y,y*e,x,x*e),g=u(0,c,f),_=u(Jt,h,d),m=p(wt,c,f),b=p(wt+Jt,h,d);i=(g-m)/2,n=(_-b)/2,r=-(g+m)/2,o=-(_+b)/2}return{ratioX:i,ratioY:n,offsetX:r,offsetY:o}}class lh extends $n{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((r,o)=>{const l=t.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(o),index:o}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=i;else{let r=l=>+i[l];if(_t(i[t])){const{key:l="value"}=this._parsing;r=c=>+Wn(i[c],l)}let o,a;for(o=t,a=t+e;o<a;++o)n._parsed[o]=r(o)}}_getRotation(){return Fi(this.options.rotation-90)}_getCircumference(){return Fi(this.options.circumference)}_getRotationExtents(){let t=Ft,e=-Ft;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const n=this.chart.getDatasetMeta(i).controller,r=n._getRotation(),o=n._getCircumference();t=Math.min(t,r),e=Math.max(e,r+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,n=this._cachedMeta,r=n.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-o)/2,0),l=Math.min(Om(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:f}=this._getRotationExtents(),{ratioX:d,ratioY:u,offsetX:p,offsetY:g}=yb(f,h,l),_=(i.width-o)/d,m=(i.height-o)/u,b=Math.max(Math.min(_,m)/2,0),w=fg(this.options.radius,b),y=Math.max(w*l,0),x=(w-y)/this._getVisibleDatasetWeightTotal();this.offsetX=p*w,this.offsetY=g*w,n.total=this.calculateTotal(),this.outerRadius=w-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(r,0,r.length,t)}_circumference(t,e){const i=this.options,n=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*r/Ft)}updateElements(t,e,i,n){const r=n==="reset",o=this.chart,a=o.chartArea,c=o.options.animation,h=(a.left+a.right)/2,f=(a.top+a.bottom)/2,d=r&&c.animateScale,u=d?0:this.innerRadius,p=d?0:this.outerRadius,{sharedOptions:g,includeOptions:_}=this._getSharedOptions(e,n);let m=this._getRotation(),b;for(b=0;b<e;++b)m+=this._circumference(b,r);for(b=e;b<e+i;++b){const w=this._circumference(b,r),y=t[b],x={x:h+this.offsetX,y:f+this.offsetY,startAngle:m,endAngle:m+w,circumference:w,outerRadius:p,innerRadius:u};_&&(x.options=g||this.resolveDataElementOptions(b,y.active?"active":n)),m+=w,this.updateElement(y,b,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i=0,n;for(n=0;n<e.length;n++){const r=t._parsed[n];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(i+=Math.abs(r))}return i}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?Ft*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],r=vo(e._parsed[t],i.options.locale);return{label:n[t]||"",value:r}}getMaxBorderWidth(t){let e=0;const i=this.chart;let n,r,o,a,l;if(!t){for(n=0,r=i.data.datasets.length;n<r;++n)if(i.isDatasetVisible(n)){o=i.getDatasetMeta(n),t=o.data,a=o.controller;break}}if(!t)return 0;for(n=0,r=t.length;n<r;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,n=t.length;i<n;++i){const r=this.resolveDataElementOptions(i);e=Math.max(e,r.offset||0,r.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(nt(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class vb extends $n{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:n=[],_dataset:r}=e,o=this.chart._animationsDisabled;let{start:a,count:l}=yg(e,n,o);this._drawStart=a,this._drawCount=l,vg(e)&&(a=0,l=n.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,i,n){const r=n==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:f}=this._getSharedOptions(e,n),d=o.axis,u=a.axis,{spanGaps:p,segment:g}=this.options,_=rr(p)?p:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||n==="none",b=e+i,w=t.length;let y=e>0&&this.getParsed(e-1);for(let x=0;x<w;++x){const k=t[x],v=m?k:{};if(x<e||x>=b){v.skip=!0;continue}const M=this.getParsed(x),C=dt(M[u]),L=v[d]=o.getPixelForValue(M[d],x),D=v[u]=r||C?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,M,l):M[u],x);v.skip=isNaN(L)||isNaN(D)||C,v.stop=x>0&&Math.abs(M[d]-y[d])>_,g&&(v.parsed=M,v.raw=c.data[x]),f&&(v.options=h||this.resolveDataElementOptions(x,k.active?"active":n)),m||this.updateElement(k,x,v,n),y=M}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return i;const r=n[0].size(this.resolveDataElementOptions(0)),o=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,r,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class Wg extends $n{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((r,o)=>{const l=t.getDatasetMeta(0).controller.getStyle(o);return{text:r,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(o),index:o}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],r=vo(e._parsed[t].r,i.options.locale);return{label:n[t]||"",value:r}}parseObjectData(t,e,i,n){return Dg.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,n)=>{const r=this.getParsed(n).r;!isNaN(r)&&this.chart.getDataVisibility(n)&&(r<e.min&&(e.min=r),r>e.max&&(e.max=r))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,i=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),r=Math.max(n/2,0),o=Math.max(i.cutoutPercentage?r/100*i.cutoutPercentage:1,0),a=(r-o)/t.getVisibleDatasetCount();this.outerRadius=r-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,n){const r=n==="reset",o=this.chart,l=o.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,f=c.yCenter,d=c.getIndexAngle(0)-.5*wt;let u=d,p;const g=360/this.countVisibleElements();for(p=0;p<e;++p)u+=this._computeAngle(p,n,g);for(p=e;p<e+i;p++){const _=t[p];let m=u,b=u+this._computeAngle(p,n,g),w=o.getDataVisibility(p)?c.getDistanceFromCenterForValue(this.getParsed(p).r):0;u=b,r&&(l.animateScale&&(w=0),l.animateRotate&&(m=b=d));const y={x:h,y:f,innerRadius:0,outerRadius:w,startAngle:m,endAngle:b,options:this.resolveDataElementOptions(p,_.active?"active":n)};this.updateElement(_,p,y,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((i,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?Fi(this.resolveDataElementOptions(t,e).angle||i):0}}class wb extends lh{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class Sb extends $n{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,n){return Dg.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta,i=e.dataset,n=e.data||[],r=e.iScale.getLabels();if(i.points=n,t!=="resize"){const o=this.resolveDatasetElementOptions(t);this.options.showLine||(o.borderWidth=0);const a={_loop:!0,_fullLoop:r.length===n.length,options:o};this.updateElement(i,void 0,a,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,i,n){const r=this._cachedMeta.rScale,o=n==="reset";for(let a=e;a<e+i;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":n),h=r.getPointPositionForValue(a,this.getParsed(a).r),f=o?r.xCenter:h.x,d=o?r.yCenter:h.y,u={x:f,y:d,angle:h.angle,skip:isNaN(f)||isNaN(d),options:c};this.updateElement(l,a,u,n)}}}class Mb extends $n{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:r}=e,o=this.getParsed(t),a=n.getLabelForValue(o.x),l=r.getLabelForValue(o.y);return{label:i[t]||"",value:"("+a+", "+l+")"}}update(t){const e=this._cachedMeta,{data:i=[]}=e,n=this.chart._animationsDisabled;let{start:r,count:o}=yg(e,i,n);if(this._drawStart=r,this._drawCount=o,vg(e)&&(r=0,o=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,r,o,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,n){const r=n==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),f=this.getSharedOptions(h),d=this.includeOptions(n,f),u=o.axis,p=a.axis,{spanGaps:g,segment:_}=this.options,m=rr(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||r||n==="none";let w=e>0&&this.getParsed(e-1);for(let y=e;y<e+i;++y){const x=t[y],k=this.getParsed(y),v=b?x:{},M=dt(k[p]),C=v[u]=o.getPixelForValue(k[u],y),L=v[p]=r||M?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,k,l):k[p],y);v.skip=isNaN(C)||isNaN(L)||M,v.stop=y>0&&Math.abs(k[u]-w[u])>m,_&&(v.parsed=k,v.raw=c.data[y]),d&&(v.options=f||this.resolveDataElementOptions(y,x.active?"active":n)),b||this.updateElement(x,y,v,n),w=k}this.updateSharedOptions(f,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const i=t.dataset,n=i.options&&i.options.borderWidth||0;if(!e.length)return n;const r=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,r,o)/2}}var kb=Object.freeze({__proto__:null,BarController:bb,BubbleController:xb,DoughnutController:lh,LineController:vb,PieController:wb,PolarAreaController:Wg,RadarController:Sb,ScatterController:Mb});function Zn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ch{static override(t){Object.assign(ch.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return Zn()}parse(){return Zn()}format(){return Zn()}add(){return Zn()}diff(){return Zn()}startOf(){return Zn()}endOf(){return Zn()}}var Tb={_date:ch};function Pb(s,t,e,i){const{controller:n,data:r,_sorted:o}=s,a=n._cachedMeta.iScale,l=s.dataset&&s.dataset.options?s.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&o&&r.length){const c=a._reversePixels?Ym:fn;if(i){if(n._sharedOptions){const h=r[0],f=typeof h.getRange=="function"&&h.getRange(t);if(f){const d=c(r,t,e-f),u=c(r,t,e+f);return{lo:d.lo,hi:u.hi}}}}else{const h=c(r,t,e);if(l){const{vScale:f}=n._cachedMeta,{_parsed:d}=s,u=d.slice(0,h.lo+1).reverse().findIndex(g=>!dt(g[f.axis]));h.lo-=Math.max(0,u);const p=d.slice(h.hi).findIndex(g=>!dt(g[f.axis]));h.hi+=Math.max(0,p)}return h}}return{lo:0,hi:r.length-1}}function $a(s,t,e,i,n){const r=s.getSortedVisibleDatasetMetas(),o=e[t];for(let a=0,l=r.length;a<l;++a){const{index:c,data:h}=r[a],{lo:f,hi:d}=Pb(r[a],t,o,n);for(let u=f;u<=d;++u){const p=h[u];p.skip||i(p,c,u)}}}function Cb(s){const t=s.indexOf("x")!==-1,e=s.indexOf("y")!==-1;return function(i,n){const r=t?Math.abs(i.x-n.x):0,o=e?Math.abs(i.y-n.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function Ml(s,t,e,i,n){const r=[];return!n&&!s.isPointInArea(t)||$a(s,e,t,function(a,l,c){!n&&!dn(a,s.chartArea,0)||a.inRange(t.x,t.y,i)&&r.push({element:a,datasetIndex:l,index:c})},!0),r}function Db(s,t,e,i){let n=[];function r(o,a,l){const{startAngle:c,endAngle:h}=o.getProps(["startAngle","endAngle"],i),{angle:f}=pg(o,{x:t.x,y:t.y});po(f,c,h)&&n.push({element:o,datasetIndex:a,index:l})}return $a(s,e,t,r),n}function Ob(s,t,e,i,n,r){let o=[];const a=Cb(e);let l=Number.POSITIVE_INFINITY;function c(h,f,d){const u=h.inRange(t.x,t.y,n);if(i&&!u)return;const p=h.getCenterPoint(n);if(!(!!r||s.isPointInArea(p))&&!u)return;const _=a(t,p);_<l?(o=[{element:h,datasetIndex:f,index:d}],l=_):_===l&&o.push({element:h,datasetIndex:f,index:d})}return $a(s,e,t,c),o}function kl(s,t,e,i,n,r){return!r&&!s.isPointInArea(t)?[]:e==="r"&&!i?Db(s,t,e,n):Ob(s,t,e,i,n,r)}function Iu(s,t,e,i,n){const r=[],o=e==="x"?"inXRange":"inYRange";let a=!1;return $a(s,e,t,(l,c,h)=>{l[o]&&l[o](t[e],n)&&(r.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),i&&!a?[]:r}var Ab={modes:{index(s,t,e,i){const n=ns(t,s),r=e.axis||"x",o=e.includeInvisible||!1,a=e.intersect?Ml(s,n,r,i,o):kl(s,n,r,!1,i,o),l=[];return a.length?(s.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,f=c.data[h];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:h})}),l):[]},dataset(s,t,e,i){const n=ns(t,s),r=e.axis||"xy",o=e.includeInvisible||!1;let a=e.intersect?Ml(s,n,r,i,o):kl(s,n,r,!1,i,o);if(a.length>0){const l=a[0].datasetIndex,c=s.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(s,t,e,i){const n=ns(t,s),r=e.axis||"xy",o=e.includeInvisible||!1;return Ml(s,n,r,i,o)},nearest(s,t,e,i){const n=ns(t,s),r=e.axis||"xy",o=e.includeInvisible||!1;return kl(s,n,r,e.intersect,i,o)},x(s,t,e,i){const n=ns(t,s);return Iu(s,n,"x",e.intersect,i)},y(s,t,e,i){const n=ns(t,s);return Iu(s,n,"y",e.intersect,i)}}};const Vg=["left","top","right","bottom"];function _r(s,t){return s.filter(e=>e.pos===t)}function zu(s,t){return s.filter(e=>Vg.indexOf(e.pos)===-1&&e.box.axis===t)}function mr(s,t){return s.sort((e,i)=>{const n=t?i:e,r=t?e:i;return n.weight===r.weight?n.index-r.index:n.weight-r.weight})}function Eb(s){const t=[];let e,i,n,r,o,a;for(e=0,i=(s||[]).length;e<i;++e)n=s[e],{position:r,options:{stack:o,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:r,horizontal:n.isHorizontal(),weight:n.weight,stack:o&&r+o,stackWeight:a});return t}function Rb(s){const t={};for(const e of s){const{stack:i,pos:n,stackWeight:r}=e;if(!i||!Vg.includes(n))continue;const o=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return t}function Lb(s,t){const e=Rb(s),{vBoxMaxWidth:i,hBoxMaxHeight:n}=t;let r,o,a;for(r=0,o=s.length;r<o;++r){a=s[r];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&t.availableWidth,a.height=n):(a.width=i,a.height=h?h*n:l&&t.availableHeight)}return e}function Fb(s){const t=Eb(s),e=mr(t.filter(c=>c.box.fullSize),!0),i=mr(_r(t,"left"),!0),n=mr(_r(t,"right")),r=mr(_r(t,"top"),!0),o=mr(_r(t,"bottom")),a=zu(t,"x"),l=zu(t,"y");return{fullSize:e,leftAndTop:i.concat(r),rightAndBottom:n.concat(l).concat(o).concat(a),chartArea:_r(t,"chartArea"),vertical:i.concat(n).concat(l),horizontal:r.concat(o).concat(a)}}function Bu(s,t,e,i){return Math.max(s[e],t[e])+Math.max(s[i],t[i])}function Hg(s,t){s.top=Math.max(s.top,t.top),s.left=Math.max(s.left,t.left),s.bottom=Math.max(s.bottom,t.bottom),s.right=Math.max(s.right,t.right)}function Ib(s,t,e,i){const{pos:n,box:r}=e,o=s.maxPadding;if(!_t(n)){e.size&&(s[n]-=e.size);const f=i[e.stack]||{size:0,count:1};f.size=Math.max(f.size,e.horizontal?r.height:r.width),e.size=f.size/f.count,s[n]+=e.size}r.getPadding&&Hg(o,r.getPadding());const a=Math.max(0,t.outerWidth-Bu(o,s,"left","right")),l=Math.max(0,t.outerHeight-Bu(o,s,"top","bottom")),c=a!==s.w,h=l!==s.h;return s.w=a,s.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function zb(s){const t=s.maxPadding;function e(i){const n=Math.max(t[i]-s[i],0);return s[i]+=n,n}s.y+=e("top"),s.x+=e("left"),e("right"),e("bottom")}function Bb(s,t){const e=t.maxPadding;function i(n){const r={left:0,top:0,right:0,bottom:0};return n.forEach(o=>{r[o]=Math.max(t[o],e[o])}),r}return i(s?["left","right"]:["top","bottom"])}function Rr(s,t,e,i){const n=[];let r,o,a,l,c,h;for(r=0,o=s.length,c=0;r<o;++r){a=s[r],l=a.box,l.update(a.width||t.w,a.height||t.h,Bb(a.horizontal,t));const{same:f,other:d}=Ib(t,e,a,i);c|=f&&n.length,h=h||d,l.fullSize||n.push(a)}return c&&Rr(n,t,e,i)||h}function jo(s,t,e,i,n){s.top=e,s.left=t,s.right=t+i,s.bottom=e+n,s.width=i,s.height=n}function Nu(s,t,e,i){const n=e.padding;let{x:r,y:o}=t;for(const a of s){const l=a.box,c=i[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*h,d=c.size||l.height;go(c.start)&&(o=c.start),l.fullSize?jo(l,n.left,o,e.outerWidth-n.right-n.left,d):jo(l,t.left+c.placed,o,f,d),c.start=o,c.placed+=f,o=l.bottom}else{const f=t.h*h,d=c.size||l.width;go(c.start)&&(r=c.start),l.fullSize?jo(l,r,n.top,d,e.outerHeight-n.bottom-n.top):jo(l,r,t.top+c.placed,d,f),c.start=r,c.placed+=f,r=l.right}}t.x=r,t.y=o}var Re={addBox(s,t){s.boxes||(s.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},s.boxes.push(t)},removeBox(s,t){const e=s.boxes?s.boxes.indexOf(t):-1;e!==-1&&s.boxes.splice(e,1)},configure(s,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(s,t,e,i){if(!s)return;const n=Fe(s.options.layout.padding),r=Math.max(t-n.width,0),o=Math.max(e-n.height,0),a=Fb(s.boxes),l=a.vertical,c=a.horizontal;Tt(s.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=l.reduce((g,_)=>_.box.options&&_.box.options.display===!1?g:g+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/h,hBoxMaxHeight:o/2}),d=Object.assign({},n);Hg(d,Fe(i));const u=Object.assign({maxPadding:d,w:r,h:o,x:n.left,y:n.top},n),p=Lb(l.concat(c),f);Rr(a.fullSize,u,f,p),Rr(l,u,f,p),Rr(c,u,f,p)&&Rr(l,u,f,p),zb(u),Nu(a.leftAndTop,u,f,p),u.x+=u.w,u.y+=u.h,Nu(a.rightAndBottom,u,f,p),s.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},Tt(a.chartArea,g=>{const _=g.box;Object.assign(_,s.chartArea),_.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class Yg{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,n){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):i)}}isAttached(t){return!0}updateConfig(t){}}class Nb extends Yg{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ua="$chartjs",Wb={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Wu=s=>s===null||s==="";function Vb(s,t){const e=s.style,i=s.getAttribute("height"),n=s.getAttribute("width");if(s[ua]={initial:{height:i,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Wu(n)){const r=Su(s,"width");r!==void 0&&(s.width=r)}if(Wu(i))if(s.style.height==="")s.height=s.width/(t||2);else{const r=Su(s,"height");r!==void 0&&(s.height=r)}return s}const $g=I0?{passive:!0}:!1;function Hb(s,t,e){s&&s.addEventListener(t,e,$g)}function Yb(s,t,e){s&&s.canvas&&s.canvas.removeEventListener(t,e,$g)}function $b(s,t){const e=Wb[s.type]||s.type,{x:i,y:n}=ns(s,t);return{type:e,chart:t,native:s,x:i!==void 0?i:null,y:n!==void 0?n:null}}function Ea(s,t){for(const e of s)if(e===t||e.contains(t))return!0}function Xb(s,t,e){const i=s.canvas,n=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||Ea(a.addedNodes,i),o=o&&!Ea(a.removedNodes,i);o&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function jb(s,t,e){const i=s.canvas,n=new MutationObserver(r=>{let o=!1;for(const a of r)o=o||Ea(a.removedNodes,i),o=o&&!Ea(a.addedNodes,i);o&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const mo=new Map;let Vu=0;function Xg(){const s=window.devicePixelRatio;s!==Vu&&(Vu=s,mo.forEach((t,e)=>{e.currentDevicePixelRatio!==s&&t()}))}function Ub(s,t){mo.size||window.addEventListener("resize",Xg),mo.set(s,t)}function Gb(s){mo.delete(s),mo.size||window.removeEventListener("resize",Xg)}function qb(s,t,e){const i=s.canvas,n=i&&ah(i);if(!n)return;const r=xg((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),o=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||r(c,h)});return o.observe(n),Ub(s,r),o}function Tl(s,t,e){e&&e.disconnect(),t==="resize"&&Gb(s)}function Kb(s,t,e){const i=s.canvas,n=xg(r=>{s.ctx!==null&&e($b(r,s))},s);return Hb(i,t,n),n}class Qb extends Yg{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(Vb(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[ua])return!1;const i=e[ua].initial;["height","width"].forEach(r=>{const o=i[r];dt(o)?e.removeAttribute(r):e.setAttribute(r,o)});const n=i.style||{};return Object.keys(n).forEach(r=>{e.style[r]=n[r]}),e.width=e.width,delete e[ua],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),o={attach:Xb,detach:jb,resize:qb}[e]||Kb;n[e]=o(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),n=i[e];if(!n)return;({attach:Tl,detach:Tl,resize:Tl}[e]||Yb)(t,e,n),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,n){return F0(t,e,i,n)}isAttached(t){const e=t&&ah(t);return!!(e&&e.isConnected)}}function Zb(s){return!oh()||typeof OffscreenCanvas<"u"&&s instanceof OffscreenCanvas?Nb:Qb}let mn=class{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return rr(this.x)&&rr(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const n={};return t.forEach(r=>{n[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),n}};function Jb(s,t){const e=s.options.ticks,i=tx(s),n=Math.min(e.maxTicksLimit||i,i),r=e.major.enabled?ix(t):[],o=r.length,a=r[0],l=r[o-1],c=[];if(o>n)return nx(t,c,r,o/n),c;const h=ex(r,t,n);if(o>0){let f,d;const u=o>1?Math.round((l-a)/(o-1)):null;for(Uo(t,c,h,dt(u)?0:a-u,a),f=0,d=o-1;f<d;f++)Uo(t,c,h,r[f],r[f+1]);return Uo(t,c,h,l,dt(u)?t.length:l+u),c}return Uo(t,c,h),c}function tx(s){const t=s.options.offset,e=s._tickSize(),i=s._length/e+(t?0:1),n=s._maxLength/e;return Math.floor(Math.min(i,n))}function ex(s,t,e){const i=sx(s),n=t.length/e;if(!i)return Math.max(n,1);const r=Bm(i);for(let o=0,a=r.length-1;o<a;o++){const l=r[o];if(l>n)return l}return Math.max(n,1)}function ix(s){const t=[];let e,i;for(e=0,i=s.length;e<i;e++)s[e].major&&t.push(e);return t}function nx(s,t,e,i){let n=0,r=e[0],o;for(i=Math.ceil(i),o=0;o<s.length;o++)o===r&&(t.push(s[o]),n++,r=e[n*i])}function Uo(s,t,e,i,n){const r=nt(i,0),o=Math.min(nt(n,s.length),s.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-i,e=l/Math.floor(l/e)),h=r;h<0;)a++,h=Math.round(r+a*e);for(c=Math.max(r,0);c<o;c++)c===h&&(t.push(s[c]),a++,h=Math.round(r+a*e))}function sx(s){const t=s.length;let e,i;if(t<2)return!1;for(i=s[0],e=1;e<t;++e)if(s[e]-s[e-1]!==i)return!1;return i}const rx=s=>s==="left"?"right":s==="right"?"left":s,Hu=(s,t,e)=>t==="top"||t==="left"?s[t]+e:s[t]-e,Yu=(s,t)=>Math.min(t||s,s);function $u(s,t){const e=[],i=s.length/t,n=s.length;let r=0;for(;r<n;r+=i)e.push(s[Math.floor(r)]);return e}function ox(s,t,e){const i=s.ticks.length,n=Math.min(t,i-1),r=s._startPixel,o=s._endPixel,a=1e-6;let l=s.getPixelForTick(n),c;if(!(e&&(i===1?c=Math.max(l-r,o-l):t===0?c=(s.getPixelForTick(1)-l)/2:c=(l-s.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<r-a||l>o+a)))return l}function ax(s,t){Tt(s,e=>{const i=e.gc,n=i.length/2;let r;if(n>t){for(r=0;r<n;++r)delete e.data[i[r]];i.splice(0,n)}})}function br(s){return s.drawTicks?s.tickLength:0}function Xu(s,t){if(!s.display)return 0;const e=he(s.font,t),i=Fe(s.padding);return(Bt(s.text)?s.text.length:1)*e.lineHeight+i.height}function lx(s,t){return Yn(s,{scale:t,type:"scale"})}function cx(s,t,e){return Yn(s,{tick:e,index:t,type:"tick"})}function hx(s,t,e){let i=th(s);return(e&&t!=="right"||!e&&t==="right")&&(i=rx(i)),i}function ux(s,t,e,i){const{top:n,left:r,bottom:o,right:a,chart:l}=s,{chartArea:c,scales:h}=l;let f=0,d,u,p;const g=o-n,_=a-r;if(s.isHorizontal()){if(u=Pe(i,r,a),_t(e)){const m=Object.keys(e)[0],b=e[m];p=h[m].getPixelForValue(b)+g-t}else e==="center"?p=(c.bottom+c.top)/2+g-t:p=Hu(s,e,t);d=a-r}else{if(_t(e)){const m=Object.keys(e)[0],b=e[m];u=h[m].getPixelForValue(b)-_+t}else e==="center"?u=(c.left+c.right)/2-_+t:u=Hu(s,e,t);p=Pe(i,o,n),f=e==="left"?-Jt:Jt}return{titleX:u,titleY:p,maxWidth:d,rotation:f}}class Ps extends mn{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:n}=this;return t=si(t,Number.POSITIVE_INFINITY),e=si(e,Number.NEGATIVE_INFINITY),i=si(i,Number.POSITIVE_INFINITY),n=si(n,Number.NEGATIVE_INFINITY),{min:si(t,i),max:si(e,n),minDefined:Gt(t),maxDefined:Gt(e)}}getMinMax(t){let{min:e,max:i,minDefined:n,maxDefined:r}=this.getUserBounds(),o;if(n&&r)return{min:e,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)o=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,o.min)),r||(i=Math.max(i,o.max));return e=r&&e>i?i:e,i=n&&e>i?e:i,{min:si(e,si(i,e)),max:si(i,si(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){Et(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:n,grace:r,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=d0(this,r,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?$u(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=Jb(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){Et(this.options.afterUpdate,[this])}beforeSetDimensions(){Et(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){Et(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),Et(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){Et(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,n,r;for(i=0,n=t.length;i<n;i++)r=t[i],r.label=Et(e.callback,[r.value,i,t],this)}afterTickToLabelConversion(){Et(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){Et(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=Yu(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,r=e.maxRotation;let o=n,a,l,c;if(!this._isVisible()||!e.display||n>=r||i<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),f=h.widest.width,d=h.highest.height,u=ge(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/i:u/(i-1),f+6>a&&(a=u/(i-(t.offset?.5:1)),l=this.maxHeight-br(t.grid)-e.padding-Xu(t.title,this.chart.options.font),c=Math.sqrt(f*f+d*d),o=Zc(Math.min(Math.asin(ge((h.highest.height+6)/a,-1,1)),Math.asin(ge(l/c,-1,1))-Math.asin(ge(d/c,-1,1)))),o=Math.max(n,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){Et(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){Et(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:n,grid:r}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=Xu(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=br(r)+l):(t.height=this.maxHeight,t.width=br(r)+l),i.display&&this.ticks.length){const{first:c,last:h,widest:f,highest:d}=this._getLabelSizes(),u=i.padding*2,p=Fi(this.labelRotation),g=Math.cos(p),_=Math.sin(p);if(a){const m=i.mirror?0:_*f.width+g*d.height;t.height=Math.min(this.maxHeight,t.height+m+u)}else{const m=i.mirror?0:g*f.width+_*d.height;t.width=Math.min(this.maxWidth,t.width+m+u)}this._calculatePadding(c,h,_,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,n){const{ticks:{align:r,padding:o},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,u=0;l?c?(d=n*t.width,u=i*e.height):(d=i*t.height,u=n*e.width):r==="start"?u=e.width:r==="end"?d=t.width:r!=="inner"&&(d=t.width/2,u=e.width/2),this.paddingLeft=Math.max((d-h+o)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-f+o)*this.width/(this.width-f),0)}else{let h=e.height/2,f=t.height/2;r==="start"?(h=0,f=t.height):r==="end"&&(h=e.height,f=0),this.paddingTop=h+o,this.paddingBottom=f+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){Et(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)dt(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=$u(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:n,_longestTextCache:r}=this,o=[],a=[],l=Math.floor(e/Yu(e,i));let c=0,h=0,f,d,u,p,g,_,m,b,w,y,x;for(f=0;f<e;f+=l){if(p=t[f].label,g=this._resolveTickFontOptions(f),n.font=_=g.string,m=r[_]=r[_]||{data:{},gc:[]},b=g.lineHeight,w=y=0,!dt(p)&&!Bt(p))w=Oa(n,m.data,m.gc,w,p),y=b;else if(Bt(p))for(d=0,u=p.length;d<u;++d)x=p[d],!dt(x)&&!Bt(x)&&(w=Oa(n,m.data,m.gc,w,x),y+=b);o.push(w),a.push(y),c=Math.max(w,c),h=Math.max(y,h)}ax(r,e);const k=o.indexOf(c),v=a.indexOf(h),M=C=>({width:o[C]||0,height:a[C]||0});return{first:M(0),last:M(e-1),widest:M(k),highest:M(v),widths:o,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Hm(this._alignToPixels?Qn(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=cx(this.getContext(),t,i))}return this.$context||(this.$context=lx(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=Fi(this.labelRotation),i=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),r=this._getLabelSizes(),o=t.autoSkipPadding||0,a=r?r.widest.width+o:0,l=r?r.highest.height+o:0;return this.isHorizontal()?l*i>a*n?a/i:l/n:l*n<a*i?l/i:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,n=this.options,{grid:r,position:o,border:a}=n,l=r.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),d=br(r),u=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,_=g/2,m=function(V){return Qn(i,V,g)};let b,w,y,x,k,v,M,C,L,D,O,F;if(o==="top")b=m(this.bottom),v=this.bottom-d,C=b-_,D=m(t.top)+_,F=t.bottom;else if(o==="bottom")b=m(this.top),D=t.top,F=m(t.bottom)-_,v=b+_,C=this.top+d;else if(o==="left")b=m(this.right),k=this.right-d,M=b-_,L=m(t.left)+_,O=t.right;else if(o==="right")b=m(this.left),L=t.left,O=m(t.right)-_,k=b+_,M=this.left+d;else if(e==="x"){if(o==="center")b=m((t.top+t.bottom)/2+.5);else if(_t(o)){const V=Object.keys(o)[0],W=o[V];b=m(this.chart.scales[V].getPixelForValue(W))}D=t.top,F=t.bottom,v=b+_,C=v+d}else if(e==="y"){if(o==="center")b=m((t.left+t.right)/2);else if(_t(o)){const V=Object.keys(o)[0],W=o[V];b=m(this.chart.scales[V].getPixelForValue(W))}k=b-_,M=k-d,L=t.left,O=t.right}const Y=nt(n.ticks.maxTicksLimit,f),N=Math.max(1,Math.ceil(f/Y));for(w=0;w<f;w+=N){const V=this.getContext(w),W=r.setContext(V),G=a.setContext(V),Z=W.lineWidth,P=W.color,q=G.dash||[],J=G.dashOffset,ht=W.tickWidth,Q=W.tickColor,Pt=W.tickBorderDash||[],ut=W.tickBorderDashOffset;y=ox(this,w,l),y!==void 0&&(x=Qn(i,y,Z),c?k=M=L=O=x:v=C=D=F=x,u.push({tx1:k,ty1:v,tx2:M,ty2:C,x1:L,y1:D,x2:O,y2:F,width:Z,color:P,borderDash:q,borderDashOffset:J,tickWidth:ht,tickColor:Q,tickBorderDash:Pt,tickBorderDashOffset:ut}))}return this._ticksLength=f,this._borderValue=b,u}_computeLabelItems(t){const e=this.axis,i=this.options,{position:n,ticks:r}=i,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:f}=r,d=br(i.grid),u=d+h,p=f?-h:u,g=-Fi(this.labelRotation),_=[];let m,b,w,y,x,k,v,M,C,L,D,O,F="middle";if(n==="top")k=this.bottom-p,v=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+p,v=this._getXAxisLabelAlignment();else if(n==="left"){const N=this._getYAxisLabelAlignment(d);v=N.textAlign,x=N.x}else if(n==="right"){const N=this._getYAxisLabelAlignment(d);v=N.textAlign,x=N.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+u;else if(_t(n)){const N=Object.keys(n)[0],V=n[N];k=this.chart.scales[N].getPixelForValue(V)+u}v=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-u;else if(_t(n)){const N=Object.keys(n)[0],V=n[N];x=this.chart.scales[N].getPixelForValue(V)}v=this._getYAxisLabelAlignment(d).textAlign}e==="y"&&(l==="start"?F="top":l==="end"&&(F="bottom"));const Y=this._getLabelSizes();for(m=0,b=a.length;m<b;++m){w=a[m],y=w.label;const N=r.setContext(this.getContext(m));M=this.getPixelForTick(m)+r.labelOffset,C=this._resolveTickFontOptions(m),L=C.lineHeight,D=Bt(y)?y.length:1;const V=D/2,W=N.color,G=N.textStrokeColor,Z=N.textStrokeWidth;let P=v;o?(x=M,v==="inner"&&(m===b-1?P=this.options.reverse?"left":"right":m===0?P=this.options.reverse?"right":"left":P="center"),n==="top"?c==="near"||g!==0?O=-D*L+L/2:c==="center"?O=-Y.highest.height/2-V*L+L:O=-Y.highest.height+L/2:c==="near"||g!==0?O=L/2:c==="center"?O=Y.highest.height/2-V*L:O=Y.highest.height-D*L,f&&(O*=-1),g!==0&&!N.showLabelBackdrop&&(x+=L/2*Math.sin(g))):(k=M,O=(1-D)*L/2);let q;if(N.showLabelBackdrop){const J=Fe(N.backdropPadding),ht=Y.heights[m],Q=Y.widths[m];let Pt=O-J.top,ut=0-J.left;switch(F){case"middle":Pt-=ht/2;break;case"bottom":Pt-=ht;break}switch(v){case"center":ut-=Q/2;break;case"right":ut-=Q;break;case"inner":m===b-1?ut-=Q:m>0&&(ut-=Q/2);break}q={left:ut,top:Pt,width:Q+J.width,height:ht+J.height,color:N.backdropColor}}_.push({label:y,font:C,textOffset:O,options:{rotation:g,color:W,strokeColor:G,strokeWidth:Z,textAlign:P,textBaseline:F,translation:[x,k],backdrop:q}})}return _}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-Fi(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:n,padding:r}}=this.options,o=this._getLabelSizes(),a=t+r,l=o.widest.width;let c,h;return e==="left"?n?(h=this.right+r,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+r,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:n,width:r,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,n,r,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(r=>r.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,i=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let r,o;const a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(r=0,o=n.length;r<o;++r){const l=n[r];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:n}}=this,r=i.setContext(this.getContext()),o=i.display?r.width:0;if(!o)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,f,d;this.isHorizontal()?(c=Qn(t,this.left,o)-o/2,h=Qn(t,this.right,a)+a/2,f=d=l):(f=Qn(t,this.top,o)-o/2,d=Qn(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=r.width,e.strokeStyle=r.color,e.beginPath(),e.moveTo(c,f),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const i=this.ctx,n=this._computeLabelArea();n&&Va(i,n);const r=this.getLabelItems(t);for(const o of r){const a=o.options,l=o.font,c=o.label,h=o.textOffset;ks(i,c,0,h,l,a)}n&&Ha(i)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:n}}=this;if(!i.display)return;const r=he(i.font),o=Fe(i.padding),a=i.align;let l=r.lineHeight/2;e==="bottom"||e==="center"||_t(e)?(l+=o.bottom,Bt(i.text)&&(l+=r.lineHeight*(i.text.length-1))):l+=o.top;const{titleX:c,titleY:h,maxWidth:f,rotation:d}=ux(this,l,e,a);ks(t,i.text,0,0,r,{color:i.color,maxWidth:f,rotation:d,textAlign:hx(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=nt(t.grid&&t.grid.z,-1),n=nt(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ps.prototype.draw?[{z:e,draw:r=>{this.draw(r)}}]:[{z:i,draw:r=>{this.drawBackground(),this.drawGrid(r),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:r=>{this.drawLabels(r)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",n=[];let r,o;for(r=0,o=e.length;r<o;++r){const a=e[r];a[i]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return he(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Go{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;gx(e)&&(i=this.register(e));const n=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+t);return r in n||(n[r]=t,fx(t,o,i),this.override&&Nt.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,n=this.scope;i in e&&delete e[i],n&&i in Nt[n]&&(delete Nt[n][i],this.override&&delete Ms[i])}}function fx(s,t,e){const i=fo(Object.create(null),[e?Nt.get(e):{},Nt.get(t),s.defaults]);Nt.set(t,i),s.defaultRoutes&&dx(t,s.defaultRoutes),s.descriptors&&Nt.describe(t,s.descriptors)}function dx(s,t){Object.keys(t).forEach(e=>{const i=e.split("."),n=i.pop(),r=[s].concat(i).join("."),o=t[e].split("."),a=o.pop(),l=o.join(".");Nt.route(r,n,l,a)})}function gx(s){return"id"in s&&"defaults"in s}class px{constructor(){this.controllers=new Go($n,"datasets",!0),this.elements=new Go(mn,"elements"),this.plugins=new Go(Object,"plugins"),this.scales=new Go(Ps,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(n=>{const r=i||this._getRegistryForType(n);i||r.isForType(n)||r===this.plugins&&n.id?this._exec(t,r,n):Tt(n,o=>{const a=i||this._getRegistryForType(o);this._exec(t,a,o)})})}_exec(t,e,i){const n=Qc(t);Et(i["before"+n],[],i),e[t](i),Et(i["after"+n],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return n}}var Hi=new px;class _x{constructor(){this._init=[]}notify(t,e,i,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const r=n?this._descriptors(t).filter(n):this._descriptors(t),o=this._notify(r,t,e,i);return e==="afterDestroy"&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,n){n=n||{};for(const r of t){const o=r.plugin,a=o[i],l=[e,n,r.options];if(Et(a,l,o)===!1&&n.cancelable)return!1}return!0}invalidate(){dt(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,n=nt(i.options&&i.options.plugins,{}),r=mx(i);return n===!1&&!e?[]:xx(t,r,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,n=(r,o)=>r.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,i),t,"stop"),this._notify(n(i,e),t,"start")}}function mx(s){const t={},e=[],i=Object.keys(Hi.plugins.items);for(let r=0;r<i.length;r++)e.push(Hi.getPlugin(i[r]));const n=s.plugins||[];for(let r=0;r<n.length;r++){const o=n[r];e.indexOf(o)===-1&&(e.push(o),t[o.id]=!0)}return{plugins:e,localIds:t}}function bx(s,t){return!t&&s===!1?null:s===!0?{}:s}function xx(s,{plugins:t,localIds:e},i,n){const r=[],o=s.getContext();for(const a of t){const l=a.id,c=bx(i[l],n);c!==null&&r.push({plugin:a,options:yx(s.config,{plugin:a,local:e[l]},c,o)})}return r}function yx(s,{plugin:t,local:e},i,n){const r=s.pluginScopeKeys(t),o=s.getOptionScopes(i,r);return e&&t.defaults&&o.push(t.defaults),s.createResolver(o,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function fc(s,t){const e=Nt.datasets[s]||{};return((t.datasets||{})[s]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function vx(s,t){let e=s;return s==="_index_"?e=t:s==="_value_"&&(e=t==="x"?"y":"x"),e}function wx(s,t){return s===t?"_index_":"_value_"}function ju(s){if(s==="x"||s==="y"||s==="r")return s}function Sx(s){if(s==="top"||s==="bottom")return"x";if(s==="left"||s==="right")return"y"}function dc(s,...t){if(ju(s))return s;for(const e of t){const i=e.axis||Sx(e.position)||s.length>1&&ju(s[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${s}' axis. Please provide 'axis' or 'position' option.`)}function Uu(s,t,e){if(e[t+"AxisID"]===s)return{axis:t}}function Mx(s,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(i=>i.xAxisID===s||i.yAxisID===s);if(e.length)return Uu(s,"x",e[0])||Uu(s,"y",e[0])}return{}}function kx(s,t){const e=Ms[s.type]||{scales:{}},i=t.scales||{},n=fc(s.type,t),r=Object.create(null);return Object.keys(i).forEach(o=>{const a=i[o];if(!_t(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=dc(o,a,Mx(o,s),Nt.scales[a.type]),c=wx(l,n),h=e.scales||{};r[o]=Kr(Object.create(null),[{axis:l},a,h[l],h[c]])}),s.data.datasets.forEach(o=>{const a=o.type||s.type,l=o.indexAxis||fc(a,t),h=(Ms[a]||{}).scales||{};Object.keys(h).forEach(f=>{const d=vx(f,l),u=o[d+"AxisID"]||d;r[u]=r[u]||Object.create(null),Kr(r[u],[{axis:d},i[u],h[f]])})}),Object.keys(r).forEach(o=>{const a=r[o];Kr(a,[Nt.scales[a.type],Nt.scale])}),r}function jg(s){const t=s.options||(s.options={});t.plugins=nt(t.plugins,{}),t.scales=kx(s,t)}function Ug(s){return s=s||{},s.datasets=s.datasets||[],s.labels=s.labels||[],s}function Tx(s){return s=s||{},s.data=Ug(s.data),jg(s),s}const Gu=new Map,Gg=new Set;function qo(s,t){let e=Gu.get(s);return e||(e=t(),Gu.set(s,e),Gg.add(e)),e}const xr=(s,t,e)=>{const i=Wn(t,e);i!==void 0&&s.add(i)};class Px{constructor(t){this._config=Tx(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Ug(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),jg(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return qo(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return qo(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return qo(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,i=this.type;return qo(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const i=this._scopeCache;let n=i.get(t);return(!n||e)&&(n=new Map,i.set(t,n)),n}getOptionScopes(t,e,i){const{options:n,type:r}=this,o=this._cachedScopes(t,i),a=o.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(f=>xr(l,t,f))),h.forEach(f=>xr(l,n,f)),h.forEach(f=>xr(l,Ms[r]||{},f)),h.forEach(f=>xr(l,Nt,f)),h.forEach(f=>xr(l,hc,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Gg.has(e)&&o.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,Ms[e]||{},Nt.datasets[e]||{},{type:e},Nt,hc]}resolveNamedOptions(t,e,i,n=[""]){const r={$shared:!0},{resolver:o,subPrefixes:a}=qu(this._resolverCache,t,n);let l=o;if(Dx(o,e)){r.$shared=!1,i=Vn(i)?i():i;const c=this.createResolver(t,i,a);l=or(o,i,c)}for(const c of e)r[c]=l[c];return r}createResolver(t,e,i=[""],n){const{resolver:r}=qu(this._resolverCache,t,i);return _t(e)?or(r,e,void 0,n):r}}function qu(s,t,e){let i=s.get(t);i||(i=new Map,s.set(t,i));const n=e.join();let r=i.get(n);return r||(r={resolver:nh(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},i.set(n,r)),r}const Cx=s=>_t(s)&&Object.getOwnPropertyNames(s).some(t=>Vn(s[t]));function Dx(s,t){const{isScriptable:e,isIndexable:i}=kg(s);for(const n of t){const r=e(n),o=i(n),a=(o||r)&&s[n];if(r&&(Vn(a)||Cx(a))||o&&Bt(a))return!0}return!1}var Ox="4.5.0";const Ax=["top","bottom","left","right","chartArea"];function Ku(s,t){return s==="top"||s==="bottom"||Ax.indexOf(s)===-1&&t==="x"}function Qu(s,t){return function(e,i){return e[s]===i[s]?e[t]-i[t]:e[s]-i[s]}}function Zu(s){const t=s.chart,e=t.options.animation;t.notifyPlugins("afterRender"),Et(e&&e.onComplete,[s],t)}function Ex(s){const t=s.chart,e=t.options.animation;Et(e&&e.onProgress,[s],t)}function qg(s){return oh()&&typeof s=="string"?s=document.getElementById(s):s&&s.length&&(s=s[0]),s&&s.canvas&&(s=s.canvas),s}const fa={},Ju=s=>{const t=qg(s);return Object.values(fa).filter(e=>e.canvas===t).pop()};function Rx(s,t,e){const i=Object.keys(s);for(const n of i){const r=+n;if(r>=t){const o=s[n];delete s[n],(e>0||r>t)&&(s[r+e]=o)}}}function Lx(s,t,e,i){return!e||s.type==="mouseout"?null:i?t:s}class hh{static defaults=Nt;static instances=fa;static overrides=Ms;static registry=Hi;static version=Ox;static getChart=Ju;static register(...t){Hi.add(...t),tf()}static unregister(...t){Hi.remove(...t),tf()}constructor(t,e){const i=this.config=new Px(e),n=qg(t),r=Ju(n);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||Zb(n)),this.platform.updateConfig(i);const a=this.platform.acquireContext(n,o.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Dm(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new _x,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=jm(f=>this.update(f),o.resizeDelay||0),this._dataChanges=[],fa[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}tn.listen(this,"complete",Zu),tn.listen(this,"progress",Ex),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:n,_aspectRatio:r}=this;return dt(t)?e&&r?r:n?i/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Hi}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():wu(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return xu(this.canvas,this.ctx),this}stop(){return tn.stop(this),this}resize(t,e){tn.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,n=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(n,t,e,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,wu(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),Et(i.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};Tt(e,(i,n)=>{i.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,n=Object.keys(i).reduce((o,a)=>(o[a]=!1,o),{});let r=[];e&&(r=r.concat(Object.keys(e).map(o=>{const a=e[o],l=dc(o,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),Tt(r,o=>{const a=o.options,l=a.id,c=dc(l,a),h=nt(a.type,o.dtype);(a.position===void 0||Ku(a.position,c)!==Ku(o.dposition))&&(a.position=o.dposition),n[l]=!0;let f=null;if(l in i&&i[l].type===h)f=i[l];else{const d=Hi.getScale(h);f=new d({id:l,type:h,ctx:this.ctx,chart:this}),i[f.id]=f}f.init(a,t)}),Tt(n,(o,a)=>{o||delete i[a]}),Tt(i,o=>{Re.configure(this,o,o.options),Re.addBox(this,o)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((n,r)=>n.index-r.index),i>e){for(let n=e;n<i;++n)this._destroyDatasetMeta(n);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(Qu("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,n)=>{e.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,n;for(this._removeUnreferencedMetasets(),i=0,n=e.length;i<n;i++){const r=e[i];let o=this.getDatasetMeta(i);const a=r.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=a,o.indexAxis=r.indexAxis||fc(a,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{const l=Hi.getController(a),{datasetElementType:c,dataElementType:h}=Nt.datasets[a];Object.assign(l,{dataElementType:Hi.getElement(h),datasetElementType:c&&Hi.getElement(c)}),o.controller=new l(this,i),t.push(o.controller)}}return this._updateMetasets(),t}_resetElements(){Tt(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:f}=this.getDatasetMeta(c),d=!n&&r.indexOf(f)===-1;f.buildOrUpdateElements(d),o=Math.max(+f.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),n||Tt(r,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Qu("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){Tt(this.scales,t=>{Re.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!hu(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:n,count:r}of e){const o=i==="_removeElements"?-r:r;Rx(t,n,o)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=r=>new Set(t.filter(o=>o[0]===r).map((o,a)=>a+","+o.splice(1).join(","))),n=i(0);for(let r=1;r<e;r++)if(!hu(n,i(r)))return;return Array.from(n).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Re.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],Tt(this.boxes,n=>{i&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,r)=>{n._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,Vn(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),n={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(i.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(tn.has(this)?this.attached&&!tn.running(this)&&tn.start(this):(this.draw(),Zu({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:i,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let n,r;for(n=0,r=e.length;n<r;++n){const o=e[n];(!t||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},n=Ig(this,t);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(n&&Va(e,n),t.controller.draw(),n&&Ha(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return dn(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,n){const r=Ab.modes[e];return typeof r=="function"?r(this,t,i,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let n=i.filter(r=>r&&r._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(n)),n}getContext(){return this.$context||(this.$context=Yn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){const i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const n=i?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,n);go(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(r,{visible:i}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),tn.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),xu(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete fa[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(r,o)=>{e.addEventListener(this,r,o),t[r]=o},n=(r,o,a)=>{r.offsetX=o,r.offsetY=a,this._eventHandler(r)};Tt(this.options.events,r=>i(r,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},r=(l,c)=>{this.canvas&&this.resize(l,c)};let o;const a=()=>{n("attach",a),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,n("resize",r),this._stop(),this._resize(0,0),i("attach",a)},e.isAttached(this.canvas)?a():o()}unbindEvents(){Tt(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},Tt(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const n=i?"set":"remove";let r,o,a,l;for(e==="dataset"&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){o=t[a];const c=o&&this.getDatasetMeta(o.datasetIndex).controller;c&&c[n+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map(({datasetIndex:r,index:o})=>{const a=this.getDatasetMeta(r);if(!a)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:a.data[o],index:o}});!Pa(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,i){const n=this.options.hover,r=(l,c)=>l.filter(h=>!c.some(f=>h.datasetIndex===f.datasetIndex&&h.index===f.index)),o=r(e,t),a=i?t:r(t,e);o.length&&this.updateHoverStyle(o,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=o=>(o.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,n)===!1)return;const r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,n),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:n=[],options:r}=this,o=e,a=this._getActiveElements(t,n,i,o),l=Fm(t),c=Lx(t,this._lastEvent,i,l);i&&(this._lastEvent=null,Et(r.onHover,[t,a,this],this),l&&Et(r.onClick,[t,a,this],this));const h=!Pa(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,n){if(t.type==="mouseout")return[];if(!i)return e;const r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,n)}}function tf(){return Tt(hh.instances,s=>s._plugins.invalidate())}function Fx(s,t,e){const{startAngle:i,x:n,y:r,outerRadius:o,innerRadius:a,options:l}=t,{borderWidth:c,borderJoinStyle:h}=l,f=Math.min(c/o,De(i-e));if(s.beginPath(),s.arc(n,r,o-c/2,i+f/2,e-f/2),a>0){const d=Math.min(c/a,De(i-e));s.arc(n,r,a+c/2,e-d/2,i+d/2,!0)}else{const d=Math.min(c/2,o*De(i-e));if(h==="round")s.arc(n,r,d,e-wt/2,i+wt/2,!0);else if(h==="bevel"){const u=2*d*d,p=-u*Math.cos(e+wt/2)+n,g=-u*Math.sin(e+wt/2)+r,_=u*Math.cos(i+wt/2)+n,m=u*Math.sin(i+wt/2)+r;s.lineTo(p,g),s.lineTo(_,m)}}s.closePath(),s.moveTo(0,0),s.rect(0,0,s.canvas.width,s.canvas.height),s.clip("evenodd")}function Ix(s,t,e){const{startAngle:i,pixelMargin:n,x:r,y:o,outerRadius:a,innerRadius:l}=t;let c=n/a;s.beginPath(),s.arc(r,o,a,i-c,e+c),l>n?(c=n/l,s.arc(r,o,l,e+c,i-c,!0)):s.arc(r,o,n,e+Jt,i-Jt),s.closePath(),s.clip()}function zx(s){return ih(s,["outerStart","outerEnd","innerStart","innerEnd"])}function Bx(s,t,e,i){const n=zx(s.options.borderRadius),r=(e-t)/2,o=Math.min(r,i*t/2),a=l=>{const c=(e-Math.min(r,l))*i/2;return ge(l,0,Math.min(r,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:ge(n.innerStart,0,o),innerEnd:ge(n.innerEnd,0,o)}}function Fs(s,t,e,i){return{x:e+s*Math.cos(t),y:i+s*Math.sin(t)}}function Ra(s,t,e,i,n,r){const{x:o,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,f=Math.max(t.outerRadius+i+e-c,0),d=h>0?h+i+e+c:0;let u=0;const p=n-l;if(i){const N=h>0?h-i:0,V=f>0?f-i:0,W=(N+V)/2,G=W!==0?p*W/(W+i):p;u=(p-G)/2}const g=Math.max(.001,p*f-e/wt)/f,_=(p-g)/2,m=l+_+u,b=n-_-u,{outerStart:w,outerEnd:y,innerStart:x,innerEnd:k}=Bx(t,d,f,b-m),v=f-w,M=f-y,C=m+w/v,L=b-y/M,D=d+x,O=d+k,F=m+x/D,Y=b-k/O;if(s.beginPath(),r){const N=(C+L)/2;if(s.arc(o,a,f,C,N),s.arc(o,a,f,N,L),y>0){const Z=Fs(M,L,o,a);s.arc(Z.x,Z.y,y,L,b+Jt)}const V=Fs(O,b,o,a);if(s.lineTo(V.x,V.y),k>0){const Z=Fs(O,Y,o,a);s.arc(Z.x,Z.y,k,b+Jt,Y+Math.PI)}const W=(b-k/d+(m+x/d))/2;if(s.arc(o,a,d,b-k/d,W,!0),s.arc(o,a,d,W,m+x/d,!0),x>0){const Z=Fs(D,F,o,a);s.arc(Z.x,Z.y,x,F+Math.PI,m-Jt)}const G=Fs(v,m,o,a);if(s.lineTo(G.x,G.y),w>0){const Z=Fs(v,C,o,a);s.arc(Z.x,Z.y,w,m-Jt,C)}}else{s.moveTo(o,a);const N=Math.cos(C)*f+o,V=Math.sin(C)*f+a;s.lineTo(N,V);const W=Math.cos(L)*f+o,G=Math.sin(L)*f+a;s.lineTo(W,G)}s.closePath()}function Nx(s,t,e,i,n){const{fullCircles:r,startAngle:o,circumference:a}=t;let l=t.endAngle;if(r){Ra(s,t,e,i,l,n);for(let c=0;c<r;++c)s.fill();isNaN(a)||(l=o+(a%Ft||Ft))}return Ra(s,t,e,i,l,n),s.fill(),l}function Wx(s,t,e,i,n){const{fullCircles:r,startAngle:o,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:f,borderDashOffset:d,borderRadius:u}=l,p=l.borderAlign==="inner";if(!c)return;s.setLineDash(f||[]),s.lineDashOffset=d,p?(s.lineWidth=c*2,s.lineJoin=h||"round"):(s.lineWidth=c,s.lineJoin=h||"bevel");let g=t.endAngle;if(r){Ra(s,t,e,i,g,n);for(let _=0;_<r;++_)s.stroke();isNaN(a)||(g=o+(a%Ft||Ft))}p&&Ix(s,t,g),l.selfJoin&&g-o>=wt&&u===0&&h!=="miter"&&Fx(s,t,g),r||(Ra(s,t,e,i,g,n),s.stroke())}class Vx extends mn{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.getProps(["x","y"],i),{angle:r,distance:o}=pg(n,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,u=nt(f,l-a),p=po(r,a,l)&&a!==l,g=u>=Ft||p,_=un(o,c+d,h+d);return g&&_}getCenterPoint(t){const{x:e,y:i,startAngle:n,endAngle:r,innerRadius:o,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,h=(n+r)/2,f=(o+a+c+l)/2;return{x:e+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:i}=this,n=(e.offset||0)/4,r=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=i>Ft?Math.floor(i/Ft):0,i===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*n,Math.sin(a)*n);const l=1-Math.sin(Math.min(wt,i||0)),c=n*l;t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,Nx(t,this,c,r,o),Wx(t,this,c,r,o),t.restore()}}function Kg(s,t,e=t){s.lineCap=nt(e.borderCapStyle,t.borderCapStyle),s.setLineDash(nt(e.borderDash,t.borderDash)),s.lineDashOffset=nt(e.borderDashOffset,t.borderDashOffset),s.lineJoin=nt(e.borderJoinStyle,t.borderJoinStyle),s.lineWidth=nt(e.borderWidth,t.borderWidth),s.strokeStyle=nt(e.borderColor,t.borderColor)}function Hx(s,t,e){s.lineTo(e.x,e.y)}function Yx(s){return s.stepped?s0:s.tension||s.cubicInterpolationMode==="monotone"?r0:Hx}function Qg(s,t,e={}){const i=s.length,{start:n=0,end:r=i-1}=e,{start:o,end:a}=t,l=Math.max(n,o),c=Math.min(r,a),h=n<o&&r<o||n>a&&r>a;return{count:i,start:l,loop:t.loop,ilen:c<l&&!h?i+c-l:c-l}}function $x(s,t,e,i){const{points:n,options:r}=t,{count:o,start:a,loop:l,ilen:c}=Qg(n,e,i),h=Yx(r);let{move:f=!0,reverse:d}=i||{},u,p,g;for(u=0;u<=c;++u)p=n[(a+(d?c-u:u))%o],!p.skip&&(f?(s.moveTo(p.x,p.y),f=!1):h(s,g,p,d,r.stepped),g=p);return l&&(p=n[(a+(d?c:0))%o],h(s,g,p,d,r.stepped)),!!l}function Xx(s,t,e,i){const n=t.points,{count:r,start:o,ilen:a}=Qg(n,e,i),{move:l=!0,reverse:c}=i||{};let h=0,f=0,d,u,p,g,_,m;const b=y=>(o+(c?a-y:y))%r,w=()=>{g!==_&&(s.lineTo(h,_),s.lineTo(h,g),s.lineTo(h,m))};for(l&&(u=n[b(0)],s.moveTo(u.x,u.y)),d=0;d<=a;++d){if(u=n[b(d)],u.skip)continue;const y=u.x,x=u.y,k=y|0;k===p?(x<g?g=x:x>_&&(_=x),h=(f*h+y)/++f):(w(),s.lineTo(y,x),p=k,f=0,g=_=x),m=x}w()}function gc(s){const t=s.options,e=t.borderDash&&t.borderDash.length;return!s._decimated&&!s._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Xx:$x}function jx(s){return s.stepped?z0:s.tension||s.cubicInterpolationMode==="monotone"?B0:ss}function Ux(s,t,e,i){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,i)&&n.closePath()),Kg(s,t.options),s.stroke(n)}function Gx(s,t,e,i){const{segments:n,options:r}=t,o=gc(t);for(const a of n)Kg(s,r,a.style),s.beginPath(),o(s,t,a,{start:e,end:e+i-1})&&s.closePath(),s.stroke()}const qx=typeof Path2D=="function";function Kx(s,t,e,i){qx&&!t.options.segment?Ux(s,t,e,i):Gx(s,t,e,i)}class Xa extends mn{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const n=i.spanGaps?this._loop:this._fullLoop;D0(this._points,i,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=$0(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,n=t[e],r=this.points,o=Fg(this,{property:e,start:n,end:n});if(!o.length)return;const a=[],l=jx(i);let c,h;for(c=0,h=o.length;c<h;++c){const{start:f,end:d}=o[c],u=r[f],p=r[d];if(u===p){a.push(u);continue}const g=Math.abs((n-u[e])/(p[e]-u[e])),_=l(u,p,g,i.stepped);_[e]=t[e],a.push(_)}return a.length===1?a[0]:a}pathSegment(t,e,i){return gc(this)(t,this,e,i)}path(t,e,i){const n=this.segments,r=gc(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const a of n)o&=r(t,this,a,{start:e,end:e+i-1});return!!o}draw(t,e,i,n){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),Kx(t,this,i,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function ef(s,t,e,i){const n=s.options,{[e]:r}=s.getProps([e],i);return Math.abs(t-r)<n.radius+n.hitRadius}class Qx extends mn{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.options,{x:r,y:o}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return ef(this,t,"x",e)}inYRange(t,e){return ef(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);const i=e&&t.borderWidth||0;return(e+i)*2}draw(t,e){const i=this.options;this.skip||i.radius<.1||!dn(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,uc(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function Zg(s,t){const{x:e,y:i,base:n,width:r,height:o}=s.getProps(["x","y","base","width","height"],t);let a,l,c,h,f;return s.horizontal?(f=o/2,a=Math.min(e,n),l=Math.max(e,n),c=i-f,h=i+f):(f=r/2,a=e-f,l=e+f,c=Math.min(i,n),h=Math.max(i,n)),{left:a,top:c,right:l,bottom:h}}function En(s,t,e,i){return s?0:ge(t,e,i)}function Zx(s,t,e){const i=s.options.borderWidth,n=s.borderSkipped,r=Mg(i);return{t:En(n.top,r.top,0,e),r:En(n.right,r.right,0,t),b:En(n.bottom,r.bottom,0,e),l:En(n.left,r.left,0,t)}}function Jx(s,t,e){const{enableBorderRadius:i}=s.getProps(["enableBorderRadius"]),n=s.options.borderRadius,r=ms(n),o=Math.min(t,e),a=s.borderSkipped,l=i||_t(n);return{topLeft:En(!l||a.top||a.left,r.topLeft,0,o),topRight:En(!l||a.top||a.right,r.topRight,0,o),bottomLeft:En(!l||a.bottom||a.left,r.bottomLeft,0,o),bottomRight:En(!l||a.bottom||a.right,r.bottomRight,0,o)}}function ty(s){const t=Zg(s),e=t.right-t.left,i=t.bottom-t.top,n=Zx(s,e/2,i/2),r=Jx(s,e/2,i/2);return{outer:{x:t.left,y:t.top,w:e,h:i,radius:r},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:i-n.t-n.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,r.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(n.b,n.r))}}}}function Pl(s,t,e,i){const n=t===null,r=e===null,a=s&&!(n&&r)&&Zg(s,i);return a&&(n||un(t,a.left,a.right))&&(r||un(e,a.top,a.bottom))}function ey(s){return s.topLeft||s.topRight||s.bottomLeft||s.bottomRight}function iy(s,t){s.rect(t.x,t.y,t.w,t.h)}function Cl(s,t,e={}){const i=s.x!==e.x?-t:0,n=s.y!==e.y?-t:0,r=(s.x+s.w!==e.x+e.w?t:0)-i,o=(s.y+s.h!==e.y+e.h?t:0)-n;return{x:s.x+i,y:s.y+n,w:s.w+r,h:s.h+o,radius:s.radius}}class ny extends mn{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:n}}=this,{inner:r,outer:o}=ty(this),a=ey(o.radius)?_o:iy;t.save(),(o.w!==r.w||o.h!==r.h)&&(t.beginPath(),a(t,Cl(o,e,r)),t.clip(),a(t,Cl(r,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),a(t,Cl(r,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,i){return Pl(this,t,e,i)}inXRange(t,e){return Pl(this,t,null,e)}inYRange(t,e){return Pl(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:n,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+n)/2:e,y:r?i:(i+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}var sy=Object.freeze({__proto__:null,ArcElement:Vx,BarElement:ny,LineElement:Xa,PointElement:Qx});const pc=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],nf=pc.map(s=>s.replace("rgb(","rgba(").replace(")",", 0.5)"));function Jg(s){return pc[s%pc.length]}function tp(s){return nf[s%nf.length]}function ry(s,t){return s.borderColor=Jg(t),s.backgroundColor=tp(t),++t}function oy(s,t){return s.backgroundColor=s.data.map(()=>Jg(t++)),t}function ay(s,t){return s.backgroundColor=s.data.map(()=>tp(t++)),t}function ly(s){let t=0;return(e,i)=>{const n=s.getDatasetMeta(i).controller;n instanceof lh?t=oy(e,t):n instanceof Wg?t=ay(e,t):n&&(t=ry(e,t))}}function sf(s){let t;for(t in s)if(s[t].borderColor||s[t].backgroundColor)return!0;return!1}function cy(s){return s&&(s.borderColor||s.backgroundColor)}function hy(){return Nt.borderColor!=="rgba(0,0,0,0.1)"||Nt.backgroundColor!=="rgba(0,0,0,0.1)"}var uy={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(s,t,e){if(!e.enabled)return;const{data:{datasets:i},options:n}=s.config,{elements:r}=n,o=sf(i)||cy(n)||r&&sf(r)||hy();if(!e.forceOverride&&o)return;const a=ly(s);i.forEach(a)}};function fy(s,t,e,i,n){const r=n.samples||i;if(r>=e)return s.slice(t,t+e);const o=[],a=(e-2)/(r-2);let l=0;const c=t+e-1;let h=t,f,d,u,p,g;for(o[l++]=s[h],f=0;f<r-2;f++){let _=0,m=0,b;const w=Math.floor((f+1)*a)+1+t,y=Math.min(Math.floor((f+2)*a)+1,e)+t,x=y-w;for(b=w;b<y;b++)_+=s[b].x,m+=s[b].y;_/=x,m/=x;const k=Math.floor(f*a)+1+t,v=Math.min(Math.floor((f+1)*a)+1,e)+t,{x:M,y:C}=s[h];for(u=p=-1,b=k;b<v;b++)p=.5*Math.abs((M-_)*(s[b].y-C)-(M-s[b].x)*(m-C)),p>u&&(u=p,d=s[b],g=b);o[l++]=d,h=g}return o[l++]=s[c],o}function dy(s,t,e,i){let n=0,r=0,o,a,l,c,h,f,d,u,p,g;const _=[],m=t+e-1,b=s[t].x,y=s[m].x-b;for(o=t;o<t+e;++o){a=s[o],l=(a.x-b)/y*i,c=a.y;const x=l|0;if(x===h)c<p?(p=c,f=o):c>g&&(g=c,d=o),n=(r*n+a.x)/++r;else{const k=o-1;if(!dt(f)&&!dt(d)){const v=Math.min(f,d),M=Math.max(f,d);v!==u&&v!==k&&_.push({...s[v],x:n}),M!==u&&M!==k&&_.push({...s[M],x:n})}o>0&&k!==u&&_.push(s[k]),_.push(a),h=x,r=0,p=g=c,f=d=u=o}}return _}function ep(s){if(s._decimated){const t=s._data;delete s._decimated,delete s._data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function rf(s){s.data.datasets.forEach(t=>{ep(t)})}function gy(s,t){const e=t.length;let i=0,n;const{iScale:r}=s,{min:o,max:a,minDefined:l,maxDefined:c}=r.getUserBounds();return l&&(i=ge(fn(t,r.axis,o).lo,0,e-1)),c?n=ge(fn(t,r.axis,a).hi+1,i,e)-i:n=e-i,{start:i,count:n}}var py={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(s,t,e)=>{if(!e.enabled){rf(s);return}const i=s.width;s.data.datasets.forEach((n,r)=>{const{_data:o,indexAxis:a}=n,l=s.getDatasetMeta(r),c=o||n.data;if(Er([a,s.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=s.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||s.options.parsing)return;let{start:f,count:d}=gy(l,c);const u=e.threshold||4*i;if(d<=u){ep(n);return}dt(o)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let p;switch(e.algorithm){case"lttb":p=fy(c,f,d,i,e);break;case"min-max":p=dy(c,f,d,i);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=p})},destroy(s){rf(s)}};function _y(s,t,e){const i=s.segments,n=s.points,r=t.points,o=[];for(const a of i){let{start:l,end:c}=a;c=ja(l,c,n);const h=_c(e,n[l],n[c],a.loop);if(!t.segments){o.push({source:a,target:h,start:n[l],end:n[c]});continue}const f=Fg(t,h);for(const d of f){const u=_c(e,r[d.start],r[d.end],d.loop),p=Lg(a,n,u);for(const g of p)o.push({source:g,target:d,start:{[e]:of(h,u,"start",Math.max)},end:{[e]:of(h,u,"end",Math.min)}})}}return o}function _c(s,t,e,i){if(i)return;let n=t[s],r=e[s];return s==="angle"&&(n=De(n),r=De(r)),{property:s,start:n,end:r}}function my(s,t){const{x:e=null,y:i=null}=s||{},n=t.points,r=[];return t.segments.forEach(({start:o,end:a})=>{a=ja(o,a,n);const l=n[o],c=n[a];i!==null?(r.push({x:l.x,y:i}),r.push({x:c.x,y:i})):e!==null&&(r.push({x:e,y:l.y}),r.push({x:e,y:c.y}))}),r}function ja(s,t,e){for(;t>s;t--){const i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function of(s,t,e,i){return s&&t?i(s[e],t[e]):s?s[e]:t?t[e]:0}function ip(s,t){let e=[],i=!1;return Bt(s)?(i=!0,e=s):e=my(s,t),e.length?new Xa({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function af(s){return s&&s.fill!==!1}function by(s,t,e){let n=s[t].fill;const r=[t];let o;if(!e)return n;for(;n!==!1&&r.indexOf(n)===-1;){if(!Gt(n))return n;if(o=s[n],!o)return!1;if(o.visible)return n;r.push(n),n=o.fill}return!1}function xy(s,t,e){const i=Sy(s);if(_t(i))return isNaN(i.value)?!1:i;let n=parseFloat(i);return Gt(n)&&Math.floor(n)===n?yy(i[0],t,n,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function yy(s,t,e,i){return(s==="-"||s==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function vy(s,t){let e=null;return s==="start"?e=t.bottom:s==="end"?e=t.top:_t(s)?e=t.getPixelForValue(s.value):t.getBasePixel&&(e=t.getBasePixel()),e}function wy(s,t,e){let i;return s==="start"?i=e:s==="end"?i=t.options.reverse?t.min:t.max:_t(s)?i=s.value:i=t.getBaseValue(),i}function Sy(s){const t=s.options,e=t.fill;let i=nt(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function My(s){const{scale:t,index:e,line:i}=s,n=[],r=i.segments,o=i.points,a=ky(t,e);a.push(ip({x:null,y:t.bottom},i));for(let l=0;l<r.length;l++){const c=r[l];for(let h=c.start;h<=c.end;h++)Ty(n,o[h],a)}return new Xa({points:n,options:{}})}function ky(s,t){const e=[],i=s.getMatchingVisibleMetas("line");for(let n=0;n<i.length;n++){const r=i[n];if(r.index===t)break;r.hidden||e.unshift(r.dataset)}return e}function Ty(s,t,e){const i=[];for(let n=0;n<e.length;n++){const r=e[n],{first:o,last:a,point:l}=Py(r,t,"x");if(!(!l||o&&a)){if(o)i.unshift(l);else if(s.push(l),!a)break}}s.push(...i)}function Py(s,t,e){const i=s.interpolate(t,e);if(!i)return{};const n=i[e],r=s.segments,o=s.points;let a=!1,l=!1;for(let c=0;c<r.length;c++){const h=r[c],f=o[h.start][e],d=o[h.end][e];if(un(n,f,d)){a=n===f,l=n===d;break}}return{first:a,last:l,point:i}}class np{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:n,y:r,radius:o}=this;return e=e||{start:0,end:Ft},t.arc(n,r,o,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:n}=this,r=t.angle;return{x:e+Math.cos(r)*n,y:i+Math.sin(r)*n,angle:r}}}function Cy(s){const{chart:t,fill:e,line:i}=s;if(Gt(e))return Dy(t,e);if(e==="stack")return My(s);if(e==="shape")return!0;const n=Oy(s);return n instanceof np?n:ip(n,i)}function Dy(s,t){const e=s.getDatasetMeta(t);return e&&s.isDatasetVisible(t)?e.dataset:null}function Oy(s){return(s.scale||{}).getPointPositionForValue?Ey(s):Ay(s)}function Ay(s){const{scale:t={},fill:e}=s,i=vy(e,t);if(Gt(i)){const n=t.isHorizontal();return{x:n?i:null,y:n?null:i}}return null}function Ey(s){const{scale:t,fill:e}=s,i=t.options,n=t.getLabels().length,r=i.reverse?t.max:t.min,o=wy(e,t,r),a=[];if(i.grid.circular){const l=t.getPointPositionForValue(0,r);return new np({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(o)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,o));return a}function Dl(s,t,e){const i=Cy(t),{chart:n,index:r,line:o,scale:a,axis:l}=t,c=o.options,h=c.fill,f=c.backgroundColor,{above:d=f,below:u=f}=h||{},p=n.getDatasetMeta(r),g=Ig(n,p);i&&o.points.length&&(Va(s,e),Ry(s,{line:o,target:i,above:d,below:u,area:e,scale:a,axis:l,clip:g}),Ha(s))}function Ry(s,t){const{line:e,target:i,above:n,below:r,area:o,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;s.save();let h=r;r!==n&&(c==="x"?(lf(s,i,o.top),Ol(s,{line:e,target:i,color:n,scale:a,property:c,clip:l}),s.restore(),s.save(),lf(s,i,o.bottom)):c==="y"&&(cf(s,i,o.left),Ol(s,{line:e,target:i,color:r,scale:a,property:c,clip:l}),s.restore(),s.save(),cf(s,i,o.right),h=n)),Ol(s,{line:e,target:i,color:h,scale:a,property:c,clip:l}),s.restore()}function lf(s,t,e){const{segments:i,points:n}=t;let r=!0,o=!1;s.beginPath();for(const a of i){const{start:l,end:c}=a,h=n[l],f=n[ja(l,c,n)];r?(s.moveTo(h.x,h.y),r=!1):(s.lineTo(h.x,e),s.lineTo(h.x,h.y)),o=!!t.pathSegment(s,a,{move:o}),o?s.closePath():s.lineTo(f.x,e)}s.lineTo(t.first().x,e),s.closePath(),s.clip()}function cf(s,t,e){const{segments:i,points:n}=t;let r=!0,o=!1;s.beginPath();for(const a of i){const{start:l,end:c}=a,h=n[l],f=n[ja(l,c,n)];r?(s.moveTo(h.x,h.y),r=!1):(s.lineTo(e,h.y),s.lineTo(h.x,h.y)),o=!!t.pathSegment(s,a,{move:o}),o?s.closePath():s.lineTo(e,f.y)}s.lineTo(e,t.first().y),s.closePath(),s.clip()}function Ol(s,t){const{line:e,target:i,property:n,color:r,scale:o,clip:a}=t,l=_y(e,i,n);for(const{source:c,target:h,start:f,end:d}of l){const{style:{backgroundColor:u=r}={}}=c,p=i!==!0;s.save(),s.fillStyle=u,Ly(s,o,a,p&&_c(n,f,d)),s.beginPath();const g=!!e.pathSegment(s,c);let _;if(p){g?s.closePath():hf(s,i,d,n);const m=!!i.pathSegment(s,h,{move:g,reverse:!0});_=g&&m,_||hf(s,i,f,n)}s.closePath(),s.fill(_?"evenodd":"nonzero"),s.restore()}}function Ly(s,t,e,i){const n=t.chart.chartArea,{property:r,start:o,end:a}=i||{};if(r==="x"||r==="y"){let l,c,h,f;r==="x"?(l=o,c=n.top,h=a,f=n.bottom):(l=n.left,c=o,h=n.right,f=a),s.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),f=Math.min(f,e.bottom)),s.rect(l,c,h-l,f-c),s.clip()}}function hf(s,t,e,i){const n=t.interpolate(e,i);n&&s.lineTo(n.x,n.y)}var Fy={id:"filler",afterDatasetsUpdate(s,t,e){const i=(s.data.datasets||[]).length,n=[];let r,o,a,l;for(o=0;o<i;++o)r=s.getDatasetMeta(o),a=r.dataset,l=null,a&&a.options&&a instanceof Xa&&(l={visible:s.isDatasetVisible(o),index:o,fill:xy(a,o,i),chart:s,axis:r.controller.options.indexAxis,scale:r.vScale,line:a}),r.$filler=l,n.push(l);for(o=0;o<i;++o)l=n[o],!(!l||l.fill===!1)&&(l.fill=by(n,o,e.propagate))},beforeDraw(s,t,e){const i=e.drawTime==="beforeDraw",n=s.getSortedVisibleDatasetMetas(),r=s.chartArea;for(let o=n.length-1;o>=0;--o){const a=n[o].$filler;a&&(a.line.updateControlPoints(r,a.axis),i&&a.fill&&Dl(s.ctx,a,r))}},beforeDatasetsDraw(s,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const i=s.getSortedVisibleDatasetMetas();for(let n=i.length-1;n>=0;--n){const r=i[n].$filler;af(r)&&Dl(s.ctx,r,s.chartArea)}},beforeDatasetDraw(s,t,e){const i=t.meta.$filler;!af(i)||e.drawTime!=="beforeDatasetDraw"||Dl(s.ctx,i,s.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const uf=(s,t)=>{let{boxHeight:e=t,boxWidth:i=t}=s;return s.usePointStyle&&(e=Math.min(e,t),i=s.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},Iy=(s,t)=>s!==null&&t!==null&&s.datasetIndex===t.datasetIndex&&s.index===t.index;class ff extends mn{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=Et(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,n)=>t.sort(i,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const i=t.labels,n=he(i.font),r=n.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=uf(i,r);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(o,r,a,l)+10):(h=this.maxHeight,c=this._fitCols(o,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,n){const{ctx:r,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let f=t;r.textAlign="left",r.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach((p,g)=>{const _=i+e/2+r.measureText(p.text).width;(g===0||c[c.length-1]+_+2*a>o)&&(f+=h,c[c.length-(g>0?0:1)]=0,u+=h,d++),l[g]={left:0,top:u,row:d,width:_,height:n},c[c.length-1]+=_+a}),f}_fitCols(t,e,i,n){const{ctx:r,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=o-t;let f=a,d=0,u=0,p=0,g=0;return this.legendItems.forEach((_,m)=>{const{itemWidth:b,itemHeight:w}=zy(i,e,r,_,n);m>0&&u+w+2*a>h&&(f+=d+a,c.push({width:d,height:u}),p+=d+a,g++,d=u=0),l[m]={left:p,top:u,col:g,width:b,height:w},d=Math.max(d,b),u+=w+a}),f+=d,c.push({width:d,height:u}),f}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:n},rtl:r}}=this,o=Ks(r,this.left,this.width);if(this.isHorizontal()){let a=0,l=Pe(i,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=Pe(i,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=o.leftForLtr(o.x(l),c.width),l+=c.width+n}else{let a=0,l=Pe(i,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=Pe(i,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=o.leftForLtr(o.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Va(t,this),this._draw(),Ha(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:n}=this,{align:r,labels:o}=t,a=Nt.color,l=Ks(t.rtl,this.left,this.width),c=he(o.font),{padding:h}=o,f=c.size,d=f/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:_}=uf(o,f),m=function(k,v,M){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;n.save();const C=nt(M.lineWidth,1);if(n.fillStyle=nt(M.fillStyle,a),n.lineCap=nt(M.lineCap,"butt"),n.lineDashOffset=nt(M.lineDashOffset,0),n.lineJoin=nt(M.lineJoin,"miter"),n.lineWidth=C,n.strokeStyle=nt(M.strokeStyle,a),n.setLineDash(nt(M.lineDash,[])),o.usePointStyle){const L={radius:g*Math.SQRT2/2,pointStyle:M.pointStyle,rotation:M.rotation,borderWidth:C},D=l.xPlus(k,p/2),O=v+d;Sg(n,L,D,O,o.pointStyleWidth&&p)}else{const L=v+Math.max((f-g)/2,0),D=l.leftForLtr(k,p),O=ms(M.borderRadius);n.beginPath(),Object.values(O).some(F=>F!==0)?_o(n,{x:D,y:L,w:p,h:g,radius:O}):n.rect(D,L,p,g),n.fill(),C!==0&&n.stroke()}n.restore()},b=function(k,v,M){ks(n,M.text,k,v+_/2,c,{strikethrough:M.hidden,textAlign:l.textAlign(M.textAlign)})},w=this.isHorizontal(),y=this._computeTitleHeight();w?u={x:Pe(r,this.left+h,this.right-i[0]),y:this.top+h+y,line:0}:u={x:this.left+h,y:Pe(r,this.top+y+h,this.bottom-e[0].height),line:0},Ag(this.ctx,t.textDirection);const x=_+h;this.legendItems.forEach((k,v)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const M=n.measureText(k.text).width,C=l.textAlign(k.textAlign||(k.textAlign=o.textAlign)),L=p+d+M;let D=u.x,O=u.y;l.setWidth(this.width),w?v>0&&D+L+h>this.right&&(O=u.y+=x,u.line++,D=u.x=Pe(r,this.left+h,this.right-i[u.line])):v>0&&O+x>this.bottom&&(D=u.x=D+e[u.line].width+h,u.line++,O=u.y=Pe(r,this.top+y+h,this.bottom-e[u.line].height));const F=l.x(D);if(m(F,O,k),D=Um(C,D+p+d,w?D+L:this.right,t.rtl),b(l.x(D),O,k),w)u.x+=L+h;else if(typeof k.text!="string"){const Y=c.lineHeight;u.y+=sp(k,Y)+h}else u.y+=x}),Eg(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=he(e.font),n=Fe(e.padding);if(!e.display)return;const r=Ks(t.rtl,this.left,this.width),o=this.ctx,a=e.position,l=i.size/2,c=n.top+l;let h,f=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+c,f=Pe(t.align,f,this.right-d);else{const p=this.columnSizes.reduce((g,_)=>Math.max(g,_.height),0);h=c+Pe(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const u=Pe(a,f,f+d);o.textAlign=r.textAlign(th(a)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,ks(o,e.text,u,h,i)}_computeTitleHeight(){const t=this.options.title,e=he(t.font),i=Fe(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,n,r;if(un(t,this.left,this.right)&&un(e,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(n=r[i],un(t,n.left,n.left+n.width)&&un(e,n.top,n.top+n.height))return this.legendItems[i]}return null}handleEvent(t){const e=this.options;if(!Wy(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,r=Iy(n,i);n&&!r&&Et(e.onLeave,[t,n,this],this),this._hoveredItem=i,i&&!r&&Et(e.onHover,[t,i,this],this)}else i&&Et(e.onClick,[t,i,this],this)}}function zy(s,t,e,i,n){const r=By(i,s,t,e),o=Ny(n,i,t.lineHeight);return{itemWidth:r,itemHeight:o}}function By(s,t,e,i){let n=s.text;return n&&typeof n!="string"&&(n=n.reduce((r,o)=>r.length>o.length?r:o)),t+e.size/2+i.measureText(n).width}function Ny(s,t,e){let i=s;return typeof t.text!="string"&&(i=sp(t,e)),i}function sp(s,t){const e=s.text?s.text.length:0;return t*e}function Wy(s,t){return!!((s==="mousemove"||s==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(s==="click"||s==="mouseup"))}var Vy={id:"legend",_element:ff,start(s,t,e){const i=s.legend=new ff({ctx:s.ctx,options:e,chart:s});Re.configure(s,i,e),Re.addBox(s,i)},stop(s){Re.removeBox(s,s.legend),delete s.legend},beforeUpdate(s,t,e){const i=s.legend;Re.configure(s,i,e),i.options=e},afterUpdate(s){const t=s.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(s,t){t.replay||s.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(s,t,e){const i=t.datasetIndex,n=e.chart;n.isDatasetVisible(i)?(n.hide(i),t.hidden=!0):(n.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:s=>s.chart.options.color,boxWidth:40,padding:10,generateLabels(s){const t=s.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:n,color:r,useBorderRadius:o,borderRadius:a}}=s.legend.options;return s._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=Fe(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:r,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:i||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:o&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:s=>s.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:s=>!s.startsWith("on"),labels:{_scriptable:s=>!["generateLabels","filter","sort"].includes(s)}}};class uh extends mn{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=Bt(i.text)?i.text.length:1;this._padding=Fe(i.padding);const r=n*he(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:i,bottom:n,right:r,options:o}=this,a=o.align;let l=0,c,h,f;return this.isHorizontal()?(h=Pe(a,i,r),f=e+t,c=r-i):(o.position==="left"?(h=i+t,f=Pe(a,n,e),l=wt*-.5):(h=r-t,f=Pe(a,e,n),l=wt*.5),c=n-e),{titleX:h,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=he(e.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(r);ks(t,e.text,0,0,i,{color:e.color,maxWidth:l,rotation:c,textAlign:th(e.align),textBaseline:"middle",translation:[o,a]})}}function Hy(s,t){const e=new uh({ctx:s.ctx,options:t,chart:s});Re.configure(s,e,t),Re.addBox(s,e),s.titleBlock=e}var Yy={id:"title",_element:uh,start(s,t,e){Hy(s,e)},stop(s){const t=s.titleBlock;Re.removeBox(s,t),delete s.titleBlock},beforeUpdate(s,t,e){const i=s.titleBlock;Re.configure(s,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Ko=new WeakMap;var $y={id:"subtitle",start(s,t,e){const i=new uh({ctx:s.ctx,options:e,chart:s});Re.configure(s,i,e),Re.addBox(s,i),Ko.set(s,i)},stop(s){Re.removeBox(s,Ko.get(s)),Ko.delete(s)},beforeUpdate(s,t,e){const i=Ko.get(s);Re.configure(s,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Lr={average(s){if(!s.length)return!1;let t,e,i=new Set,n=0,r=0;for(t=0,e=s.length;t<e;++t){const a=s[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();i.add(l.x),n+=l.y,++r}}return r===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:n/r}},nearest(s,t){if(!s.length)return!1;let e=t.x,i=t.y,n=Number.POSITIVE_INFINITY,r,o,a;for(r=0,o=s.length;r<o;++r){const l=s[r].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=cc(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,i=l.y}return{x:e,y:i}}};function Vi(s,t){return t&&(Bt(t)?Array.prototype.push.apply(s,t):s.push(t)),s}function en(s){return(typeof s=="string"||s instanceof String)&&s.indexOf(`
`)>-1?s.split(`
`):s}function Xy(s,t){const{element:e,datasetIndex:i,index:n}=t,r=s.getDatasetMeta(i).controller,{label:o,value:a}=r.getLabelAndValue(n);return{chart:s,label:o,parsed:r.getParsed(n),raw:s.data.datasets[i].data[n],formattedValue:a,dataset:r.getDataset(),dataIndex:n,datasetIndex:i,element:e}}function df(s,t){const e=s.chart.ctx,{body:i,footer:n,title:r}=s,{boxWidth:o,boxHeight:a}=t,l=he(t.bodyFont),c=he(t.titleFont),h=he(t.footerFont),f=r.length,d=n.length,u=i.length,p=Fe(t.padding);let g=p.height,_=0,m=i.reduce((y,x)=>y+x.before.length+x.lines.length+x.after.length,0);if(m+=s.beforeBody.length+s.afterBody.length,f&&(g+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),m){const y=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=u*y+(m-u)*l.lineHeight+(m-1)*t.bodySpacing}d&&(g+=t.footerMarginTop+d*h.lineHeight+(d-1)*t.footerSpacing);let b=0;const w=function(y){_=Math.max(_,e.measureText(y).width+b)};return e.save(),e.font=c.string,Tt(s.title,w),e.font=l.string,Tt(s.beforeBody.concat(s.afterBody),w),b=t.displayColors?o+2+t.boxPadding:0,Tt(i,y=>{Tt(y.before,w),Tt(y.lines,w),Tt(y.after,w)}),b=0,e.font=h.string,Tt(s.footer,w),e.restore(),_+=p.width,{width:_,height:g}}function jy(s,t){const{y:e,height:i}=t;return e<i/2?"top":e>s.height-i/2?"bottom":"center"}function Uy(s,t,e,i){const{x:n,width:r}=i,o=e.caretSize+e.caretPadding;if(s==="left"&&n+r+o>t.width||s==="right"&&n-r-o<0)return!0}function Gy(s,t,e,i){const{x:n,width:r}=e,{width:o,chartArea:{left:a,right:l}}=s;let c="center";return i==="center"?c=n<=(a+l)/2?"left":"right":n<=r/2?c="left":n>=o-r/2&&(c="right"),Uy(c,s,t,e)&&(c="center"),c}function gf(s,t,e){const i=e.yAlign||t.yAlign||jy(s,e);return{xAlign:e.xAlign||t.xAlign||Gy(s,t,e,i),yAlign:i}}function qy(s,t){let{x:e,width:i}=s;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function Ky(s,t,e){let{y:i,height:n}=s;return t==="top"?i+=e:t==="bottom"?i-=n+e:i-=n/2,i}function pf(s,t,e,i){const{caretSize:n,caretPadding:r,cornerRadius:o}=s,{xAlign:a,yAlign:l}=e,c=n+r,{topLeft:h,topRight:f,bottomLeft:d,bottomRight:u}=ms(o);let p=qy(t,a);const g=Ky(t,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(h,d)+n:a==="right"&&(p+=Math.max(f,u)+n),{x:ge(p,0,i.width-t.width),y:ge(g,0,i.height-t.height)}}function Qo(s,t,e){const i=Fe(e.padding);return t==="center"?s.x+s.width/2:t==="right"?s.x+s.width-i.right:s.x+i.left}function _f(s){return Vi([],en(s))}function Qy(s,t,e){return Yn(s,{tooltip:t,tooltipItems:e,type:"tooltip"})}function mf(s,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?s.override(e):s}const rp={beforeTitle:Zi,title(s){if(s.length>0){const t=s[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Zi,beforeBody:Zi,beforeLabel:Zi,label(s){if(this&&this.options&&this.options.mode==="dataset")return s.label+": "+s.formattedValue||s.formattedValue;let t=s.dataset.label||"";t&&(t+=": ");const e=s.formattedValue;return dt(e)||(t+=e),t},labelColor(s){const e=s.chart.getDatasetMeta(s.datasetIndex).controller.getStyle(s.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(s){const e=s.chart.getDatasetMeta(s.datasetIndex).controller.getStyle(s.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Zi,afterBody:Zi,beforeFooter:Zi,footer:Zi,afterFooter:Zi};function je(s,t,e,i){const n=s[t].call(e,i);return typeof n>"u"?rp[t].call(e,i):n}class bf extends mn{static positioners=Lr;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),n=i.enabled&&e.options.animation&&i.animations,r=new zg(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=Qy(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:i}=e,n=je(i,"beforeTitle",this,t),r=je(i,"title",this,t),o=je(i,"afterTitle",this,t);let a=[];return a=Vi(a,en(n)),a=Vi(a,en(r)),a=Vi(a,en(o)),a}getBeforeBody(t,e){return _f(je(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,n=[];return Tt(t,r=>{const o={before:[],lines:[],after:[]},a=mf(i,r);Vi(o.before,en(je(a,"beforeLabel",this,r))),Vi(o.lines,je(a,"label",this,r)),Vi(o.after,en(je(a,"afterLabel",this,r))),n.push(o)}),n}getAfterBody(t,e){return _f(je(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,n=je(i,"beforeFooter",this,t),r=je(i,"footer",this,t),o=je(i,"afterFooter",this,t);let a=[];return a=Vi(a,en(n)),a=Vi(a,en(r)),a=Vi(a,en(o)),a}_createItems(t){const e=this._active,i=this.chart.data,n=[],r=[],o=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Xy(this.chart,e[l]));return t.filter&&(a=a.filter((h,f,d)=>t.filter(h,f,d,i))),t.itemSort&&(a=a.sort((h,f)=>t.itemSort(h,f,i))),Tt(a,h=>{const f=mf(t.callbacks,h);n.push(je(f,"labelColor",this,h)),r.push(je(f,"labelPointStyle",this,h)),o.push(je(f,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=a,a}update(t,e){const i=this.options.setContext(this.getContext()),n=this._active;let r,o=[];if(!n.length)this.opacity!==0&&(r={opacity:0});else{const a=Lr[i.position].call(this,n,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const l=this._size=df(this,i),c=Object.assign({},a,l),h=gf(this.chart,i,c),f=pf(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,r={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,n){const r=this.getCaretPosition(t,i,n);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){const{xAlign:n,yAlign:r}=this,{caretSize:o,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:f}=ms(a),{x:d,y:u}=t,{width:p,height:g}=e;let _,m,b,w,y,x;return r==="center"?(y=u+g/2,n==="left"?(_=d,m=_-o,w=y+o,x=y-o):(_=d+p,m=_+o,w=y-o,x=y+o),b=_):(n==="left"?m=d+Math.max(l,h)+o:n==="right"?m=d+p-Math.max(c,f)-o:m=this.caretX,r==="top"?(w=u,y=w-o,_=m-o,b=m+o):(w=u+g,y=w+o,_=m+o,b=m-o),x=w),{x1:_,x2:m,x3:b,y1:w,y2:y,y3:x}}drawTitle(t,e,i){const n=this.title,r=n.length;let o,a,l;if(r){const c=Ks(i.rtl,this.x,this.width);for(t.x=Qo(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",o=he(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,l=0;l<r;++l)e.fillText(n[l],c.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+a,l+1===r&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,n,r){const o=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c}=r,h=he(r.bodyFont),f=Qo(this,"left",r),d=n.x(f),u=l<h.lineHeight?(h.lineHeight-l)/2:0,p=e.y+u;if(r.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},_=n.leftForLtr(d,c)+c/2,m=p+l/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,uc(t,g,_,m),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,uc(t,g,_,m)}else{t.lineWidth=_t(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const g=n.leftForLtr(d,c),_=n.leftForLtr(n.xPlus(d,1),c-2),m=ms(o.borderRadius);Object.values(m).some(b=>b!==0)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,_o(t,{x:g,y:p,w:c,h:l,radius:m}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),_o(t,{x:_,y:p+1,w:c-2,h:l-2,radius:m}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(g,p,c,l),t.strokeRect(g,p,c,l),t.fillStyle=o.backgroundColor,t.fillRect(_,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:n}=this,{bodySpacing:r,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,f=he(i.bodyFont);let d=f.lineHeight,u=0;const p=Ks(i.rtl,this.x,this.width),g=function(M){e.fillText(M,p.x(t.x+u),t.y+d/2),t.y+=d+r},_=p.textAlign(o);let m,b,w,y,x,k,v;for(e.textAlign=o,e.textBaseline="middle",e.font=f.string,t.x=Qo(this,_,i),e.fillStyle=i.bodyColor,Tt(this.beforeBody,g),u=a&&_!=="right"?o==="center"?c/2+h:c+2+h:0,y=0,k=n.length;y<k;++y){for(m=n[y],b=this.labelTextColors[y],e.fillStyle=b,Tt(m.before,g),w=m.lines,a&&w.length&&(this._drawColorBox(e,t,y,p,i),d=Math.max(f.lineHeight,l)),x=0,v=w.length;x<v;++x)g(w[x]),d=f.lineHeight;Tt(m.after,g)}u=0,d=f.lineHeight,Tt(this.afterBody,g),t.y-=r}drawFooter(t,e,i){const n=this.footer,r=n.length;let o,a;if(r){const l=Ks(i.rtl,this.x,this.width);for(t.x=Qo(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",o=he(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,a=0;a<r;++a)e.fillText(n[a],l.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,n){const{xAlign:r,yAlign:o}=this,{x:a,y:l}=t,{width:c,height:h}=i,{topLeft:f,topRight:d,bottomLeft:u,bottomRight:p}=ms(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+f,l),o==="top"&&this.drawCaret(t,e,i,n),e.lineTo(a+c-d,l),e.quadraticCurveTo(a+c,l,a+c,l+d),o==="center"&&r==="right"&&this.drawCaret(t,e,i,n),e.lineTo(a+c,l+h-p),e.quadraticCurveTo(a+c,l+h,a+c-p,l+h),o==="bottom"&&this.drawCaret(t,e,i,n),e.lineTo(a+u,l+h),e.quadraticCurveTo(a,l+h,a,l+h-u),o==="center"&&r==="left"&&this.drawCaret(t,e,i,n),e.lineTo(a,l+f),e.quadraticCurveTo(a,l,a+f,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,n=i&&i.x,r=i&&i.y;if(n||r){const o=Lr[t.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=df(this,t),l=Object.assign({},o,this._size),c=gf(e,t,l),h=pf(t,l,c,e);(n._to!==h.x||r._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=Fe(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,n,e),Ag(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),Eg(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),r=!Pa(i,n),o=this._positionChanged(n,e);(r||o)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,i),a=this._positionChanged(o,t),l=e||!Pa(o,r)||a;return l&&(this._active=o,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,n){const r=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:n,options:r}=this,o=Lr[r.position].call(this,t,e);return o!==!1&&(i!==o.x||n!==o.y)}}var Zy={id:"tooltip",_element:bf,positioners:Lr,afterInit(s,t,e){e&&(s.tooltip=new bf({chart:s,options:e}))},beforeUpdate(s,t,e){s.tooltip&&s.tooltip.initialize(e)},reset(s,t,e){s.tooltip&&s.tooltip.initialize(e)},afterDraw(s){const t=s.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(s.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(s.ctx),s.notifyPlugins("afterTooltipDraw",e)}},afterEvent(s,t){if(s.tooltip){const e=t.replay;s.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(s,t)=>t.bodyFont.size,boxWidth:(s,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:rp},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:s=>s!=="filter"&&s!=="itemSort"&&s!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Jy=Object.freeze({__proto__:null,Colors:uy,Decimation:py,Filler:Fy,Legend:Vy,SubTitle:$y,Title:Yy,Tooltip:Zy});const tv=(s,t,e,i)=>(typeof t=="string"?(e=s.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function ev(s,t,e,i){const n=s.indexOf(t);if(n===-1)return tv(s,t,e,i);const r=s.lastIndexOf(t);return n!==r?e:n}const iv=(s,t)=>s===null?null:ge(Math.round(s),0,t);function xf(s){const t=this.getLabels();return s>=0&&s<t.length?t[s]:s}class nv extends Ps{static id="category";static defaults={ticks:{callback:xf}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const i=this.getLabels();for(const{index:n,label:r}of e)i[n]===r&&i.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(dt(t))return null;const i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:ev(i,t,nt(e,t),this._addedLabels),iv(e,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(n=this.getLabels().length-1)),this.min=i,this.max=n}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,n=[];let r=this.getLabels();r=t===0&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)n.push({value:o});return n}getLabelForValue(t){return xf.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function sv(s,t){const e=[],{bounds:n,step:r,min:o,max:a,precision:l,count:c,maxTicks:h,maxDigits:f,includeBounds:d}=s,u=r||1,p=h-1,{min:g,max:_}=t,m=!dt(o),b=!dt(a),w=!dt(c),y=(_-g)/(f+1);let x=fu((_-g)/p/u)*u,k,v,M,C;if(x<1e-14&&!m&&!b)return[{value:g},{value:_}];C=Math.ceil(_/x)-Math.floor(g/x),C>p&&(x=fu(C*x/p/u)*u),dt(l)||(k=Math.pow(10,l),x=Math.ceil(x*k)/k),n==="ticks"?(v=Math.floor(g/x)*x,M=Math.ceil(_/x)*x):(v=g,M=_),m&&b&&r&&Wm((a-o)/r,x/1e3)?(C=Math.round(Math.min((a-o)/x,h)),x=(a-o)/C,v=o,M=a):w?(v=m?o:v,M=b?a:M,C=c-1,x=(M-v)/C):(C=(M-v)/x,Qr(C,Math.round(C),x/1e3)?C=Math.round(C):C=Math.ceil(C));const L=Math.max(du(x),du(v));k=Math.pow(10,dt(l)?L:l),v=Math.round(v*k)/k,M=Math.round(M*k)/k;let D=0;for(m&&(d&&v!==o?(e.push({value:o}),v<o&&D++,Qr(Math.round((v+D*x)*k)/k,o,yf(o,y,s))&&D++):v<o&&D++);D<C;++D){const O=Math.round((v+D*x)*k)/k;if(b&&O>a)break;e.push({value:O})}return b&&d&&M!==a?e.length&&Qr(e[e.length-1].value,a,yf(a,y,s))?e[e.length-1].value=a:e.push({value:a}):(!b||M===a)&&e.push({value:M}),e}function yf(s,t,{horizontal:e,minRotation:i}){const n=Fi(i),r=(e?Math.sin(n):Math.cos(n))||.001,o=.75*t*(""+s).length;return Math.min(t/r,o)}class La extends Ps{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return dt(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:n,max:r}=this;const o=l=>n=e?n:l,a=l=>r=i?r:l;if(t){const l=Ki(n),c=Ki(r);l<0&&c<0?a(0):l>0&&c>0&&o(0)}if(n===r){let l=r===0?1:Math.abs(r*.05);a(r+l),t||o(n-l)}this.min=n,this.max=r}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:i}=t,n;return i?(n=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const n={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},r=this._range||this,o=sv(n,r);return t.bounds==="ticks"&&gg(o,this,"value"),t.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const n=(i-e)/Math.max(t.length-1,1)/2;e-=n,i+=n}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return vo(t,this.chart.options.locale,this.options.ticks.format)}}class rv extends La{static id="linear";static defaults={ticks:{callback:Wa.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Gt(t)?t:0,this.max=Gt(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=Fi(this.options.ticks.minRotation),n=(t?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,r.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const bo=s=>Math.floor(An(s)),Jn=(s,t)=>Math.pow(10,bo(s)+t);function vf(s){return s/Math.pow(10,bo(s))===1}function wf(s,t,e){const i=Math.pow(10,e),n=Math.floor(s/i);return Math.ceil(t/i)-n}function ov(s,t){const e=t-s;let i=bo(e);for(;wf(s,t,i)>10;)i++;for(;wf(s,t,i)<10;)i--;return Math.min(i,bo(s))}function av(s,{min:t,max:e}){t=si(s.min,t);const i=[],n=bo(t);let r=ov(t,e),o=r<0?Math.pow(10,Math.abs(r)):1;const a=Math.pow(10,r),l=n>r?Math.pow(10,n):0,c=Math.round((t-l)*o)/o,h=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-h)/Math.pow(10,r)),d=si(s.min,Math.round((l+h+f*Math.pow(10,r))*o)/o);for(;d<e;)i.push({value:d,major:vf(d),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(r++,f=2,o=r>=0?1:o),d=Math.round((l+h+f*Math.pow(10,r))*o)/o;const u=si(s.max,d);return i.push({value:u,major:vf(u),significand:f}),i}class lv extends Ps{static id="logarithmic";static defaults={ticks:{callback:Wa.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const i=La.prototype.parse.apply(this,[t,e]);if(i===0){this._zero=!0;return}return Gt(i)&&i>0?i:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Gt(t)?Math.max(0,t):null,this.max=Gt(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Gt(this._userMin)&&(this.min=t===Jn(this.min,0)?Jn(this.min,-1):Jn(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let i=this.min,n=this.max;const r=a=>i=t?i:a,o=a=>n=e?n:a;i===n&&(i<=0?(r(1),o(10)):(r(Jn(i,-1)),o(Jn(n,1)))),i<=0&&r(Jn(n,-1)),n<=0&&o(Jn(i,1)),this.min=i,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},i=av(e,this);return t.bounds==="ticks"&&gg(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":vo(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=An(t),this._valueRange=An(this.max)-An(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(An(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function mc(s){const t=s.ticks;if(t.display&&s.display){const e=Fe(t.backdropPadding);return nt(t.font&&t.font.size,Nt.font.size)+e.height}return 0}function cv(s,t,e){return e=Bt(e)?e:[e],{w:n0(s,t.string,e),h:e.length*t.lineHeight}}function Sf(s,t,e,i,n){return s===i||s===n?{start:t-e/2,end:t+e/2}:s<i||s>n?{start:t-e,end:t}:{start:t,end:t+e}}function hv(s){const t={l:s.left+s._padding.left,r:s.right-s._padding.right,t:s.top+s._padding.top,b:s.bottom-s._padding.bottom},e=Object.assign({},t),i=[],n=[],r=s._pointLabels.length,o=s.options.pointLabels,a=o.centerPointLabels?wt/r:0;for(let l=0;l<r;l++){const c=o.setContext(s.getPointLabelContext(l));n[l]=c.padding;const h=s.getPointPosition(l,s.drawingArea+n[l],a),f=he(c.font),d=cv(s.ctx,f,s._pointLabels[l]);i[l]=d;const u=De(s.getIndexAngle(l)+a),p=Math.round(Zc(u)),g=Sf(p,h.x,d.w,0,180),_=Sf(p,h.y,d.h,90,270);uv(e,t,u,g,_)}s.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),s._pointLabelItems=gv(s,i,n)}function uv(s,t,e,i,n){const r=Math.abs(Math.sin(e)),o=Math.abs(Math.cos(e));let a=0,l=0;i.start<t.l?(a=(t.l-i.start)/r,s.l=Math.min(s.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/r,s.r=Math.max(s.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/o,s.t=Math.min(s.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/o,s.b=Math.max(s.b,t.b+l))}function fv(s,t,e){const i=s.drawingArea,{extra:n,additionalAngle:r,padding:o,size:a}=e,l=s.getPointPosition(t,i+n+o,r),c=Math.round(Zc(De(l.angle+Jt))),h=mv(l.y,a.h,c),f=pv(c),d=_v(l.x,a.w,f);return{visible:!0,x:l.x,y:h,textAlign:f,left:d,top:h,right:d+a.w,bottom:h+a.h}}function dv(s,t){if(!t)return!0;const{left:e,top:i,right:n,bottom:r}=s;return!(dn({x:e,y:i},t)||dn({x:e,y:r},t)||dn({x:n,y:i},t)||dn({x:n,y:r},t))}function gv(s,t,e){const i=[],n=s._pointLabels.length,r=s.options,{centerPointLabels:o,display:a}=r.pointLabels,l={extra:mc(r)/2,additionalAngle:o?wt/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const f=fv(s,h,l);i.push(f),a==="auto"&&(f.visible=dv(f,c),f.visible&&(c=f))}return i}function pv(s){return s===0||s===180?"center":s<180?"left":"right"}function _v(s,t,e){return e==="right"?s-=t:e==="center"&&(s-=t/2),s}function mv(s,t,e){return e===90||e===270?s-=t/2:(e>270||e<90)&&(s-=t),s}function bv(s,t,e){const{left:i,top:n,right:r,bottom:o}=e,{backdropColor:a}=t;if(!dt(a)){const l=ms(t.borderRadius),c=Fe(t.backdropPadding);s.fillStyle=a;const h=i-c.left,f=n-c.top,d=r-i+c.width,u=o-n+c.height;Object.values(l).some(p=>p!==0)?(s.beginPath(),_o(s,{x:h,y:f,w:d,h:u,radius:l}),s.fill()):s.fillRect(h,f,d,u)}}function xv(s,t){const{ctx:e,options:{pointLabels:i}}=s;for(let n=t-1;n>=0;n--){const r=s._pointLabelItems[n];if(!r.visible)continue;const o=i.setContext(s.getPointLabelContext(n));bv(e,o,r);const a=he(o.font),{x:l,y:c,textAlign:h}=r;ks(e,s._pointLabels[n],l,c+a.lineHeight/2,a,{color:o.color,textAlign:h,textBaseline:"middle"})}}function op(s,t,e,i){const{ctx:n}=s;if(e)n.arc(s.xCenter,s.yCenter,t,0,Ft);else{let r=s.getPointPosition(0,t);n.moveTo(r.x,r.y);for(let o=1;o<i;o++)r=s.getPointPosition(o,t),n.lineTo(r.x,r.y)}}function yv(s,t,e,i,n){const r=s.ctx,o=t.circular,{color:a,lineWidth:l}=t;!o&&!i||!a||!l||e<0||(r.save(),r.strokeStyle=a,r.lineWidth=l,r.setLineDash(n.dash||[]),r.lineDashOffset=n.dashOffset,r.beginPath(),op(s,e,o,i),r.closePath(),r.stroke(),r.restore())}function vv(s,t,e){return Yn(s,{label:e,index:t,type:"pointLabel"})}class wv extends La{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Wa.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=Fe(mc(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=Gt(t)&&!isNaN(t)?t:0,this.max=Gt(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/mc(this.options))}generateTickLabels(t){La.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,i)=>{const n=Et(this.options.pointLabels.callback,[e,i],this);return n||n===0?n:""}).filter((e,i)=>this.chart.getDataVisibility(i))}fit(){const t=this.options;t.display&&t.pointLabels.display?hv(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,n))}getIndexAngle(t){const e=Ft/(this._pointLabels.length||1),i=this.options.startAngle||0;return De(t*e+Fi(i))}getDistanceFromCenterForValue(t){if(dt(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(dt(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return vv(this.getContext(),t,i)}}getPointPosition(t,e,i=0){const n=this.getIndexAngle(t)-Jt+i;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:n,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:n,bottom:r}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),op(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:n,border:r}=e,o=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&xv(this,o),n.display&&this.ticks.forEach((h,f)=>{if(f!==0||f===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const d=this.getContext(f),u=n.setContext(d),p=r.setContext(d);yv(this,u,l,o,p)}}),i.display){for(t.save(),a=o-1;a>=0;a--){const h=i.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:d}=h;!d||!f||(t.lineWidth=d,t.strokeStyle=f,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const n=this.getIndexAngle(0);let r,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=i.setContext(this.getContext(l)),h=he(c.font);if(r=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,o=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=Fe(c.backdropPadding);t.fillRect(-o/2-f.left,-r-h.size/2-f.top,o+f.width,h.size+f.height)}ks(t,a.label,0,-r,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}const Ua={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Ge=Object.keys(Ua);function Mf(s,t){return s-t}function kf(s,t){if(dt(t))return null;const e=s._adapter,{parser:i,round:n,isoWeekday:r}=s._parseOpts;let o=t;return typeof i=="function"&&(o=i(o)),Gt(o)||(o=typeof i=="string"?e.parse(o,i):e.parse(o)),o===null?null:(n&&(o=n==="week"&&(rr(r)||r===!0)?e.startOf(o,"isoWeek",r):e.startOf(o,n)),+o)}function Tf(s,t,e,i){const n=Ge.length;for(let r=Ge.indexOf(s);r<n-1;++r){const o=Ua[Ge[r]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((e-t)/(a*o.size))<=i)return Ge[r]}return Ge[n-1]}function Sv(s,t,e,i,n){for(let r=Ge.length-1;r>=Ge.indexOf(e);r--){const o=Ge[r];if(Ua[o].common&&s._adapter.diff(n,i,o)>=t-1)return o}return Ge[e?Ge.indexOf(e):0]}function Mv(s){for(let t=Ge.indexOf(s)+1,e=Ge.length;t<e;++t)if(Ua[Ge[t]].common)return Ge[t]}function Pf(s,t,e){if(!e)s[t]=!0;else if(e.length){const{lo:i,hi:n}=Jc(e,t),r=e[i]>=t?e[i]:e[n];s[r]=!0}}function kv(s,t,e,i){const n=s._adapter,r=+n.startOf(t[0].value,i),o=t[t.length-1].value;let a,l;for(a=r;a<=o;a=+n.add(a,1,i))l=e[a],l>=0&&(t[l].major=!0);return t}function Cf(s,t,e){const i=[],n={},r=t.length;let o,a;for(o=0;o<r;++o)a=t[o],n[a]=o,i.push({value:a,major:!1});return r===0||!e?i:kv(s,i,n,e)}class bc extends Ps{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),n=this._adapter=new Tb._date(t.adapters.date);n.init(e),Kr(i.displayFormats,n.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:kf(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:n,max:r,minDefined:o,maxDefined:a}=this.getUserBounds();function l(c){!o&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(r=Math.max(r,c.max))}(!o||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=Gt(n)&&!isNaN(n)?n:+e.startOf(Date.now(),i),r=Gt(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(n,r-1),this.max=Math.max(n+1,r)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,n=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const r=this.min,o=this.max,a=$m(n,r,o);return this._unit=e.unit||(i.autoSkip?Tf(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):Sv(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:Mv(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Cf(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,i=0,n,r;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,r=this.getDecimalForValue(t[t.length-1]),t.length===1?i=r:i=(r-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;e=ge(e,0,o),i=ge(i,0,o),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){const t=this._adapter,e=this.min,i=this.max,n=this.options,r=n.time,o=r.unit||Tf(r.minUnit,e,i,this._getLabelCapacity(e)),a=nt(n.ticks.stepSize,1),l=o==="week"?r.isoWeekday:!1,c=rr(l)||l===!0,h={};let f=e,d,u;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":o),t.diff(i,e,o)>1e5*a)throw new Error(e+" and "+i+" are too far apart with stepSize of "+a+" "+o);const p=n.ticks.source==="data"&&this.getDataTimestamps();for(d=f,u=0;d<i;d=+t.add(d,a,o),u++)Pf(h,d,p);return(d===i||n.bounds==="ticks"||u===1)&&Pf(h,d,p),Object.keys(h).sort(Mf).map(g=>+g)}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,r=this._unit,o=e||n[r];return this._adapter.format(t,o)}_tickFormatFunction(t,e,i,n){const r=this.options,o=r.ticks.callback;if(o)return Et(o,[t,e,i],this);const a=r.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],f=c&&a[c],d=i[e],u=c&&f&&d&&d.major;return this._adapter.format(t,n||(u?f:h))}generateTickLabels(t){let e,i,n;for(e=0,i=t.length;e<i;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,n=Fi(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(n),o=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*o,h:i*o+a*r}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,n=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,Cf(this,[t],this._majorUnit),n),o=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,i=n.length;e<i;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const n=this.getLabels();for(e=0,i=n.length;e<i;++e)t.push(kf(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return mg(t.sort(Mf))}}function Zo(s,t,e){let i=0,n=s.length-1,r,o,a,l;e?(t>=s[i].pos&&t<=s[n].pos&&({lo:i,hi:n}=fn(s,"pos",t)),{pos:r,time:a}=s[i],{pos:o,time:l}=s[n]):(t>=s[i].time&&t<=s[n].time&&({lo:i,hi:n}=fn(s,"time",t)),{time:r,pos:a}=s[i],{time:o,pos:l}=s[n]);const c=o-r;return c?a+(l-a)*(t-r)/c:a}class Tv extends bc{static id="timeseries";static defaults=bc.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Zo(e,this.min),this._tableRange=Zo(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,n=[],r=[];let o,a,l,c,h;for(o=0,a=t.length;o<a;++o)c=t[o],c>=e&&c<=i&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,a=n.length;o<a;++o)h=n[o+1],l=n[o-1],c=n[o],Math.round((h+l)/2)!==c&&r.push({time:c,pos:o/(a-1)});return r}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return(!i.includes(t)||!i.length)&&i.splice(0,0,t),(!i.includes(e)||i.length===1)&&i.push(e),i.sort((n,r)=>n-r)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Zo(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return Zo(this._table,i*this._tableRange+this._minPos,!0)}}var Pv=Object.freeze({__proto__:null,CategoryScale:nv,LinearScale:rv,LogarithmicScale:lv,RadialLinearScale:wv,TimeScale:bc,TimeSeriesScale:Tv});const Cv=[kb,sy,Jy,Pv];hh.register(...Cv);ue.registerPlugin(gt);ue.registerPlugin(lr);function Dv({lenses:s,projects:t,publications:e}){const i=s,n=t,r=e;let o=0;const a={isAnimating:!1},l=document.getElementById("lens-rail-viewport"),c=document.getElementById("lens-rail"),h=document.getElementById("content-display");let f=[],d;function u(D){const O=document.createElement("button");return O.className="pill px-5 py-2 text-sm font-medium rounded-full transition-opacity duration-300 bg-gray-800/50 text-gray-300 hover:opacity-85 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-gray-400",O.dataset.lensId=D.id,O.innerHTML=`
            <span class="relative z-10">${D.name}</span>
            <div class="pill-glint"></div>
            <div class="pill-border-trace"></div>
        `,O.addEventListener("click",F=>{if(d&&d.isDragging){F.stopPropagation();return}_(O)}),O}function p(){c.innerHTML="",[...i,...i,...i].forEach(D=>{c.appendChild(u(D))}),f=ue.utils.toArray("#lens-rail .pill")}function g(D){o=D%i.length,f.forEach((O,F)=>{const Y=F%i.length===o;O.classList.toggle("unfocused",!Y),O.classList.toggle("bg-gray-700",Y),O.classList.toggle("text-white",Y),O.classList.toggle("bg-gray-800/50",!Y),O.classList.toggle("text-gray-300",!Y)})}function _(D){if(a.isAnimating)return;const O=f.indexOf(D);a.isAnimating=!0;const F=i[O%i.length],Y=l.offsetWidth/2-(D.offsetLeft+D.offsetWidth/2);ue.to(c,{x:Y,duration:1.4,ease:"power4.inOut"}),O%i.length!==o?ue.timeline({onComplete:()=>{a.isAnimating=!1}}).add(()=>{g(O),m(D)}).to(h,{opacity:0,duration:.3,ease:"power1.in"},"<").add(()=>{F.type==="redirect"?(h.innerHTML='<div class="text-center py-20 text-gray-500">Redirecting to topics...</div>',ue.set(h,{opacity:1})):(w(F.id),setTimeout(()=>v(F.id),400))},">-0.1"):(m(D),setTimeout(()=>a.isAnimating=!1,1e3))}function m(D){ue.timeline().fromTo(D.querySelector(".pill-glint"),{x:"-250%"},{x:"250%",duration:.85,ease:"power2.inOut"}).fromTo(D.querySelector(".pill-border-trace"),{opacity:0},{keyframes:[{"--angle":"180deg",opacity:1,duration:.4},{"--angle":"450deg",duration:.8},{opacity:0,duration:.3}],ease:"none"},"<0.1")}function b(){const D=i.length*(f[0].offsetWidth+parseInt(getComputedStyle(f[0]).marginRight));d=lr.create(c,{type:"x",edgeResistance:.65,inertia:{resistance:200,duration:{min:.8,max:3.2},ease:"power2.out"},onDragStart:()=>{ue.killTweensOf(c)},onDrag:function(){const O=ue.getProperty(this.target,"x");O>-D?(ue.set(this.target,{x:O-D}),this.update()):O<-D*2&&(ue.set(this.target,{x:O+D}),this.update())},onThrowUpdate:function(){const O=ue.getProperty(this.target,"x");O>-D?(ue.set(this.target,{x:O-D}),this.update()):O<-D*2&&(ue.set(this.target,{x:O+D}),this.update())}})[0]}function w(D){let O="";const F=x(D),Y=n.filter(N=>N.lenses.includes(D)).length||3;for(let N=0;N<Y;N++)O+=`<div class="reveal-item opacity-0"><div class="skeleton-box ${y(F)}"></div></div>`;h.innerHTML=`<div class="${k(D)}">${O}</div>`,ue.to("#content-display .reveal-item",{opacity:1,duration:.3})}function y(D){switch(D){case"timeline":return"h-48";case"formal":return"h-24";default:return"h-40"}}function x(D){switch(D){case"featured":case"latest":return"timeline";case"research":return"formal";case"tools":return"grid";case"experimental":return"experimental-grid";default:return"grid"}}function k(D){switch(D){case"featured":case"latest":return"space-y-8";case"research":return"space-y-6";case"tools":return"grid md:grid-cols-2 lg:grid-cols-3 gap-8";case"experimental":return"grid grid-cols-2 md:grid-cols-4 gap-4 auto-rows-min";default:return"grid md:grid-cols-2 lg:grid-cols-3 gap-8"}}function v(D){const O=n.filter(N=>N.lenses.includes(D)),F=x(D),Y=O.map((N,V)=>`<div class="reveal-item opacity-0">${(()=>{switch(F){case"timeline":return`
                            <div class="grid md:grid-cols-12 gap-8 items-center">
                                <div class="md:col-span-1 text-gray-500 font-bold text-lg hidden md:block">${String(V+1).padStart(2,"0")}</div>
                                <div class="md:col-span-4 rounded-lg overflow-hidden aspect-w-4 aspect-h-3">
                                    <img src="${N.cover}" alt="${N.title}" class="w-full h-full object-cover">
                                </div>
                                <div class="md:col-span-7">
                                    <h3 class="font-serif text-2xl mb-2">${N.title}</h3>
                                    <p class="text-gray-400">${N.description}</p>
                                    <span class="inline-block mt-3 text-xs font-semibold bg-sky-900/50 text-sky-300 px-2 py-1 rounded-full">${N.domain}</span>
                                </div>
                            </div>`;case"formal":return`
                            <div class="bg-gray-900/50 p-6 rounded-lg hover:bg-gray-900 transition-colors duration-300">
                                <h3 class="font-serif text-xl mb-2">${N.title}</h3>
                                <p class="text-gray-400 text-sm">${N.description}</p>
                                <span class="inline-block mt-4 text-xs font-semibold bg-indigo-900/50 text-indigo-300 px-2 py-1 rounded-full">${N.domain}</span>
                            </div>`;case"experimental-grid":const G=V%3===0?"col-span-2":"col-span-1",Z=V%2===0?"row-span-2":"row-span-1";return`
                            <div class="${G} ${Z} group relative rounded-lg overflow-hidden">
                                <img src="${N.cover}" alt="${N.title}" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                <div class="absolute bottom-0 left-0 p-4">
                                    <h3 class="font-medium text-lg leading-tight">${N.title}</h3>
                                </div>
                            </div>`;default:return`
                            <div class="bg-gray-900/30 rounded-lg overflow-hidden group">
                                <div class="aspect-w-16 aspect-h-9 overflow-hidden">
                                    <img src="${N.cover}" alt="${N.title}" class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                                </div>
                                <div class="p-4">
                                    <h3 class="font-medium">${N.title}</h3>
                                    <p class="text-sm text-gray-400 mt-1 truncate">${N.description}</p>
                                </div>
                            </div>`}})()}</div>`).join("");h.innerHTML=`<div class="${k(D)}">${Y}</div>`,ue.fromTo("#content-display .reveal-item",{opacity:0,y:20},{opacity:1,y:0,stagger:.07,duration:.5,ease:"power2.out"})}function M(){const D=document.getElementById("publications").querySelector(".grid");D&&(D.innerHTML=r.map(O=>`
            <div class="bg-gray-900/30 p-5 rounded-lg">
                <h3 class="font-medium">${O.title}</h3>
                <p class="text-sm text-gray-500 mt-1">${O.journal}, ${O.year}</p>
                <a href="${O.url}" class="text-sm text-sky-400 hover:text-sky-300 mt-2 inline-block">View Publication →</a>
            </div>
        `).join(""))}function C(){const D=document.getElementById("project-dist-chart").getContext("2d");if(!D)return;const O=n.reduce((Y,N)=>(Y[N.domain]=(Y[N.domain]||0)+1,Y),{}),F={labels:Object.keys(O),datasets:[{label:"Project Domains",data:Object.values(O),backgroundColor:["rgba(59, 130, 246, 0.7)","rgba(168, 85, 247, 0.7)","rgba(34, 197, 94, 0.7)","rgba(239, 68, 68, 0.7)","rgba(249, 115, 22, 0.7)"],borderColor:"#0d0d0d",borderWidth:2}]};new hh(D,{type:"doughnut",data:F,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{color:"#f0f0f0",font:{family:"Inter, sans-serif"}}}}}})}function L(){p(),v(i[o].id),M(),C(),b();const D=f[i.length+o],O=l.offsetWidth/2-(D.offsetLeft+D.offsetWidth/2);ue.set(c,{x:O}),g(o)}L()}const Ov=document.getElementById("work-page-container"),Av=JSON.parse(Ov.dataset.pageData);Dv(Av);
