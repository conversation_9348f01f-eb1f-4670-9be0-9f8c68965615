<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>About | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>.content-container[data-astro-cid-kh7btl4r]{max-width:800px;margin:0 auto 60px;padding:0 30px}.back-section[data-astro-cid-kh7btl4r]{margin-bottom:40px}.about-content[data-astro-cid-kh7btl4r]{padding:20px 0}h2[data-astro-cid-kh7btl4r]{font-family:Georgia Custom,Georgia,serif;margin:2.5rem 0 1.2rem;font-size:1.6rem;color:#24170f;letter-spacing:-.01em}p[data-astro-cid-kh7btl4r]{font-family:Georgia Custom,Georgia,serif;line-height:1.2;margin-bottom:1.5rem;font-size:1.1rem;color:#2b1d15}.future-content-section[data-astro-cid-kh7btl4r]{margin-top:4rem;padding-top:2.5rem;border-top:1px solid rgba(200,200,200,.15)}@media (max-width: 768px){.content-container[data-astro-cid-kh7btl4r]{padding:0 20px}h2[data-astro-cid-kh7btl4r]{font-size:1.4rem;margin:2rem 0 1rem}p[data-astro-cid-kh7btl4r]{font-size:1rem;margin-bottom:1.5rem}.future-content-section[data-astro-cid-kh7btl4r]{margin-top:3rem;padding-top:2rem}.content-container[data-astro-cid-kh7btl4r]{padding:0 20px;max-width:90%}h2[data-astro-cid-kh7btl4r]{font-size:1.3rem}.intro-section[data-astro-cid-kh7btl4r] p[data-astro-cid-kh7btl4r]{font-size:1rem}ul[data-astro-cid-kh7btl4r]{padding-left:20px}}@media (max-width: 480px){.content-container[data-astro-cid-kh7btl4r]{padding:0 15px}}
</style></head> <body data-page="about" style="--color-accent: #3a2c23; --color-bg: #3a2c23; background-image: url(/images/limestone.png); background-color: #3a2c23;" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="blog-header" data-astro-cid-kh7btl4r> <h1 class="blog-title" data-astro-cid-kh7btl4r>about</h1> </div> <div class="content-container" data-astro-cid-kh7btl4r> <section class="about-section intro-section" data-astro-cid-kh7btl4r> <p data-astro-cid-kh7btl4r>This is the main introductory text about me. Keep it concise and engaging. Briefly touch upon what drives you or the purpose of this site.</p> <p data-astro-cid-kh7btl4r>This demonstrates how the components can be reused with different properties to change the appearance and behavior.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>My Journey</h2> <p data-astro-cid-kh7btl4r>Expand on your background, key experiences, and the path that led you here. Use storytelling elements if appropriate.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Skills & Expertise</h2> <p data-astro-cid-kh7btl4r>List or describe your core skills, tools you master, and areas you specialize in.</p> <ul data-astro-cid-kh7btl4r> <li data-astro-cid-kh7btl4r>Skill/Area 1: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 2: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 3: Brief description.</li> </ul> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Philosophy</h2> <p data-astro-cid-kh7btl4r>Discuss your approach to work, design principles, or core values that guide you.</p> </section> </div> <script>
      // Set up back button event
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    </script>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  