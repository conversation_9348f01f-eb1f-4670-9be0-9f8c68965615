<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>archives | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<link rel="stylesheet" href="/_astro/archive.BLJzJT9i.css"></head> <body data-page="blog" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <div class="blog-header" data-astro-cid-q37dchuc> <div class="blog-title" data-astro-cid-q37dchuc>archives</div> </div>  <div class="page-container" data-astro-cid-q37dchuc> <div class="blog-content" data-astro-cid-q37dchuc> <div class="archive-container" data-astro-cid-q37dchuc> <!-- Archive Title is displayed by the blog-header --> <!-- Controls: Sort Toggle --> <div class="controls-container" data-astro-cid-q37dchuc> <button id="sort-toggle-button" data-order="newest" data-astro-cid-q37dchuc> <span class="sort-label" data-astro-cid-q37dchuc>Sort: Newest</span> <svg class="sort-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-q37dchuc><line x1="12" y1="5" x2="12" y2="19" data-astro-cid-q37dchuc></line><polyline points="19 12 12 19 5 12" data-astro-cid-q37dchuc></polyline></svg> </button> </div> <!-- Timeline --> <div id="timeline-container" class="timeline-container" data-astro-cid-q37dchuc> <div class="timeline" data-astro-cid-q37dchuc></div> </div> <!-- Placeholder for infinite scroll loader if needed later --> <div class="loader-placeholder" style="text-align: center; margin-top: 2rem; display: none;" data-astro-cid-q37dchuc>Loading...</div> </div> </div> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  <script>(function(){const allPosts = [{"id":"68024d2ea9db3b2a9cc5d026","uuid":"ad757d58-7a6b-4070-8bd1-217ac38dda03","title":"Dream Log – Synesthetic Ocean","slug":"dream-log-synesthetic-ocean","html":"<p>Was floating above a phosphorescent ocean, but each wave had a frequency. I heard sound as color, felt motion as thought. Might represent how I subconsciously bind domains.</p>","comment_id":"68024d2ea9db3b2a9cc5d026","feature_image":null,"featured":false,"visibility":"public","created_at":"2025-04-18 13:01:34","updated_at":"2025-04-18 13:01:47","published_at":"2025-04-18 13:01:47","custom_excerpt":"Vivid, symbolic dream with layered sensory patterns.","codeinjection_head":null,"codeinjection_foot":null,"custom_template":null,"canonical_url":null,"url":"/dream-log-synesthetic-ocean/","excerpt":"Vivid, symbolic dream with layered sensory patterns.","reading_time":1,"access":"public","authors":[{"id":"1","name":"Approx","slug":"approx","profile_image":null,"cover_image":null,"bio":null,"website":null,"location":null,"facebook":null,"twitter":null,"meta_title":null,"meta_description":null,"url":"/author/approx/"}],"primary_author":{"id":"1","name":"Approx","slug":"approx","profile_image":null,"cover_image":null,"bio":null,"website":null,"location":null,"facebook":null,"twitter":null,"meta_title":null,"meta_description":null,"url":"/author/approx/"},"tags":[{"id":"68024c08a9db3b2a9cc5d001","name":"archive","slug":"archive","description":null,"feature_image":null,"visibility":"public","meta_title":null,"meta_description":null,"og_image":null,"og_title":null,"og_description":null,"twitter_image":null,"twitter_title":null,"twitter_description":null,"codeinjection_head":null,"codeinjection_foot":null,"canonical_url":null,"accent_color":"#715ff7","url":"/tag/archive/"}],"primary_tag":{"id":"68024c08a9db3b2a9cc5d001","name":"archive","slug":"archive","description":null,"feature_image":null,"visibility":"public","meta_title":null,"meta_description":null,"og_image":null,"og_title":null,"og_description":null,"twitter_image":null,"twitter_title":null,"twitter_description":null,"codeinjection_head":null,"codeinjection_foot":null,"canonical_url":null,"accent_color":"#715ff7","url":"/tag/archive/"}},{"id":"68024d0aa9db3b2a9cc5d01b","uuid":"029792b3-498b-40ba-870b-8c17de0dcec6","title":"Flow State Diary – Day 1","slug":"flow-state-diary-day-1","html":"<p>Intentional silence after high-focus work generated emotional residue. No music, no scrolling. Flow lingered. Writing this to trace what triggers it best.</p>","comment_id":"68024d0aa9db3b2a9cc5d01b","feature_image":null,"featured":false,"visibility":"public","created_at":"2025-04-18 13:00:58","updated_at":"2025-04-18 13:01:22","published_at":"2025-04-18 13:01:22","custom_excerpt":"First log entry of conscious flow manipulation.","codeinjection_head":null,"codeinjection_foot":null,"custom_template":null,"canonical_url":null,"url":"/flow-state-diary-day-1/","excerpt":"First log entry of conscious flow manipulation.","reading_time":1,"access":"public","authors":[{"id":"1","name":"Approx","slug":"approx","profile_image":null,"cover_image":null,"bio":null,"website":null,"location":null,"facebook":null,"twitter":null,"meta_title":null,"meta_description":null,"url":"/author/approx/"}],"primary_author":{"id":"1","name":"Approx","slug":"approx","profile_image":null,"cover_image":null,"bio":null,"website":null,"location":null,"facebook":null,"twitter":null,"meta_title":null,"meta_description":null,"url":"/author/approx/"},"tags":[{"id":"68024c08a9db3b2a9cc5d001","name":"archive","slug":"archive","description":null,"feature_image":null,"visibility":"public","meta_title":null,"meta_description":null,"og_image":null,"og_title":null,"og_description":null,"twitter_image":null,"twitter_title":null,"twitter_description":null,"codeinjection_head":null,"codeinjection_foot":null,"canonical_url":null,"accent_color":"#715ff7","url":"/tag/archive/"}],"primary_tag":{"id":"68024c08a9db3b2a9cc5d001","name":"archive","slug":"archive","description":null,"feature_image":null,"visibility":"public","meta_title":null,"meta_description":null,"og_image":null,"og_title":null,"og_description":null,"twitter_image":null,"twitter_title":null,"twitter_description":null,"codeinjection_head":null,"codeinjection_foot":null,"canonical_url":null,"accent_color":"#715ff7","url":"/tag/archive/"}}];

  document.addEventListener('DOMContentLoaded', () => {
    const timelineContainer = document.getElementById('timeline-container');
    const sortToggleButton = document.getElementById('sort-toggle-button');
    const loaderPlaceholder = document.querySelector('.loader-placeholder');
    const sortLabel = document.querySelector('.sort-label');
    const sortIcon = document.querySelector('.sort-icon');

    let currentSortOrder = 'newest'; // 'newest' or 'oldest'

    // Function to render the timeline
    const renderTimeline = () => {
      // 1. Sort posts based on currentSortOrder
      const sortedPosts = [...allPosts].sort((a, b) => {
        const dateA = new Date(a.published_at || a.data?.pubDatetime || a.data?.pubDate).getTime();
        const dateB = new Date(b.published_at || b.data?.pubDatetime || b.data?.pubDate).getTime();
        return currentSortOrder === 'newest' ? dateB - dateA : dateA - dateB;
      });

      // 2. Group posts by year
      const postsByYear = sortedPosts.reduce((acc, post) => {
        const year = new Date(post.published_at || post.data?.pubDatetime || post.data?.pubDate).getFullYear();
        if (!acc[year]) {
          acc[year] = [];
        }
        acc[year].push(post);
        return acc;
      }, {});

      // 3. Generate HTML
      let timelineHTML = '';
      const years = Object.keys(postsByYear).sort((a, b) => (currentSortOrder === 'newest' ? b - a : a - b));

      if (years.length === 0) {
        timelineHTML = '<p>No posts found in the archive.</p>';
      } else {
        timelineHTML = '<div class="timeline">'; // Start timeline wrapper

        years.forEach(year => {
          timelineHTML += `<div class="timeline-year-section">
                             <h2 class="timeline-year-marker">${year}</h2>
                             <div class="timeline-posts-for-year">`;

          postsByYear[year].forEach(post => {
            const postDate = new Date(post.published_at || post.data?.pubDatetime || post.data?.pubDate);
            const formattedDate = postDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            const title = post.title || post.data?.title;
            const excerpt = post.excerpt || post.data?.excerpt || '';
            const postUrl = `/blog/${post.slug}`;
            const postId = post.id || post.slug;

            timelineHTML += `
              <div class="timeline-item" id="post-${postId}">
                <div class="timeline-marker" aria-label="${title}" tabindex="0" data-post-id="post-${postId}"></div>
                <a href="${postUrl}" class="timeline-item-link">
                  <div class="timeline-content">
                    <span class="timeline-item-date">${formattedDate}</span>
                    <h3 class="timeline-item-title">${title}</h3>
                    <p class="timeline-item-excerpt">${excerpt}</p>
                  </div>
                </a>
              </div>`;
          });

          timelineHTML += `</div></div>`; // Close posts-for-year and year-section
        });

        timelineHTML += '</div>'; // End timeline wrapper
      }

      // 4. Update DOM
      timelineContainer.querySelector('.timeline').innerHTML = timelineHTML;

      // Stagger item animations
      const items = timelineContainer.querySelectorAll('.timeline-item');
      items.forEach((item, idx) => {
        item.style.setProperty('--delay', `${idx * 0.1}s`);
      });
      
      // Add click event to markers to navigate to post
      const markers = document.querySelectorAll('.timeline-marker');
      markers.forEach(marker => {
        marker.addEventListener('click', (e) => {
          const postId = marker.getAttribute('data-post-id');
          const link = document.querySelector(`#${postId} .timeline-item-link`);
          if (link) link.click();
        });
        
        // Also add keyboard accessibility
        marker.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            const postId = marker.getAttribute('data-post-id');
            const link = document.querySelector(`#${postId} .timeline-item-link`);
            if (link) link.click();
          }
        });
      });
    };

    // --- Event Listeners ---

    // Toggle Sort Order
    sortToggleButton.addEventListener('click', () => {
      currentSortOrder = (currentSortOrder === 'newest') ? 'oldest' : 'newest';
      sortToggleButton.dataset.order = currentSortOrder;
      sortLabel.textContent = `Sort: ${currentSortOrder === 'newest' ? 'Newest' : 'Oldest'}`;
      sortIcon.classList.toggle('asc', currentSortOrder === 'oldest');
      renderTimeline();
    });

    // --- Initial Render ---
    renderTimeline();

  });
})();</script> 