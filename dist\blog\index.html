<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Blog | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<link rel="stylesheet" href="/_astro/blog.D62fjZrA.css">
<style>.tag-link[data-astro-cid-blwjyjpt]{display:inline-block;padding:.2rem .6rem;border-radius:1rem;text-decoration:none;border:1px solid rgba(var(--color-accent-rgb),.3);background-color:rgba(var(--color-accent-rgb),.05);color:rgba(var(--color-accent-rgb),.8);transition:background-color .2s ease,border-color .2s ease,color .2s ease;font-family:Georgia Custom,Georgia,serif}.tag-link[data-astro-cid-blwjyjpt]:hover{background-color:rgba(var(--color-accent-rgb),.15);border-color:rgba(var(--color-accent-rgb),.5);color:rgba(var(--color-accent-rgb),1)}.tag-sm[data-astro-cid-blwjyjpt]{font-size:.7rem}.tag-lg[data-astro-cid-blwjyjpt]{font-size:.8rem}
.blog-post-card[data-astro-cid-f45vxlzk]{opacity:0;animation:fade-in-card .5s ease-out forwards;margin:1.5rem 0;background-color:#14141499;border:1px solid rgba(255,255,255,.05);border-radius:.75rem;padding:1.5rem;list-style:none;box-shadow:var(--shadow-card);transition:transform var(--transition-duration) var(--easing-standard),box-shadow var(--transition-duration) var(--easing-standard),border-color var(--transition-duration) var(--easing-standard)}.blog-post-card[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px);box-shadow:var(--shadow-card-hover-light);border-color:#ffffff1a}.blog-post-card[data-astro-cid-f45vxlzk]:last-child{margin-bottom:1.5rem}.post-link[data-astro-cid-f45vxlzk]{display:block;transition:transform var(--transition-duration) var(--easing-standard);text-decoration:none;color:var(--blog-text)}.post-link[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px)}.post-date[data-astro-cid-f45vxlzk]{display:block;font-size:.8rem;color:#f0f0f099;margin-bottom:.5rem;font-family:Georgia Custom,Georgia,serif}.post-title[data-astro-cid-f45vxlzk]{font-size:1.4rem;font-weight:600;margin:0 0 .75rem;color:var(--blog-text);font-family:Georgia Custom,Georgia,serif;line-height:1.3}.post-description[data-astro-cid-f45vxlzk]{font-size:.95rem;line-height:1.6;margin-bottom:1rem;color:#f0f0f0cc;font-family:Georgia Custom,Georgia,serif}.post-tags[data-astro-cid-f45vxlzk]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:1rem}@keyframes fade-in-card{0%{opacity:0;transform:translateY(4px)}to{opacity:1;transform:translateY(0)}}
@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0;mix-blend-mode:plus-lighter}to{opacity:1;mix-blend-mode:plus-lighter}}@keyframes astroFadeOut{0%{opacity:1;mix-blend-mode:plus-lighter}to{opacity:0;mix-blend-mode:plus-lighter}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}
</style><style>[data-astro-transition-scope="astro-w2v4zgte-1"] { view-transition-name: toolbox-thinking-upgrade-your-mental-api; }@layer astro { ::view-transition-old(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-2"] { view-transition-name: why-metacognition-outperforms-motivation; }@layer astro { ::view-transition-old(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-3"] { view-transition-name: test-3; }@layer astro { ::view-transition-old(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-4"] { view-transition-name: test-2; }@layer astro { ::view-transition-old(test-2) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(test-2) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(test-2) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(test-2) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-5"] { view-transition-name: the-test-file; }@layer astro { ::view-transition-old(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style></head> <body data-page="blog" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <h1 class="blog-header" data-astro-cid-ijnerlr2><span class="blog-title" data-astro-cid-ijnerlr2>blog</span></h1>  <div class="page-container" data-astro-cid-ijnerlr2> <!-- Left Sidebar --> <div class="blog-sidebar" data-astro-cid-ijnerlr2> <!-- Search Bar --> <div class="search-container sidebar-section" style="display: flex; justify-content: center;" data-astro-cid-ijnerlr2> <a href="/search" class="search-link sidebar-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <circle cx="11" cy="11" r="8" data-astro-cid-ijnerlr2></circle> <line x1="21" y1="21" x2="16.65" y2="16.65" data-astro-cid-ijnerlr2></line> </svg> <span data-astro-cid-ijnerlr2>search</span> </a> </div> <!-- Archive Button --> <div class="archive-container sidebar-section" data-astro-cid-ijnerlr2> <a href="/blog/archive" class="archive-link sidebar-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M6 3h12l3 6v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9l3-6z" data-astro-cid-ijnerlr2></path> <path d="M10 12h4" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>archives</span> </a> </div> <!-- Subscribe --> <div class="subscribe-container sidebar-section" data-astro-cid-ijnerlr2> <a href="mailto:<EMAIL>" class="subscribe-link sidebar-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M3 8l7.333 4.667a2 2 0 0 0 2.334 0L20 8" data-astro-cid-ijnerlr2></path> <rect x="3" y="6" width="18" height="12" rx="2" ry="2" data-astro-cid-ijnerlr2></rect> </svg> <span data-astro-cid-ijnerlr2>subscribe by email</span> </a> </div> <!-- Tags Filter (Collapsible) --> <div class="tags-container" data-astro-cid-ijnerlr2> <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list" data-astro-cid-ijnerlr2> <span class="tags-title" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z" data-astro-cid-ijnerlr2></path> <path d="M6 9.01V9" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>tags</span> </span> <span class="toggle-icon" data-astro-cid-ijnerlr2> <span class="line hor" data-astro-cid-ijnerlr2></span> <span class="line vert" data-astro-cid-ijnerlr2></span> </span> </button> <div class="tags-list" id="tags-list" data-astro-cid-ijnerlr2> <a href="/tags" class="tag-link all-tags-link" data-astro-cid-ijnerlr2>all tags</a> <a href="/blog/tag/ai" class="tag-link" data-astro-cid-ijnerlr2>ai</a><a href="/blog/tag/rsrc" class="tag-link" data-astro-cid-ijnerlr2>rsrc</a> </div> </div> </div> <!-- Main Content --> <div class="blog-content" data-astro-cid-ijnerlr2>  <ul id="blog-post-list" data-astro-cid-ijnerlr2> <li class="post-list-item" data-tags="blog" data-astro-cid-ijnerlr2> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/toolbox-thinking-upgrade-your-mental-api/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:30:48.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-1"> Toolbox Thinking: Upgrade Your Mental API </h3> <p class="post-description" data-astro-cid-f45vxlzk>You&#39;re only as powerful as the tools you know how to use under pressure.
</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li><li class="post-list-item" data-tags="blog" data-astro-cid-ijnerlr2> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/why-metacognition-outperforms-motivation/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:30:17.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-2"> Why Metacognition Outperforms Motivation </h3> <p class="post-description" data-astro-cid-f45vxlzk>Metacognition isn&#39;t a supplement to motivation—it&#39;s the upgrade.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li><li class="post-list-item" data-tags="blog" data-astro-cid-ijnerlr2> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/test-3/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-16T07:04:20.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 16, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-3"> Test 3 </h3> <p class="post-description" data-astro-cid-f45vxlzk>Yes</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li><li class="post-list-item" data-tags="" data-astro-cid-ijnerlr2> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/test-2/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-16T06:55:29.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 16, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-4"> test 2 </h3> <p class="post-description" data-astro-cid-f45vxlzk>dsadasdasdasds</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li><li class="post-list-item" data-tags="" data-astro-cid-ijnerlr2> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/the-test-file/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-16T06:54:20.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 16, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-5"> The test file. </h3> <p class="post-description" data-astro-cid-f45vxlzk>Boom. a test.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li> </ul>  <div class="pagination" data-astro-cid-ijnerlr2>  <div class="pagination-info" data-astro-cid-ijnerlr2>Page 1 of 2</div> <a href="/blog/page/2" class="pagination-link next-page" data-astro-cid-ijnerlr2>Older Posts →</a> </div>  <div class="all-blogs-link-container" style="text-align:center; margin-top: 2rem;" data-astro-cid-ijnerlr2> <a href="/blog/all" class="all-blogs-link" data-astro-cid-ijnerlr2>View All Blogs</a> </div>  </div> </div> <script>
  // Tag drawer toggle functionality
  document.getElementById('tags-toggle')?.addEventListener('click', function() {
    const container = this.closest('.tags-container');
    const isExpanded = this.getAttribute('aria-expanded') === 'true';
    
    // Toggle container state
    container.classList.toggle('open');
    this.setAttribute('aria-expanded', !isExpanded);
  });
</script> <script>
  // Fade-up animation observer
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('in-view');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  document.querySelectorAll('[data-animate="fade-up"]').forEach(el => observer.observe(el));
</script>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  