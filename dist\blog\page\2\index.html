<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Blog - Page 2 | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_page_.C4qPaefH.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css"></head> <body data-page="blog" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <div class="blog-header" data-astro-cid-hzrsv7ue> <div class="blog-title" data-astro-cid-hzrsv7ue>blog</div> </div>  <div class="page-container" data-astro-cid-hzrsv7ue> <!-- Left Sidebar --> <div class="blog-sidebar" data-astro-cid-hzrsv7ue> <!-- Search Bar --> <div class="search-container sidebar-section" data-astro-cid-hzrsv7ue> <a href="/search" class="search-link" data-astro-cid-hzrsv7ue> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-hzrsv7ue> <circle cx="11" cy="11" r="8" data-astro-cid-hzrsv7ue></circle> <line x1="21" y1="21" x2="16.65" y2="16.65" data-astro-cid-hzrsv7ue></line> </svg> <span data-astro-cid-hzrsv7ue>search</span> </a> </div> <!-- Archive Button --> <div class="archive-container sidebar-section" data-astro-cid-hzrsv7ue> <a href="/blog/timeline" class="archive-link" data-astro-cid-hzrsv7ue> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-hzrsv7ue> <path d="M3 3v18h18" data-astro-cid-hzrsv7ue></path> <path d="M7 17l4-4 4 4 4-4" data-astro-cid-hzrsv7ue></path> <path d="M7 11l4-4 4 4 4-4" data-astro-cid-hzrsv7ue></path> </svg> <span data-astro-cid-hzrsv7ue>archive</span> </a> </div> <!-- Subscribe --> <div class="subscribe-container sidebar-section" data-astro-cid-hzrsv7ue> <a href="#" class="subscribe-link" data-astro-cid-hzrsv7ue>subscribe by email</a> </div> </div> <!-- Main Content --> <div class="blog-content" data-astro-cid-hzrsv7ue> <!-- Posts List --> <div class="posts-list" data-astro-cid-hzrsv7ue> <div class="post-item first-post" data-astro-cid-hzrsv7ue> <h2 class="post-title" data-astro-cid-hzrsv7ue> <a href="/blog/systems-thinking-for-everyday-decisions" data-astro-cid-hzrsv7ue>Systems Thinking for Everyday Decisions</a> </h2> <div class="post-meta" data-astro-cid-hzrsv7ue> <span class="post-date" data-astro-cid-hzrsv7ue> April 7, 2025 </span> <span class="reading-time" data-astro-cid-hzrsv7ue>
• 1 min read
</span> </div> <p class="post-description" data-astro-cid-hzrsv7ue></p> <div class="post-tags" data-astro-cid-hzrsv7ue>  </div> <a href="/blog/systems-thinking-for-everyday-decisions" class="read-more" data-astro-cid-hzrsv7ue>
Read Post <span class="read-more-arrow" data-astro-cid-hzrsv7ue>→</span> </a> </div> </div> <!-- Ghost-style Pagination --> <div class="pagination" data-astro-cid-hzrsv7ue> <a href="/blog/page/1" class="pagination-link prev-page" data-astro-cid-hzrsv7ue> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-hzrsv7ue> <path d="M19 12H5" data-astro-cid-hzrsv7ue></path> <path d="M12 19l-7-7 7-7" data-astro-cid-hzrsv7ue></path> </svg> <span data-astro-cid-hzrsv7ue>Newer Posts</span> </a> <div class="pagination-info" data-astro-cid-hzrsv7ue>
Page 2 of 2 </div> <span class="pagination-link disabled" data-astro-cid-hzrsv7ue> <span data-astro-cid-hzrsv7ue>Older Posts</span> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-hzrsv7ue> <path d="M5 12h14" data-astro-cid-hzrsv7ue></path> <path d="M12 5l7 7-7 7" data-astro-cid-hzrsv7ue></path> </svg> </span> </div> </div> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  