<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Tag: archive | Blog | PVB</title><link rel="stylesheet" href="/_astro/about.g5V2UwZZ.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>.tag-link[data-astro-cid-blwjyjpt]{display:inline-block;padding:.2rem .6rem;border-radius:1rem;text-decoration:none;border:1px solid rgba(var(--color-accent-rgb),.3);background-color:rgba(var(--color-accent-rgb),.05);color:rgba(var(--color-accent-rgb),.8);transition:background-color .2s ease,border-color .2s ease,color .2s ease;font-family:Georgia Custom,Georgia,serif}.tag-link[data-astro-cid-blwjyjpt]:hover{background-color:rgba(var(--color-accent-rgb),.15);border-color:rgba(var(--color-accent-rgb),.5);color:rgba(var(--color-accent-rgb),1)}.tag-sm[data-astro-cid-blwjyjpt]{font-size:.7rem}.tag-lg[data-astro-cid-blwjyjpt]{font-size:.8rem}
.blog-post-card[data-astro-cid-f45vxlzk]{opacity:0;animation:fade-in-card .5s ease-out forwards;margin:1.5rem 0;background-color:#14141499;border:1px solid rgba(255,255,255,.05);border-radius:.75rem;padding:1.5rem;list-style:none;box-shadow:var(--shadow-card);transition:transform var(--transition-duration) var(--easing-standard),box-shadow var(--transition-duration) var(--easing-standard),border-color var(--transition-duration) var(--easing-standard)}.blog-post-card[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px);box-shadow:var(--shadow-card-hover-light);border-color:#ffffff1a}.blog-post-card[data-astro-cid-f45vxlzk]:last-child{margin-bottom:1.5rem}.post-link[data-astro-cid-f45vxlzk]{display:block;transition:transform var(--transition-duration) var(--easing-standard);text-decoration:none;color:var(--blog-text)}.post-link[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px)}.post-date[data-astro-cid-f45vxlzk]{display:block;font-size:.8rem;color:#f0f0f099;margin-bottom:.5rem;font-family:Georgia Custom,Georgia,serif}.post-title[data-astro-cid-f45vxlzk]{font-size:1.4rem;font-weight:600;margin:0 0 .75rem;color:var(--blog-text);font-family:Georgia Custom,Georgia,serif;line-height:1.3}.post-description[data-astro-cid-f45vxlzk]{font-size:.95rem;line-height:1.6;margin-bottom:1rem;color:#f0f0f0cc;font-family:Georgia Custom,Georgia,serif}.post-tags[data-astro-cid-f45vxlzk]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:1rem}@keyframes fade-in-card{0%{opacity:0;transform:translateY(4px)}to{opacity:1;transform:translateY(0)}}
@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0;mix-blend-mode:plus-lighter}to{opacity:1;mix-blend-mode:plus-lighter}}@keyframes astroFadeOut{0%{opacity:1;mix-blend-mode:plus-lighter}to{opacity:0;mix-blend-mode:plus-lighter}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}
</style><style>[data-astro-transition-scope="astro-w2v4zgte-1"] { view-transition-name: dream-log-synesthetic-ocean; }@layer astro { ::view-transition-old(dream-log-synesthetic-ocean) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(dream-log-synesthetic-ocean) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(dream-log-synesthetic-ocean) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(dream-log-synesthetic-ocean) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-1"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-2"] { view-transition-name: flow-state-diary-day-1; }@layer astro { ::view-transition-old(flow-state-diary-day-1) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(flow-state-diary-day-1) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(flow-state-diary-day-1) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(flow-state-diary-day-1) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style></head> <body data-page="blog" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="blog-header" data-astro-cid-trjsnkp3> <div class="blog-title" data-astro-cid-trjsnkp3>blog</div> </div> <div class="page-container" data-astro-cid-trjsnkp3> <div class="blog-sidebar" data-astro-cid-trjsnkp3>   <div class="search-container sidebar-section" data-astro-cid-trjsnkp3> <a href="/search" class="search-link sidebar-link" data-astro-cid-trjsnkp3> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-trjsnkp3> <circle cx="11" cy="11" r="8" data-astro-cid-trjsnkp3></circle> <line x1="21" y1="21" x2="16.65" y2="16.65" data-astro-cid-trjsnkp3></line> </svg> <span data-astro-cid-trjsnkp3>search</span> </a> </div> <div class="archive-container sidebar-section" data-astro-cid-trjsnkp3> <a href="/blog/archives" class="archive-link sidebar-link" data-astro-cid-trjsnkp3> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-trjsnkp3> <path d="M3 3v18h18" data-astro-cid-trjsnkp3></path> <path d="M7 17l4-4 4 4 4-4" data-astro-cid-trjsnkp3></path> <path d="M7 11l4-4 4 4 4-4" data-astro-cid-trjsnkp3></path> </svg> <span data-astro-cid-trjsnkp3>archives</span> </a> </div> <div class="subscribe-container sidebar-section" data-astro-cid-trjsnkp3> <a href="#" class="subscribe-link sidebar-link" data-astro-cid-trjsnkp3>subscribe by email</a> </div>  <div class="tags-container" data-astro-cid-trjsnkp3> <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list" data-astro-cid-trjsnkp3> <span class="tags-title" data-astro-cid-trjsnkp3> <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-trjsnkp3> <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z" data-astro-cid-trjsnkp3></path> <path d="M6 9.01V9" data-astro-cid-trjsnkp3></path> </svg> <span data-astro-cid-trjsnkp3>tags</span> </span> <span class="toggle-icon" data-astro-cid-trjsnkp3> <span class="line hor" data-astro-cid-trjsnkp3></span> <span class="line vert" data-astro-cid-trjsnkp3></span> </span> </button> <div class="tags-list" id="tags-list" data-astro-cid-trjsnkp3> <a href="/tags" class="tag-link all-tags-link" data-astro-cid-trjsnkp3>all tags</a>     </div> </div> </div> <div class="blog-content" data-astro-cid-trjsnkp3> <h1 class="section-title" data-astro-cid-trjsnkp3>Posts tagged with "archive"</h1> <ul id="blog-post-list" data-astro-cid-trjsnkp3> <li class="post-list-item" data-astro-cid-trjsnkp3> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/dream-log-synesthetic-ocean/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:31:47.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-1"> Dream Log – Synesthetic Ocean </h3> <p class="post-description" data-astro-cid-f45vxlzk>Vivid, symbolic dream with layered sensory patterns.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li><li class="post-list-item" data-astro-cid-trjsnkp3> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/flow-state-diary-day-1/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:31:22.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-2"> Flow State Diary – Day 1 </h3> <p class="post-description" data-astro-cid-f45vxlzk>First log entry of conscious flow manipulation.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </li> </ul> </div> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>   <script>
  // Add necessary JS for sidebar toggle if not a component
</script>