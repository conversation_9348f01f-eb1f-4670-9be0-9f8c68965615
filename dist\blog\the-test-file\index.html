<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>The test file.</title><link rel="stylesheet" href="/_astro/about.g5V2UwZZ.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>.tag-link[data-astro-cid-blwjyjpt]{display:inline-block;padding:.2rem .6rem;border-radius:1rem;text-decoration:none;border:1px solid rgba(var(--color-accent-rgb),.3);background-color:rgba(var(--color-accent-rgb),.05);color:rgba(var(--color-accent-rgb),.8);transition:background-color .2s ease,border-color .2s ease,color .2s ease;font-family:Georgia Custom,Georgia,serif}.tag-link[data-astro-cid-blwjyjpt]:hover{background-color:rgba(var(--color-accent-rgb),.15);border-color:rgba(var(--color-accent-rgb),.5);color:rgba(var(--color-accent-rgb),1)}.tag-sm[data-astro-cid-blwjyjpt]{font-size:.7rem}.tag-lg[data-astro-cid-blwjyjpt]{font-size:.8rem}
</style>
<link rel="stylesheet" href="/_astro/_slug_.D4c09drC.css">
<style>@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0;mix-blend-mode:plus-lighter}to{opacity:1;mix-blend-mode:plus-lighter}}@keyframes astroFadeOut{0%{opacity:1;mix-blend-mode:plus-lighter}to{opacity:0;mix-blend-mode:plus-lighter}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}
</style><style>[data-astro-transition-scope="astro-a4rjachf-1"] { view-transition-name: the-test-file; }@layer astro { ::view-transition-old(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(the-test-file) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-a4rjachf-1"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-a4rjachf-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-a4rjachf-1"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-a4rjachf-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-a4rjachf-1"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-a4rjachf-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-a4rjachf-1"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-a4rjachf-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style></head> <body data-page="blog-post" style="--color-accent: #f0f0f0; --color-bg: rgba(0, 0, 0, 0.85); background-image: url(/images/blackgranite.png); background-color: rgba(0, 0, 0, 0.85);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <div class="blog-header" data-astro-cid-bvzihdzo> <div class="blog-title" data-astro-cid-bvzihdzo>blog</div> </div>   <button id="toc-toggle" class="toc-toggle" aria-expanded="false" aria-controls="toc-panel" data-astro-cid-bvzihdzo> <span class="toc-icon-dots" data-astro-cid-bvzihdzo> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> </span> </button>  <div id="toc-panel" class="toc-panel" aria-hidden="true" data-astro-cid-bvzihdzo> <div class="toc-panel-inner" data-astro-cid-bvzihdzo> <h3 class="toc-panel-title" data-astro-cid-bvzihdzo>Table of Contents</h3> <button class="toc-close" id="toc-close" aria-label="Close Table of Contents" data-astro-cid-bvzihdzo> <span class="toc-close-arrow" data-astro-cid-bvzihdzo>›</span> </button> <div id="toc-content" class="toc-content" data-astro-cid-bvzihdzo> <!-- The TOC content will be populated via JavaScript --> </div> </div> </div>  <article class="blog-post" data-astro-cid-bvzihdzo> <header class="post-header" data-astro-cid-bvzihdzo> <h1 class="post-title" data-astro-cid-bvzihdzo data-astro-transition-scope="astro-a4rjachf-1"> The test file. </h1> <div class="post-date" data-astro-cid-bvzihdzo>April 16, 2025</div> </header> <div class="post-content" data-astro-cid-bvzihdzo><p>Boom. a test.</p></div>  <div class="post-footer" data-astro-cid-bvzihdzo> <div class="post-tags" data-astro-cid-bvzihdzo>  </div> <div class="post-actions" data-astro-cid-bvzihdzo> <a href="/blog" class="return-link" data-astro-cid-bvzihdzo>← blog</a> <a href="#" class="subscribe-link" data-astro-cid-bvzihdzo>subscribe by email</a> </div> </div> </article> <script>
    document.addEventListener('DOMContentLoaded', function() {
      const tocToggle = document.getElementById('toc-toggle');
      const tocPanel = document.getElementById('toc-panel');
      const tocContent = document.getElementById('toc-content');
      const tocClose = document.getElementById('toc-close');
      const blogPost = document.querySelector('.blog-post');
      const isWorkPost = document.body.getAttribute('data-page') === 'work-post';

      // Set return link text based on page type
      const returnLink = document.querySelector('.return-link');
      if (returnLink && isWorkPost) {
        returnLink.textContent = '← Work';
        returnLink.setAttribute('href', '/work');
      }

      // Prevent automatic scrolling to bottom
      window.history.scrollRestoration = 'manual';

      // Generate TOC from headings in the post
      function generateToc() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (headings.length === 0) {
          tocContent.innerHTML = '<p class="toc-empty">No sections found</p>';
          // Close TOC if no sections
          tocToggle.classList.remove('active');
          tocToggle.setAttribute('aria-expanded', 'false');
          tocPanel.classList.remove('active');
          tocPanel.setAttribute('aria-hidden', 'true');
          return;
        }

        const tocHtml = document.createElement('div');
        tocHtml.classList.add('toc-list-container');

        headings.forEach((heading, index) => {
          // Add an ID to the heading if it doesn't have one
          if (!heading.id) {
            heading.id = `heading-${index}`;
          }

          const item = document.createElement('div');
          item.classList.add(`toc-item`);
          item.classList.add(`toc-${heading.tagName.toLowerCase()}`);

          const a = document.createElement('a');
          a.href = `#${heading.id}`;
          a.textContent = heading.textContent;
          a.style.color = '#ffffff'; // Force white color
          a.style.textDecoration = 'none'; // Force no underline

          a.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all TOC links
            document.querySelectorAll('.toc-content a').forEach(link => {
              link.classList.remove('active');
            });

            // Add active class to clicked link
            a.classList.add('active');

            // Smooth scroll to the heading
            heading.scrollIntoView({ behavior: 'smooth' });

            // Add a highlight effect to the heading
            heading.classList.add('highlight');
            setTimeout(() => {
              heading.classList.remove('highlight');
            }, 1500);

            // On mobile, close the TOC after clicking
            if (window.innerWidth < 768) {
              toggleToc(false);
            }
          });

          item.appendChild(a);
          tocHtml.appendChild(item);
        });

        tocContent.innerHTML = '';
        tocContent.appendChild(tocHtml);

        // Initial styling of all links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.style.color = '#ffffff';
          link.style.textDecoration = 'none';
        });

        // Highlight the current section on scroll
        window.addEventListener('scroll', highlightCurrentSection);

        // Initial highlight
        setTimeout(highlightCurrentSection, 100);
      }

      // Highlight the current section in the TOC
      function highlightCurrentSection() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (!headings.length) return;

        // Find the heading that's currently in view
        let currentHeading = null;

        // Calculate viewport height and set threshold for heading detection
        const viewportHeight = window.innerHeight;
        const threshold = viewportHeight * 0.25; // 25% from the top (reduced from 30%)

        // Check which heading is most visible in the viewport
        let maxVisibleHeight = 0;
        let mostVisibleHeading = null;

        headings.forEach(heading => {
          const rect = heading.getBoundingClientRect();

          // If the heading is near the top of the viewport (within threshold)
          if (rect.top < threshold && rect.top > -50) { // Allow slight scrolling past
            currentHeading = heading;
            return; // Exit early, we found our heading
          }
          
          // If no heading is in the threshold zone, use the most visible one
          if (rect.top > 0 && rect.bottom < viewportHeight) {
            const visibleHeight = Math.min(viewportHeight, rect.bottom) - Math.max(0, rect.top);
            if (visibleHeight > maxVisibleHeight) {
              maxVisibleHeight = visibleHeight;
              mostVisibleHeading = heading;
            }
          }
        });

        // If we found a heading in the threshold, use it
        if (currentHeading) {
          // Keep current heading
        }
        // If no heading is in threshold but we found a visible heading, use that
        else if (mostVisibleHeading) {
          currentHeading = mostVisibleHeading;
        }
        // If we've scrolled down but found no headings, use the last one
        else if (window.scrollY > 0 && headings.length > 0) {
          let lastVisibleHeading = null;
          for (let i = headings.length - 1; i >= 0; i--) {
            if (headings[i].getBoundingClientRect().top < 0) {
              lastVisibleHeading = headings[i];
              break;
            }
          }
          currentHeading = lastVisibleHeading || headings[0];
        }
        // Otherwise use the first one
        else if (headings.length > 0) {
          currentHeading = headings[0];
        }

        // Remove active class from all TOC links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.classList.remove('active');
        });

        // If we found a current heading, highlight its TOC link
        if (currentHeading) {
          const id = currentHeading.id;
          const tocLink = document.querySelector(`.toc-content a[href="#${id}"]`);
          if (tocLink) {
            tocLink.classList.add('active');

            // Ensure the active link is visible in the TOC panel if it's open
            if (tocPanel.classList.contains('active')) {
              const tocPanelInner = document.querySelector('.toc-panel-inner');
              if (tocPanelInner) {
                const linkRect = tocLink.getBoundingClientRect();
                const panelRect = tocPanelInner.getBoundingClientRect();

                // If link is outside the visible area of the panel
                if (linkRect.top < panelRect.top || linkRect.bottom > panelRect.bottom) {
                  tocLink.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }
            }
          }
        }
      }

      // Toggle TOC panel with smooth transitions
      function toggleToc(open) {
        const isOpen = open !== undefined ? open : !tocToggle.classList.contains('active');
        if (isOpen) {
          generateToc();
        }
        tocToggle.classList.toggle('active', isOpen);
        tocToggle.setAttribute('aria-expanded', isOpen.toString());
        tocPanel.classList.toggle('active', isOpen);
        tocPanel.setAttribute('aria-hidden', (!isOpen).toString());
      }

      if (tocToggle && tocPanel) {
        tocToggle.addEventListener('click', () => toggleToc());
        tocClose?.addEventListener('click', () => toggleToc(false));
        // Auto-open TOC by default on post pages
        toggleToc(true);
      }

      // Close TOC when clicking outside
      document.addEventListener('click', (e) => {
        if (tocPanel.classList.contains('active') &&
            !tocPanel.contains(e.target) &&
            !tocToggle.contains(e.target) &&
            !tocClose.contains(e.target)) {
          toggleToc(false);
        }
      });

      // Close TOC with ESC key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && tocPanel.classList.contains('active')) {
          toggleToc(false);
        }
      });

      // Set up back button event
      const backButton = document.querySelector('.nav-circle.top-left');
      if (backButton) {
        backButton.addEventListener('click', () => {
          window.history.back();
        });
      }

      // Make the bottom button slightly transparent on scroll
      const bottomButton = document.querySelector('.nav-circle.bottom-center');

      window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
          bottomButton.style.opacity = "0.7";
        } else {
          bottomButton.style.opacity = "1";
        }
      });

      // Generate TOC on page load only if it should be open
      if (window.innerWidth > 768) {
        generateToc();
      } else {
        // On mobile, start with TOC closed
        toggleToc(false);
      }
    });
  </script>  <script src="/scripts/toc-enhancements.js" defer></script>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  