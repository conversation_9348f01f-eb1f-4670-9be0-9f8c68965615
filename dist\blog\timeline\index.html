<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Archive | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css"></head> <body data-page="blog" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content --> <div class="blog-header"><div class="blog-title">archive</div></div><div class="timeline-container"><!-- Back to Blog --><div class="back-section"><a href="/blog" class="back-to-blog"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"></path><path d="M12 19l-7-7 7-7"></path></svg><span>back to blog</span></a></div><!-- Timeline Content --><div class="timeline-content"><div class="year-section"><h2 class="year-heading">2025</h2><div class="year-posts"><div class="timeline-post"><div class="post-date">Apr 18</div><h3 class="post-title"><a href="/blog/dream-log-synesthetic-ocean">Dream Log – Synesthetic Ocean</a></h3></div><div class="timeline-post"><div class="post-date">Apr 18</div><h3 class="post-title"><a href="/blog/flow-state-diary-day-1">Flow State Diary – Day 1</a></h3></div></div></div></div></div><style>
      /* Global Scrollbar Styling */
      :global(html) {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
      }

      :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
      }

      :global(::-webkit-scrollbar-track) {
        background: transparent;
      }

      :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
      }

      :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
      }

      /* Blog Header - Static (not fixed) */
      .blog-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px;
      }

      .blog-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.5rem;
        color: rgba(240, 240, 240, 0.9);
        letter-spacing: -0.01em;
        position: relative;
      }

      /* Add subtle underline to blog title */
      .blog-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: rgba(240, 240, 240, 0.4);
      }

      /* Timeline Container */
      .timeline-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
      }

      /* Back to Blog Link */
      .back-section {
        margin-bottom: 40px;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        width: fit-content;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }

      /* Year Sections */
      .year-section {
        margin-bottom: 50px;
      }

      .year-heading {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.95);
        margin-bottom: 20px;
        font-weight: normal;
        position: relative;
        padding-bottom: 10px;
      }

      .year-heading::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 1px;
        background-color: rgba(200, 200, 200, 0.3);
      }

      /* Post Items */
      .timeline-post {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
      }

      .post-date {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.9rem;
        color: rgba(180, 180, 180, 0.75);
        margin-bottom: 5px;
      }

      .post-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.2rem;
        font-weight: normal;
        margin: 0;
      }

      .post-title a {
        color: rgba(220, 220, 220, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .post-title a:hover {
        color: rgba(255, 255, 255, 1);
        text-decoration: underline;
        text-underline-offset: 3px;
        text-decoration-color: rgba(200, 200, 200, 0.4);
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.3rem;
        }

        .timeline-container {
          padding: 0 20px;
        }

        .year-heading {
          font-size: 1.6rem;
        }

        .post-title {
          font-size: 1.1rem;
        }
      }
    </style> </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html> 