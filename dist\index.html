<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>PVB Home</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<style>.quote-container[data-astro-cid-j7pv25f6]{text-align:center;max-width:550px;padding:20px;position:relative;margin-top:0}.quote-container[data-astro-cid-j7pv25f6] .quote-indicator-wrapper[data-astro-cid-j7pv25f6]{position:absolute;bottom:100%;left:50%;transform:translate(-50%);margin-bottom:25px;width:44px;height:44px;display:flex;justify-content:center;align-items:center;cursor:pointer;z-index:50}.quote-indicator[data-astro-cid-j7pv25f6]{width:12px;height:2px;background-color:var(--color-inactive, #8a8178);border-radius:1px;transition:transform var(--transition-duration) var(--easing-standard),width var(--transition-duration) var(--easing-standard),height var(--transition-duration) var(--easing-standard),background-color var(--transition-duration) var(--easing-standard),border var(--transition-duration) var(--easing-standard),border-radius var(--transition-duration) var(--easing-standard)}.quote-indicator-wrapper[data-astro-cid-j7pv25f6].active .quote-indicator[data-astro-cid-j7pv25f6]{width:20px;height:20px;background-color:transparent;border:var(--circle-border-width) solid var(--color-inactive, #8a8178);border-radius:50%}.quote-indicator-wrapper[data-astro-cid-j7pv25f6].active:hover .quote-indicator[data-astro-cid-j7pv25f6]{transform:scale(1.15)}.quote-text[data-astro-cid-j7pv25f6]{font-size:1.05rem;line-height:1.5;margin-bottom:.8em;color:var(--color-text, #3a2c23);font-family:Georgia Custom,Georgia,serif}.quote-attribution[data-astro-cid-j7pv25f6]{font-size:.8rem;font-style:italic;color:var(--color-text-secondary, #6a5a4f);font-family:Georgia Custom,Georgia,serif}@media (max-width: 768px){.quote-container[data-astro-cid-j7pv25f6]{max-width:90%;padding:15px}.quote-container[data-astro-cid-j7pv25f6] .quote-indicator-wrapper[data-astro-cid-j7pv25f6]{margin-bottom:20px}.quote-text[data-astro-cid-j7pv25f6]{font-size:1rem}.quote-attribution[data-astro-cid-j7pv25f6]{font-size:.75rem}}
</style>
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css"></head> <body data-page="home" style="--color-accent: #3a2c23; --color-bg: rgba(250, 246, 242, 0); background-image: url(/images/whitemarble.png); background-color: rgba(250, 246, 242, 0);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="quote-container" data-astro-cid-j7pv25f6> <div class="quote-indicator-wrapper" id="quote-indicator-wrapper" title="Quote Info" aria-label="Show Quote Information" data-astro-cid-j7pv25f6> <div class="quote-indicator" id="quote-indicator" data-astro-cid-j7pv25f6></div> </div> <p class="quote-text" data-astro-cid-j7pv25f6></p> <p class="quote-attribution" data-astro-cid-j7pv25f6></p> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="About" aria-label="About Page" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>  <div class="nav-circle top-right" title="Links" aria-label="Links Page" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> </div> <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  