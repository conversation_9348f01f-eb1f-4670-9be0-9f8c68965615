<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>links | pvb</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>body[data-page=links]{overflow-x:hidden;overflow-y:auto;color:var(--blog-text);background-attachment:fixed;background-size:cover;height:auto;min-height:100vh}.content-wrapper{position:relative;height:auto;min-height:100vh;width:100%;padding-bottom:60px}.links-container[data-astro-cid-au7rboj5]{max-width:700px;margin:0 auto 60px;padding:0 25px}.link-category[data-astro-cid-au7rboj5]{margin-bottom:30px}.category-header[data-astro-cid-au7rboj5]{display:flex;align-items:center;gap:1rem;margin:30px 0 15px}.category-line[data-astro-cid-au7rboj5]{flex-grow:1;height:1px;background-color:var(--blog-border)}.category-title[data-astro-cid-au7rboj5]{font-family:Georgia Custom,Georgia,serif;font-size:.9rem;font-weight:400;color:var(--blog-text-secondary);white-space:nowrap;letter-spacing:.03em;padding:0 .5rem;margin:0;text-transform:lowercase}.links-grid[data-astro-cid-au7rboj5]{display:grid;grid-template-columns:repeat(2,1fr);gap:12px}.link-card[data-astro-cid-au7rboj5]{display:flex;align-items:center;background-color:#ffffff08;border:1px solid var(--blog-border);border-radius:4px;padding:12px;text-decoration:none;transition:all .3s var(--easing-standard);color:inherit}.link-card[data-astro-cid-au7rboj5]:hover{background-color:#ffffff0d;transform:translateY(-2px);border-color:#fff3}.link-card[data-astro-cid-au7rboj5].inactive{opacity:.5;cursor:default;pointer-events:none}.link-icon[data-astro-cid-au7rboj5]{width:32px;height:32px;min-width:32px;display:flex;justify-content:center;align-items:center;border-radius:4px;overflow:hidden;margin-right:12px}.link-icon[data-astro-cid-au7rboj5] img[data-astro-cid-au7rboj5]{width:18px;height:18px;-o-object-fit:contain;object-fit:contain}.link-content[data-astro-cid-au7rboj5]{flex:1;overflow:hidden}.link-title[data-astro-cid-au7rboj5]{font-family:Georgia Custom,Georgia,serif;font-size:.85rem;margin:0 0 3px;color:var(--blog-text);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.link-description[data-astro-cid-au7rboj5]{font-family:Georgia Custom,Georgia,serif;font-size:.7rem;margin:0;color:var(--blog-text-secondary)}.link-arrow[data-astro-cid-au7rboj5]{margin-left:8px;font-size:.8rem;color:#fff6;transition:transform var(--transition-duration) var(--easing-standard)}.link-card[data-astro-cid-au7rboj5]:hover .link-arrow[data-astro-cid-au7rboj5]{transform:translate(2px);color:#ffffffb3}@media (max-width: 768px){.links-grid[data-astro-cid-au7rboj5]{grid-template-columns:1fr}.category-title[data-astro-cid-au7rboj5]{font-size:.8rem}}@media (max-width: 480px){.link-card[data-astro-cid-au7rboj5]{padding:10px}.link-icon[data-astro-cid-au7rboj5]{width:28px;height:28px;min-width:28px;margin-right:10px}.link-icon[data-astro-cid-au7rboj5] img[data-astro-cid-au7rboj5]{width:16px;height:16px}.link-title[data-astro-cid-au7rboj5]{font-size:.8rem}.link-description[data-astro-cid-au7rboj5]{font-size:.65rem}}
</style></head> <body data-page="links" style="--color-accent: #f0f0f0; --color-bg: rgba(10, 10, 10, 0.94); background-image: url(/images/blackgranite.png); background-color: rgba(10, 10, 10, 0.94);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="blog-header" data-astro-cid-au7rboj5> <h1 class="blog-title" data-astro-cid-au7rboj5>links</h1> </div> <main class="links-container" data-astro-cid-au7rboj5> <section class="link-category" data-astro-cid-au7rboj5> <div class="category-header" data-astro-cid-au7rboj5> <div class="category-line" data-astro-cid-au7rboj5></div> <h2 class="category-title" data-astro-cid-au7rboj5>research</h2> <div class="category-line" data-astro-cid-au7rboj5></div> </div> <div class="links-grid" data-astro-cid-au7rboj5> <a href="https://orcid.org/0009-0001-2718-0254" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/orcid.svg" alt="ORCID logo - academic publication identifier" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>ORCID</div> <div class="link-description" data-astro-cid-au7rboj5>academic publication identifier</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://murst.org/" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/murst-logo.png" alt="MURST Initiative logo - advancing recursive intelligence research" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>MURST Initiative</div> <div class="link-description" data-astro-cid-au7rboj5>advancing recursive intelligence research</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://zenodo.org/communities/murst/records" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/zenodo.svg" alt="Zenodo logo - research publication archive" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>Zenodo</div> <div class="link-description" data-astro-cid-au7rboj5>research publication archive</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a> </div> </section><section class="link-category" data-astro-cid-au7rboj5> <div class="category-header" data-astro-cid-au7rboj5> <div class="category-line" data-astro-cid-au7rboj5></div> <h2 class="category-title" data-astro-cid-au7rboj5>social</h2> <div class="category-line" data-astro-cid-au7rboj5></div> </div> <div class="links-grid" data-astro-cid-au7rboj5> <a href="https://www.linkedin.com/in/pruthvi-bhat-/" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/linkedin.png" alt="LinkedIn logo - professional profile" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>LinkedIn</div> <div class="link-description" data-astro-cid-au7rboj5>professional profile</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://x.com/pvibhat" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/x.svg" alt="X logo - twitter/x profile" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>X</div> <div class="link-description" data-astro-cid-au7rboj5>twitter/x profile</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://bsky.app/profile/pvbhat.bsky.social" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/bluesky.svg" alt="Bluesky logo - bluesky profile" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>Bluesky</div> <div class="link-description" data-astro-cid-au7rboj5>bluesky profile</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://www.lesswrong.com/users/pruthvi-bhat" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/lesswrong.svg" alt="LessWrong logo - rationality community" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>LessWrong</div> <div class="link-description" data-astro-cid-au7rboj5>rationality community</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a> </div> </section><section class="link-category" data-astro-cid-au7rboj5> <div class="category-header" data-astro-cid-au7rboj5> <div class="category-line" data-astro-cid-au7rboj5></div> <h2 class="category-title" data-astro-cid-au7rboj5>projects</h2> <div class="category-line" data-astro-cid-au7rboj5></div> </div> <div class="links-grid" data-astro-cid-au7rboj5> <a href="https://pruthvibhat.medium.com/" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/medium.png" alt="Medium logo - essays and articles" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>Medium</div> <div class="link-description" data-astro-cid-au7rboj5>essays and articles</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://github.com/PV-Bhat" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/github.png" alt="GitHub logo - code repositories" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>GitHub</div> <div class="link-description" data-astro-cid-au7rboj5>code repositories</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a><a href="https://hevy.com/user/approxfit" class="link-card " target="_blank" rel="noopener noreferrer" data-astro-cid-au7rboj5> <div class="link-icon" data-astro-cid-au7rboj5> <img src="/images/logos/hevy.png" alt="Hevy logo - fitness tracking" loading="lazy" data-astro-cid-au7rboj5> </div> <div class="link-content" data-astro-cid-au7rboj5> <div class="link-title" data-astro-cid-au7rboj5>Hevy</div> <div class="link-description" data-astro-cid-au7rboj5>fitness tracking</div> </div> <div class="link-arrow" aria-hidden="true" data-astro-cid-au7rboj5>→</div> </a> </div> </section> </main>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  