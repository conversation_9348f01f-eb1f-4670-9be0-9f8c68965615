<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Tag: blog | PVB</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>.quote-container[data-astro-cid-hqxj4ft6]{margin:0 auto}h1[data-astro-cid-hqxj4ft6]{font-family:Georgia Custom,Georgia,serif}.text-muted[data-astro-cid-hqxj4ft6]{color:var(--color-text-secondary)}.text-accent[data-astro-cid-hqxj4ft6]{color:var(--color-accent)}.posts-container[data-astro-cid-hqxj4ft6]{margin:2rem 0;text-align:left}.posts-container[data-astro-cid-hqxj4ft6] ul[data-astro-cid-hqxj4ft6]{list-style:none;padding:0}.back-link[data-astro-cid-hqxj4ft6]{display:inline-flex;align-items:center;color:var(--color-accent);text-decoration:none;transition:opacity .2s ease;font-family:Georgia Custom,Georgia,serif}.back-link[data-astro-cid-hqxj4ft6]:hover{opacity:.75}.back-arrow[data-astro-cid-hqxj4ft6]{margin-right:.5rem}
.tag-link[data-astro-cid-blwjyjpt]{display:inline-block;padding:.2rem .6rem;border-radius:1rem;text-decoration:none;border:1px solid rgba(var(--color-accent-rgb),.3);background-color:rgba(var(--color-accent-rgb),.05);color:rgba(var(--color-accent-rgb),.8);transition:background-color .2s ease,border-color .2s ease,color .2s ease;font-family:Georgia Custom,Georgia,serif}.tag-link[data-astro-cid-blwjyjpt]:hover{background-color:rgba(var(--color-accent-rgb),.15);border-color:rgba(var(--color-accent-rgb),.5);color:rgba(var(--color-accent-rgb),1)}.tag-sm[data-astro-cid-blwjyjpt]{font-size:.7rem}.tag-lg[data-astro-cid-blwjyjpt]{font-size:.8rem}
.blog-post-card[data-astro-cid-f45vxlzk]{opacity:0;animation:fade-in-card .5s ease-out forwards;margin:1.5rem 0;background-color:#14141499;border:1px solid rgba(255,255,255,.05);border-radius:.75rem;padding:1.5rem;list-style:none;box-shadow:var(--shadow-card);transition:transform var(--transition-duration) var(--easing-standard),box-shadow var(--transition-duration) var(--easing-standard),border-color var(--transition-duration) var(--easing-standard)}.blog-post-card[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px);box-shadow:var(--shadow-card-hover-light);border-color:#ffffff1a}.blog-post-card[data-astro-cid-f45vxlzk]:last-child{margin-bottom:1.5rem}.post-link[data-astro-cid-f45vxlzk]{display:block;transition:transform var(--transition-duration) var(--easing-standard);text-decoration:none;color:var(--blog-text)}.post-link[data-astro-cid-f45vxlzk]:hover{transform:translateY(-2px)}.post-date[data-astro-cid-f45vxlzk]{display:block;font-size:.8rem;color:#f0f0f099;margin-bottom:.5rem;font-family:Georgia Custom,Georgia,serif}.post-title[data-astro-cid-f45vxlzk]{font-size:1.4rem;font-weight:600;margin:0 0 .75rem;color:var(--blog-text);font-family:Georgia Custom,Georgia,serif;line-height:1.3}.post-description[data-astro-cid-f45vxlzk]{font-size:.95rem;line-height:1.6;margin-bottom:1rem;color:#f0f0f0cc;font-family:Georgia Custom,Georgia,serif}.post-tags[data-astro-cid-f45vxlzk]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:1rem}@keyframes fade-in-card{0%{opacity:0;transform:translateY(4px)}to{opacity:1;transform:translateY(0)}}
@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0;mix-blend-mode:plus-lighter}to{opacity:1;mix-blend-mode:plus-lighter}}@keyframes astroFadeOut{0%{opacity:1;mix-blend-mode:plus-lighter}to{opacity:0;mix-blend-mode:plus-lighter}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}
</style><style>[data-astro-transition-scope="astro-6rhg7idu-1"] { view-transition-name: blog; }@layer astro { ::view-transition-old(blog) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(blog) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(blog) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(blog) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-6rhg7idu-1"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-6rhg7idu-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-6rhg7idu-1"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-6rhg7idu-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-6rhg7idu-1"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-6rhg7idu-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-6rhg7idu-1"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-6rhg7idu-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-2"] { view-transition-name: toolbox-thinking-upgrade-your-mental-api; }@layer astro { ::view-transition-old(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(toolbox-thinking-upgrade-your-mental-api) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-2"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-3"] { view-transition-name: why-metacognition-outperforms-motivation; }@layer astro { ::view-transition-old(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(why-metacognition-outperforms-motivation) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-3"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-4"] { view-transition-name: test-3; }@layer astro { ::view-transition-old(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(test-3) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-4"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-4"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-w2v4zgte-5"] { view-transition-name: systems-thinking-for-everyday-decisions; }@layer astro { ::view-transition-old(systems-thinking-for-everyday-decisions) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(systems-thinking-for-everyday-decisions) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(systems-thinking-for-everyday-decisions) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(systems-thinking-for-everyday-decisions) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-w2v4zgte-5"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-w2v4zgte-5"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style></head> <body data-page="tag" style="--color-accent: #f0f0f0; --color-bg: rgba(0, 0, 0, 0.9); background-color: rgba(0, 0, 0, 0.9);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="quote-container" style="max-width: 800px;" data-astro-cid-hqxj4ft6> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-hqxj4ft6>
Tag: <span data-astro-cid-hqxj4ft6 data-astro-transition-scope="astro-6rhg7idu-1">#blog</span> </h1> <p class="mb-8 text-muted" data-astro-cid-hqxj4ft6> 4 posts with this tag
</p> <div class="posts-container" data-astro-cid-hqxj4ft6> <ul data-astro-cid-hqxj4ft6> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/toolbox-thinking-upgrade-your-mental-api/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:30:48.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-2"> Toolbox Thinking: Upgrade Your Mental API </h3> <p class="post-description" data-astro-cid-f45vxlzk>You&#39;re only as powerful as the tools you know how to use under pressure.
</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/why-metacognition-outperforms-motivation/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-18T07:30:17.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 18, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-3"> Why Metacognition Outperforms Motivation </h3> <p class="post-description" data-astro-cid-f45vxlzk>Metacognition isn&#39;t a supplement to motivation—it&#39;s the upgrade.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/test-3/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-16T07:04:20.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 16, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-4"> Test 3 </h3> <p class="post-description" data-astro-cid-f45vxlzk>Yes</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li> <li class="blog-post-card" data-astro-cid-f45vxlzk> <a href="/blog/systems-thinking-for-everyday-decisions/" class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time datetime="2025-04-07T07:21:00.000Z" class="post-date" data-astro-cid-f45vxlzk>Apr 7, 2025</time> <h3 class="post-title" data-astro-cid-f45vxlzk data-astro-transition-scope="astro-w2v4zgte-5"> Systems Thinking for Everyday Decisions </h3> <p class="post-description" data-astro-cid-f45vxlzk>Systems thinking isn’t just for consultants—it’s a mindset shift that enhances everyday decision-making.</p> <div class="post-tags" data-astro-cid-f45vxlzk>  </div> </div> </a> </li>  </ul> </div> <div class="mt-10" data-astro-cid-hqxj4ft6> <a href="/tags" class="back-link" data-astro-cid-hqxj4ft6> <span class="back-arrow" data-astro-cid-hqxj4ft6>←</span> <span data-astro-cid-hqxj4ft6>All tags</span> </a> </div> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  