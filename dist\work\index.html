<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Work</title><link rel="stylesheet" href="/_astro/about.BQ_O01lS.css">
<link rel="stylesheet" href="/_astro/_slug_.DjBHMTUQ.css">
<style>@import"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&family=Playfair+Display:wght@400;700&display=swap";#lens-rail-viewport{position:relative;cursor:grab}#lens-rail-viewport:active{cursor:grabbing}.pill{position:relative;overflow:hidden;flex-shrink:0;will-change:transform,opacity,filter;transition:opacity .4s ease,filter .4s ease,background-color .3s ease;backface-visibility:hidden}.pill.unfocused{opacity:.6}.pill.unfocused:hover{opacity:.85}.pill-glint{position:absolute;top:0;left:0;width:60%;height:100%;background:linear-gradient(to right,transparent 0%,rgba(255,255,255,.5) 50%,transparent 100%);transform:skew(-25deg) translate(-250%);will-change:transform}.pill-border-trace{--angle: 90deg;position:absolute;inset:-2px;border-radius:9999px;background:conic-gradient(from var(--angle),transparent 0% 50%,#ffffff 50%,#ffffff 50.5%,transparent 50.5% 100%);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;opacity:0;will-change:background}.skeleton-box{position:relative;overflow:hidden;background-color:#ffffff08;border-radius:.5rem}.skeleton-box:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;transform:translate(-100%);background-image:linear-gradient(90deg,#23232d00 0,#37415566,#23232d00);animation:obsidian-shimmer 2.5s infinite}@keyframes obsidian-shimmer{to{transform:translate(100%)}}
</style><script type="module" src="/_astro/hoisted.DSf6GNrp.js"></script></head> <body data-page="work" style="--color-accent: #f0f0f0; --color-bg: rgba(0,0,0,0.88); background-image: url(/images/obsidian.png); background-color: rgba(0,0,0,0.88);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="p-4 sm:p-6 md:p-8 max-w-7xl mx-auto"> <header class="text-center py-16 sm:py-24"> <h1 class="font-serif text-5xl md:text-7xl font-bold tracking-tighter">Work</h1> <p class="mt-4 text-lg text-gray-400 max-w-2xl mx-auto">
A curated collection of projects spanning deep research, practical software, and experimental concepts. Select a lens to explore.
</p> </header> <main id="work-page-container" class="work-page" data-page-data="{&#34;lenses&#34;:[{&#34;id&#34;:&#34;research&#34;,&#34;name&#34;:&#34;Research&#34;},{&#34;id&#34;:&#34;featured&#34;,&#34;name&#34;:&#34;Featured&#34;},{&#34;id&#34;:&#34;latest&#34;,&#34;name&#34;:&#34;Latest&#34;},{&#34;id&#34;:&#34;experimental&#34;,&#34;name&#34;:&#34;Experimental&#34;},{&#34;id&#34;:&#34;formal-projects&#34;,&#34;name&#34;:&#34;Formal Projects&#34;},{&#34;id&#34;:&#34;software&#34;,&#34;name&#34;:&#34;Software&#34;},{&#34;id&#34;:&#34;all-topics&#34;,&#34;name&#34;:&#34;All Topics&#34;,&#34;type&#34;:&#34;redirect&#34;}],&#34;projects&#34;:[{&#34;id&#34;:1,&#34;title&#34;:&#34;Synapse Drive – Centralized Resource Platform&#34;,&#34;description&#34;:&#34;Built a Notion-meets-GitHub hub for knowledge sovereignty.\n&#34;,&#34;tags&#34;:[&#34;work&#34;],&#34;lenses&#34;:[&#34;Featured&#34;,&#34;Latest&#34;],&#34;domain&#34;:&#34;General&#34;,&#34;cover&#34;:&#34;https://placehold.co/600x400/0d0d0d/f0f0f0?text=Synapse&#34;},{&#34;id&#34;:2,&#34;title&#34;:&#34;Leads Wizard – AI‑Driven Lead Qualification&#34;,&#34;description&#34;:&#34;Used LLM orchestration to rank and qualify sales leads with dynamic input vectors.\n&#34;,&#34;tags&#34;:[&#34;ai&#34;,&#34;work&#34;],&#34;lenses&#34;:[&#34;Latest&#34;],&#34;domain&#34;:&#34;ai&#34;,&#34;cover&#34;:&#34;https://placehold.co/600x400/0d0d0d/f0f0f0?text=Leads&#34;},{&#34;id&#34;:3,&#34;title&#34;:&#34;Recursive Self-Referential Compression (RSRC): AI’s Survival Map&#34;,&#34;description&#34;:&#34;A dual-metric framework for sustainable AI that prioritizes efficiency over brute-force scaling.&#34;,&#34;tags&#34;:[&#34;ai&#34;,&#34;rsrc&#34;,&#34;work&#34;],&#34;lenses&#34;:[&#34;Latest&#34;],&#34;domain&#34;:&#34;ai&#34;,&#34;cover&#34;:&#34;https://placehold.co/600x400/0d0d0d/f0f0f0?text=Recursive&#34;}],&#34;publications&#34;:[{&#34;title&#34;:&#34;Recursive Self-Referential Compression (RSRC): AI's Survival Map in the Post-Scaling Era&#34;,&#34;journal&#34;:&#34;MURST Research Initiative&#34;,&#34;year&#34;:2025,&#34;url&#34;:&#34;#&#34;,&#34;pdfUrl&#34;:&#34;/pdfs/RSRC_Paper.pdf&#34;},{&#34;title&#34;:&#34;Cordyceps militaris: Culturing, Optimization and Bioactive Compound Analysis&#34;,&#34;journal&#34;:&#34;Research Paper (IEEE format)&#34;,&#34;year&#34;:2023,&#34;url&#34;:&#34;#&#34;,&#34;pdfUrl&#34;:&#34;/pdfs/Cordyceps_Paper.pdf&#34;}]}"><div id="work-live-region" class="visually-hidden" aria-live="polite" aria-atomic="true"></div> <div id="lens-rail-viewport" class="w-full h-16 mb-12 overflow-hidden"> <div id="lens-rail" class="flex items-center h-full absolute top-0 left-0 space-x-4 px-4"></div> </div> <div id="content-display" class="min-h-[60vh] transition-opacity duration-500 grid md:grid-cols-2 lg:grid-cols-3 gap-8"></div> <section id="publications" class="mt-24 py-16 border-t border-gray-800"> <div class="flex items-center justify-center mb-12"> <div class="flex-grow h-px bg-gray-800"></div> <h2 class="font-serif text-3xl md:text-4xl mx-6 text-center">Research & Publications</h2> <div class="flex-grow h-px bg-gray-800"></div> </div> <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto"> <div class="bg-gray-900/30 p-5 rounded-lg hover:bg-gray-900 transition-colors duration-300"> <h3 class="font-medium">Recursive Self-Referential Compression (RSRC): AI&#39;s Survival Map in the Post-Scaling Era</h3> <p class="text-sm text-gray-500 mt-1">MURST Research Initiative, 2025</p> <a href="#" class="text-sm text-sky-400 hover:text-sky-300 mt-2 inline-block">View Publication →</a> </div><div class="bg-gray-900/30 p-5 rounded-lg hover:bg-gray-900 transition-colors duration-300"> <h3 class="font-medium">Cordyceps militaris: Culturing, Optimization and Bioactive Compound Analysis</h3> <p class="text-sm text-gray-500 mt-1">Research Paper (IEEE format), 2023</p> <a href="#" class="text-sm text-sky-400 hover:text-sky-300 mt-2 inline-block">View Publication →</a> </div> </div> </section> <section id="distribution-chart" class="mt-16 py-16"> <h2 class="font-serif text-3xl md:text-4xl text-center mb-8">Project Domain Distribution</h2> <div class="chart-container relative mx-auto" style="height:300px; max-width:300px;"> <canvas id="project-dist-chart"></canvas> </div> <p class="text-center text-gray-500 mt-4">An overview of the domains my work encompasses.</p> </section> </main> </div>   </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html> 