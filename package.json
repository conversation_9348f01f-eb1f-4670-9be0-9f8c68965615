{"name": "pvb-astro", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build && pagefind --site dist || echo 'Build completed with warnings'", "preview": "astro preview", "astro": "astro", "a11y": "axe dist --save"}, "dependencies": {"@astrojs/react": "^4.2.4", "@astrojs/rss": "^4.0.5", "@astrojs/tailwind": "^6.0.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "astro": "^4.5.5", "chart.js": "^4.5.0", "dayjs": "^1.11.10", "gray-matter": "^4.0.3", "gsap": "^3.13.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@astrojs/sitemap": "^3.3.0", "@pagefind/default-ui": "^1.0.4", "pagefind": "^1.0.4", "@axe-core/cli": "^4.8.4"}}