---
import { slugifyStr } from "../utils/slugify";
import Tag from "./Tag.astro";

export interface Props {
  project: any; // Using 'any' for Ghost content
  variant?: "featured" | "standard";
}

const { project, variant = "standard" } = Astro.props;

// Use direct JSON properties for project data
const title = project.title;
const description = project.excerpt || project.custom_excerpt || '';

// Process tags from JSON 'tags' array
const tags = Array.isArray(project.tags) ? project.tags.map((tag: any) => {
  if (typeof tag === 'object' && tag !== null) {
    // Ghost format - tag is an object with name and slug properties
    return {
      name: tag.name,
      slug: tag.slug || slugifyStr(tag.name)
    };
  } else if (typeof tag === 'string') {
    // String format - create an object with name and slugified name
    return {
      name: tag,
      slug: slugifyStr(tag)
    };
  }
  return null;
}).filter(Boolean) : [];

const featured = project.featured || false;

// For URLs, use Ghost content structure if available, otherwise use work structure
const repoUrl = project.repo_url || project.repoUrl || '';
const liveUrl = project.live_url || project.liveUrl || '';

// Use published_at for project date
const projectDate = project.published_at;

// Use status if provided in JSON
const status = project.status || '';

function formatDate(date: Date) {
  if (!date) return "";

  try {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  } catch (e) {
    // If we have a string instead of a date
    if (typeof date === 'string') {
      const d = new Date(date);
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short'
      });
    }
    return "";
  }
}

const isFeatured = variant === "featured";
---

{
  isFeatured ? (
    <article class="project-card featured">
      <a href={`/work/${project.slug}`} class="project-link">
        <div class="project-meta">
          <time datetime={projectDate instanceof Date ? projectDate.toISOString() : new Date(projectDate).toISOString()}>{formatDate(projectDate)}</time>
          <div class="project-badges">
            {status && status !== "Completed" && (
              <span class="project-status">{status}</span>
            )}
            <span class="featured-badge">Featured</span>
          </div>
        </div>

        <h2 class="project-title" transition:name={slugifyStr(title)}>
          {title}
        </h2>

        <p class="project-description">{description}</p>

        {tags && tags.length > 0 && (
          <div class="project-tags">
            {tags.map((tag: {slug: string, name: string}) => (
              <Tag tag={tag.slug} tagName={tag.name} />
            ))}
          </div>
        )}

        <div class="project-actions">
          <span class="view-details">
            read more <span class="arrow">→</span>
          </span>

          <div class="external-links">
            {repoUrl && (
              <a href={repoUrl} target="_blank" rel="noopener noreferrer"
                class="external-link" onclick="event.stopPropagation()">
                Source <span class="ext-arrow">↗</span>
              </a>
            )}
            {liveUrl && (
              <a href={liveUrl} target="_blank" rel="noopener noreferrer"
                class="external-link" onclick="event.stopPropagation()">
                Demo <span class="ext-arrow">↗</span>
              </a>
            )}
          </div>
        </div>
      </a>
    </article>
  ) : (
    <article class="project-card standard">
      <a href={`/work/${project.slug}`} class="project-link">
        <div class="project-content">
          <h2 class="project-title" transition:name={slugifyStr(title)}>
            {title}
          </h2>
          <p class="project-description">{description}</p>

          <div class="project-footer">
            <div class="project-meta">
              <time datetime={projectDate instanceof Date ? projectDate.toISOString() : new Date(projectDate).toISOString()}>{formatDate(projectDate)}</time>
              {status && status !== "Completed" && (
                <span class="project-status small">{status}</span>
              )}
            </div>

            <div class="project-tags-container">
              {tags && tags.length > 0 && (
                <div class="project-tags small">
                  {tags.slice(0, 3).map((tag: {slug: string, name: string}) => (
                    <Tag tag={tag.slug} tagName={tag.name} size="sm" />
                  ))}
                  {tags.length > 3 && (
                    <span class="more-tags">+{tags.length - 3}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </a>
    </article>
  )
}

<style>
  /* Project card - base styling with refined transitions */
  .project-card {
    position: relative;
    transition: transform 0.5s var(--easing-standard);
  }

  .project-card.featured {
    margin-bottom: 3.5rem;
    padding-bottom: 2.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  }

  /* Standard project card styling */
  .project-card.standard {
    position: relative;
    border-radius: 2px;
    transition: transform 0.6s var(--easing-standard), opacity 0.6s var(--easing-standard);
  }

  .project-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: color 0.4s var(--easing-standard);
    height: 100%;
  }

  .project-card.standard .project-link {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Add subtle hover effect with intentional timing */
  .project-card:hover {
    transform: translateY(-3px);
  }

  /* Add a subtle highlight line animation on hover for featured projects */
  .project-card.featured::after {
    content: '';
    position: absolute;
    bottom: 2.5rem;
    left: 0;
    width: 0;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    transition: width 0.7s var(--easing-standard);
  }

  .project-card.featured:hover::after {
    width: 100%;
  }

  /* Add subtle glow effect for standard project cards */
  .project-card.standard::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02), transparent 70%);
    opacity: 0;
    transition: opacity 0.8s var(--easing-standard);
    pointer-events: none;
    z-index: -1;
  }

  .project-card.standard:hover::after {
    opacity: 1;
  }

  /* Project Content Styling - Typography refinements */
  .project-title {
    font-size: 1.6rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 0.9rem;
    line-height: 1.3;
    font-family: 'Georgia Custom', Georgia, serif;
    letter-spacing: -0.01em;
  }

  .project-card.standard .project-title {
    font-size: 1.25rem;
    margin-bottom: 0.7rem;
    letter-spacing: 0;
  }

  .project-description {
    font-size: 0.95rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.75);
    margin-bottom: 1.5rem;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .project-card.standard .project-description {
    font-size: 0.85rem;
    margin-bottom: 1.2rem;
    color: rgba(255, 255, 255, 0.65);
    flex-grow: 1;
    line-height: 1.5;
  }

  /* Project metadata styling with refined spacing */
  .project-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .project-card.standard .project-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .project-card.standard .project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    flex-wrap: wrap;
    gap: 0.7rem;
  }

  time {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.45);
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1;
  }

  /* Project badges & status indicators - refined with subtle styling */
  .project-badges {
    display: flex;
    gap: 0.7rem;
    align-items: center;
  }

  .project-status {
    font-size: 0.65rem;
    color: rgba(255, 215, 0, 0.85);
    border: 1px solid rgba(255, 215, 0, 0.2);
    padding: 0 0.4rem;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.5;
  }

  .project-status.small {
    font-size: 0.6rem;
    padding: 0 0.3rem;
  }

  .featured-badge {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0 0.4rem;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.5;
  }

  /* Tags styling - refined with better spacing and subtle hover effects */
  .project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .project-tags.small {
    margin-bottom: 0;
    gap: 0.4rem;
  }

  .tag {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0 0 0.15rem 0;
    letter-spacing: 0.01em;
    transition: all 0.4s var(--easing-standard);
  }

  .tag.small {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.5);
    border-bottom: none;
  }

  .project-card:hover .tag {
    color: rgba(255, 255, 255, 0.85);
    border-bottom-color: rgba(255, 255, 255, 0.25);
  }

  .project-card:hover .tag.small {
    color: rgba(255, 255, 255, 0.7);
  }

  .more-tags {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.4);
    margin-left: 0.2rem;
  }

  /* Project actions - refined with better alignment and subtle animations */
  .project-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .external-links {
    display: flex;
    gap: 1rem;
  }

  .view-details {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.4s var(--easing-standard);
  }

  .project-card:hover .view-details {
    color: rgba(255, 255, 255, 0.85);
  }

  .view-details .arrow {
    font-size: 0.85rem;
    transition: transform 0.4s var(--easing-standard);
  }

  .project-card:hover .view-details .arrow {
    transform: translateX(0.2rem);
  }

  .external-link {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.45);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.2rem;
    transition: all 0.3s var(--easing-standard);
  }

  .external-link:hover {
    color: rgba(255, 255, 255, 0.9);
  }

  .ext-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s var(--easing-standard);
  }

  .external-link:hover .ext-arrow {
    transform: translateX(0.125rem) translateY(-0.125rem);
  }

  /* Responsive adjustments */
  @media (max-width: 48rem) { /* 768px */
    .project-title {
      font-size: 1.5rem;
    }

    .project-card.standard .project-title {
      font-size: 1.15rem;
    }

    .project-description {
      font-size: 0.9rem;
    }

    .project-card.featured {
      margin-bottom: 3rem;
      padding-bottom: 2rem;
    }

    .project-card.featured::after {
      bottom: 2rem;
    }

    .project-card.standard .project-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.7rem;
    }
  }

  @media (max-width: 30rem) { /* 480px */
    .project-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.7rem;
    }

    .external-links {
      width: 100%;
      justify-content: flex-start;
    }
  }
</style>