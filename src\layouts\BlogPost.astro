---
import type { CollectionEntry } from "astro:content";
import { SITE } from "../config";
import Layout from "./Layout.astro";
import Tag from "../components/Tag.astro";
import { slugifyStr } from "../utils/slugify";

export interface Props {
  post: any; // Accept any post object
}

const { post } = Astro.props;
// Normalize entry for both Astro content and raw JSON
const raw = post.data || post;
const { title, author = SITE.author, description, ogImage, tags = [] } = raw;
// Determine publication date
const pubDatetime = raw.pubDatetime ? new Date(raw.pubDatetime) : new Date(raw.published_at);
const modDatetime = raw.modDatetime ? new Date(raw.modDatetime) : null;
// Render HTML: use HTML from JSON only
const contentHtml = raw.html || '';

const datetime = pubDatetime.toISOString();
const postDate = pubDatetime.toLocaleDateString("en-US", {
  day: "numeric",
  month: "long",
  year: "numeric",
});

const ogUrl = ogImage ? ogImage : SITE.ogImage;

// Determine if this is a blog post or work project
const isWorkProject = (raw.tags && raw.tags.some(t => (typeof t === 'object' ? t.slug === 'work' || t.name === 'work' : t === 'work'))) || post.collection === 'work';
const pageType = isWorkProject ? 'work' : 'blog';
const backLabel = pageType; // always lower-case
---

<Layout
  pageTitle={title}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.85)"
  backgroundImageUrl={isWorkProject ? "/images/obsidian.png" : "/images/blackgranite.png"}
  bodyDataPage={isWorkProject ? "work-post" : "blog-post"}
>
  <!-- Header title - Dynamic based on content type -->
  <div class="blog-header">
    <div class="blog-title">{pageType}</div>
  </div>

  <!-- DO NOT add a back button here - the Navigation component handles it -->

  <!-- Table of Contents Toggle Button -->
  <button id="toc-toggle" class="toc-toggle" aria-expanded="false" aria-controls="toc-panel">
    <span class="toc-icon-dots">
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
    </span>
  </button>
  <!-- Table of Contents Panel -->
  <div id="toc-panel" class="toc-panel" aria-hidden="true">
    <div class="toc-panel-inner">
      <h3 class="toc-panel-title">Table of Contents</h3>
      <button class="toc-close" id="toc-close" aria-label="Close Table of Contents">
        <span class="toc-close-arrow">›</span>
      </button>
      <div id="toc-content" class="toc-content">
        <!-- The TOC content will be populated via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Content -->
  <article class="blog-post">
    <header class="post-header">
      <h1 class="post-title" transition:name={slugifyStr(title)}>
        {title}
      </h1>
      <div class="post-date">{postDate}</div>
    </header>

    <div class="post-content" set:html={raw.html}></div>

    {post.relatedPosts && post.relatedPosts.length > 0 && (
      <div class="related-posts">
        <h3 class="related-title">You might also enjoy</h3>
        <div class="related-grid">
          {post.relatedPosts.map(relatedPost => (
            <div class="related-post">
              <h4 class="related-post-title">
                <a href={`/blog/${relatedPost.slug}`}>{relatedPost.data.title}</a>
              </h4>
              <div class="related-post-date">
                {new Date(relatedPost.data.pubDatetime).toLocaleDateString('en-US', {day: 'numeric', month: 'short', year: 'numeric'})}
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <div class="post-footer">
      <div class="post-tags">
        {tags.map(tag => <Tag tag={slugifyStr(tag)} tagName={tag} />)}
      </div>

      <div class="post-actions">
        <a href={`/${backLabel}`} class="return-link">← {backLabel}</a>
        <a href="#" class="subscribe-link">subscribe by email</a>
      </div>
    </div>
  </article>

  <script is:inline>
    document.addEventListener('DOMContentLoaded', function() {
      const tocToggle = document.getElementById('toc-toggle');
      const tocPanel = document.getElementById('toc-panel');
      const tocContent = document.getElementById('toc-content');
      const tocClose = document.getElementById('toc-close');
      const blogPost = document.querySelector('.blog-post');
      const isWorkPost = document.body.getAttribute('data-page') === 'work-post';

      // Set return link text based on page type
      const returnLink = document.querySelector('.return-link');
      if (returnLink && isWorkPost) {
        returnLink.textContent = '← Work';
        returnLink.setAttribute('href', '/work');
      }

      // Prevent automatic scrolling to bottom
      window.history.scrollRestoration = 'manual';

      // Generate TOC from headings in the post
      function generateToc() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (headings.length === 0) {
          tocContent.innerHTML = '<p class="toc-empty">No sections found</p>';
          // Close TOC if no sections
          tocToggle.classList.remove('active');
          tocToggle.setAttribute('aria-expanded', 'false');
          tocPanel.classList.remove('active');
          tocPanel.setAttribute('aria-hidden', 'true');
          return;
        }

        const tocHtml = document.createElement('div');
        tocHtml.classList.add('toc-list-container');

        headings.forEach((heading, index) => {
          // Add an ID to the heading if it doesn't have one
          if (!heading.id) {
            heading.id = `heading-${index}`;
          }

          const item = document.createElement('div');
          item.classList.add(`toc-item`);
          item.classList.add(`toc-${heading.tagName.toLowerCase()}`);

          const a = document.createElement('a');
          a.href = `#${heading.id}`;
          a.textContent = heading.textContent;
          a.style.color = '#ffffff'; // Force white color
          a.style.textDecoration = 'none'; // Force no underline

          a.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all TOC links
            document.querySelectorAll('.toc-content a').forEach(link => {
              link.classList.remove('active');
            });

            // Add active class to clicked link
            a.classList.add('active');

            // Smooth scroll to the heading
            heading.scrollIntoView({ behavior: 'smooth' });

            // Add a highlight effect to the heading
            heading.classList.add('highlight');
            setTimeout(() => {
              heading.classList.remove('highlight');
            }, 1500);

            // On mobile, close the TOC after clicking
            if (window.innerWidth < 768) {
              toggleToc(false);
            }
          });

          item.appendChild(a);
          tocHtml.appendChild(item);
        });

        tocContent.innerHTML = '';
        tocContent.appendChild(tocHtml);

        // Initial styling of all links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.style.color = '#ffffff';
          link.style.textDecoration = 'none';
        });

        // Highlight the current section on scroll
        window.addEventListener('scroll', highlightCurrentSection);

        // Initial highlight
        setTimeout(highlightCurrentSection, 100);
      }

      // Highlight the current section in the TOC
      function highlightCurrentSection() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (!headings.length) return;

        // Find the heading that's currently in view
        let currentHeading = null;

        // Calculate viewport height and set threshold for heading detection
        const viewportHeight = window.innerHeight;
        const threshold = viewportHeight * 0.25; // 25% from the top (reduced from 30%)

        // Check which heading is most visible in the viewport
        let maxVisibleHeight = 0;
        let mostVisibleHeading = null;

        headings.forEach(heading => {
          const rect = heading.getBoundingClientRect();

          // If the heading is near the top of the viewport (within threshold)
          if (rect.top < threshold && rect.top > -50) { // Allow slight scrolling past
            currentHeading = heading;
            return; // Exit early, we found our heading
          }
          
          // If no heading is in the threshold zone, use the most visible one
          if (rect.top > 0 && rect.bottom < viewportHeight) {
            const visibleHeight = Math.min(viewportHeight, rect.bottom) - Math.max(0, rect.top);
            if (visibleHeight > maxVisibleHeight) {
              maxVisibleHeight = visibleHeight;
              mostVisibleHeading = heading;
            }
          }
        });

        // If we found a heading in the threshold, use it
        if (currentHeading) {
          // Keep current heading
        }
        // If no heading is in threshold but we found a visible heading, use that
        else if (mostVisibleHeading) {
          currentHeading = mostVisibleHeading;
        }
        // If we've scrolled down but found no headings, use the last one
        else if (window.scrollY > 0 && headings.length > 0) {
          let lastVisibleHeading = null;
          for (let i = headings.length - 1; i >= 0; i--) {
            if (headings[i].getBoundingClientRect().top < 0) {
              lastVisibleHeading = headings[i];
              break;
            }
          }
          currentHeading = lastVisibleHeading || headings[0];
        }
        // Otherwise use the first one
        else if (headings.length > 0) {
          currentHeading = headings[0];
        }

        // Remove active class from all TOC links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.classList.remove('active');
        });

        // If we found a current heading, highlight its TOC link
        if (currentHeading) {
          const id = currentHeading.id;
          const tocLink = document.querySelector(`.toc-content a[href="#${id}"]`);
          if (tocLink) {
            tocLink.classList.add('active');

            // Ensure the active link is visible in the TOC panel if it's open
            if (tocPanel.classList.contains('active')) {
              const tocPanelInner = document.querySelector('.toc-panel-inner');
              if (tocPanelInner) {
                const linkRect = tocLink.getBoundingClientRect();
                const panelRect = tocPanelInner.getBoundingClientRect();

                // If link is outside the visible area of the panel
                if (linkRect.top < panelRect.top || linkRect.bottom > panelRect.bottom) {
                  tocLink.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }
            }
          }
        }
      }

      // Toggle TOC panel with smooth transitions
      function toggleToc(open) {
        const isOpen = open !== undefined ? open : !tocToggle.classList.contains('active');
        if (isOpen) {
          generateToc();
        }
        tocToggle.classList.toggle('active', isOpen);
        tocToggle.setAttribute('aria-expanded', isOpen.toString());
        tocPanel.classList.toggle('active', isOpen);
        tocPanel.setAttribute('aria-hidden', (!isOpen).toString());
      }

      if (tocToggle && tocPanel) {
        tocToggle.addEventListener('click', () => toggleToc());
        tocClose?.addEventListener('click', () => toggleToc(false));
        // Auto-open TOC by default on post pages
        toggleToc(true);
      }

      // Close TOC when clicking outside
      document.addEventListener('click', (e) => {
        if (tocPanel.classList.contains('active') &&
            !tocPanel.contains(e.target) &&
            !tocToggle.contains(e.target) &&
            !tocClose.contains(e.target)) {
          toggleToc(false);
        }
      });

      // Close TOC with ESC key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && tocPanel.classList.contains('active')) {
          toggleToc(false);
        }
      });

      // Set up back button event
      const backButton = document.querySelector('.nav-circle.top-left');
      if (backButton) {
        backButton.addEventListener('click', () => {
          window.history.back();
        });
      }

      // Make the bottom button slightly transparent on scroll
      const bottomButton = document.querySelector('.nav-circle.bottom-center');

      window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
          bottomButton.style.opacity = "0.7";
        } else {
          bottomButton.style.opacity = "1";
        }
      });

      // Generate TOC on page load only if it should be open
      if (window.innerWidth > 768) {
        generateToc();
      } else {
        // On mobile, start with TOC closed
        toggleToc(false);
      }
    });
  </script>
  
  <!-- Load TOC enhancement script -->
  <script src="/scripts/toc-enhancements.js" defer></script>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Make sure scrollbars are at the edge */
  :global(body),
  :global(.blog-post),
  :global(.toc-panel) {
    scrollbar-width: thin;
    overflow-y: auto;
  }

  /* Fix for work-post pages to match work pages */
  :global(body[data-page="work-post"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
  }

  /* Blog Header - Static (not fixed) */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px 0 20px;
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.3rem;
    color: rgba(240, 240, 240, 0.85);
    letter-spacing: -0.01em;
    position: relative;
  }

  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* TOC Toggle Button - Solid white square that becomes hollow on hover */
  .toc-toggle {
    position: fixed;
    top: 30px;
    right: 30px;
    width: 20px; /* Smaller size */
    height: 20px; /* Smaller size */
    background-color: rgba(255, 255, 255, 0.9); /* Solid white */
    border: none;
    border-radius: 3px; /* Slightly rounded corners */
    cursor: pointer;
    z-index: 110;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform var(--transition-duration) var(--easing-standard),
                background-color var(--transition-duration) var(--easing-standard),
                border var(--transition-duration) var(--easing-standard);
    transform-origin: center;
  }

  /* Hover effect - grows and becomes hollow with dots */
  .toc-toggle:hover {
    transform: scale(1.2);
    background-color: transparent;
    border: 1.5px solid rgba(255, 255, 255, 0.9);
  }

  /* Active state (open state) - stays hollow */
  .toc-toggle.active {
    background-color: transparent;
    border: 1.5px solid rgba(255, 255, 255, 0.9);
  }

  /* Dots display */
  .toc-icon-dots {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px; /* Reduced from 3px for smaller size */
    opacity: 0;
    transition: opacity var(--transition-duration) var(--easing-standard);
  }

  /* Show dots on hover or when active */
  .toc-toggle:hover .toc-icon-dots,
  .toc-toggle.active .toc-icon-dots {
    opacity: 1;
  }

  .dot {
    width: 2.5px; /* Smaller dots */
    height: 2.5px; /* Smaller dots */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transition: width var(--transition-duration) var(--easing-standard),
                height var(--transition-duration) var(--easing-standard);
  }

  /* Lines hover effect (active state) */
  .toc-toggle.active:hover .dot {
    width: 10px; /* Expand to lines, slightly smaller */
    height: 1.5px; /* Thinner height for lines */
    border-radius: 1px; /* Less rounded for lines */
  }

  /* TOC Panel - Smoother slide animation and fully off-screen when closed */
  .toc-panel {
    position: fixed;
    top: 0;
    right: -350px; /* Start more off-screen to ensure it fully hides */
    width: 300px;
    height: 100vh;
    z-index: 100;
    overflow: hidden;
    transition: right 0.6s cubic-bezier(0.25, 0.1, 0.25, 1); /* Smoother animation */
  }

  /* Inner panel that gets the blur effect */
  .toc-panel-inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(20, 20, 20, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    padding: 80px 20px 40px;
    overflow-y: auto;
  }

  .toc-panel.active {
    right: 0;
  }

  /* TOC Close Button with Arrow - Animation direction for closing */
  .toc-close {
    position: absolute;
    left: 10px;
    top: 85px;
    background: transparent;
    border: none;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.8rem;
    transition: transform 0.4s cubic-bezier(0.25, 0.1, 0.25, 1),
                color 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    opacity: 0.8;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Rotate the arrow to point right (to close) */
  .toc-close .toc-close-arrow {
    display: inline-block;
    transform: rotate(180deg);
    transition: transform 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .toc-close:hover {
    transform: translateX(-3px);
    opacity: 1;
  }

  .toc-close:hover .toc-close-arrow {
    transform: translateX(2px) rotate(180deg);
  }

  .toc-panel-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 30px;
    font-weight: normal;
    text-align: center;
  }

  /* TOC content styling - Refined for minimal style */
  .toc-content {
    font-family: 'Georgia Custom', Georgia, serif;
    padding-left: 10px;
    text-align: center;
  }

  .toc-list-container {
    display: inline-block;
    text-align: left;
    width: 85%;
  }

  .toc-item {
    margin-bottom: 26px; /* Increased spacing between items */
  }

  .toc-item.toc-h3 {
    padding-left: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px; /* Increased spacing */
    margin-top: 8px; /* Increased spacing from h2 */
  }

  .toc-item.toc-h4 {
    padding-left: 38px;
    font-size: 0.85rem;
    margin-bottom: 18px; /* Increased spacing */
    margin-top: 6px; /* Increased spacing from h3 */
  }

  .toc-content a {
    color: rgba(255, 255, 255, 0.78); /* Reduced opacity for non-active items */
    text-decoration: none;
    display: block;
    line-height: 1.4;
    padding: 5px 8px; /* Increased padding for better clickable area */
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 4px;
  }

  .toc-content a:hover {
    color: rgba(255, 255, 255, 1);
    transform: translateX(5px);
    background-color: rgba(255, 255, 255, 0.08); /* Subtle background on hover */
  }

  .toc-content a.active {
    color: rgba(255, 255, 255, 1);
    transform: translateX(8px);
    position: relative;
    font-size: 1.05em;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.1); /* Clearer active state */
  }

  .toc-content a.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 80%;
    background-color: rgba(255, 255, 255, 0.9); /* Brighter indicator */
    border-radius: 1px;
  }

  .toc-empty {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    font-size: 0.9rem;
    text-align: center;
    padding: 20px 0;
  }

  /* Better content stability during TOC animation */
  :global(body[data-page="blog-post"]),
  :global(body[data-page="work-post"]) {
    overflow-x: hidden;
  }

  .blog-post {
    max-width: 750px;
    margin: 40px auto 100px; /* Reduced top margin since title is now static */
    padding: 0 30px;
    position: relative;
    width: 100%;
    transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
    line-height: 1.7;
  }

  /* More subtle content fade when TOC is open - no transform to maintain stability */
  :global(body.toc-active) .blog-post {
    opacity: 0.8;
  }

  .post-header {
    margin-bottom: 40px;
  }

  .post-title {
    font-size: 2rem;
    font-weight: normal;
    margin-bottom: 10px;
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  .post-date {
    font-size: 0.95rem;
    color: rgba(200, 200, 200, 0.75);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .post-content {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.8;
    letter-spacing: 0.01em;
    font-size: 1.05rem;
  }

  .post-content p {
    margin-bottom: 1.6em;
  }

  .post-content :global(h2) {
    margin-top: 2em;
    margin-bottom: 0.8em;
    font-size: 1.6rem;
    color: rgba(240, 240, 240, 0.98);
  }

  .post-content :global(h3) {
    margin-top: 1.8em;
    margin-bottom: 0.7em;
    font-size: 1.3rem;
  }

  .post-content :global(ul), .post-content :global(ol) {
    margin-bottom: 1.6em;
    padding-left: 1.5em;
  }

  .post-content :global(li) {
    margin-bottom: 0.5em;
  }

  .post-content :global(a) {
    color: rgba(200, 200, 255, 0.9);
    text-decoration: none;
    border-bottom: 1px solid rgba(200, 200, 255, 0.3);
    transition: border-color 0.3s ease, color 0.3s ease;
  }

  .post-content :global(a:hover) {
    color: rgba(210, 210, 255, 1);
    border-bottom-color: rgba(210, 210, 255, 0.7);
  }

  .post-content :global(blockquote) {
    border-left: 3px solid rgba(200, 200, 200, 0.3);
    padding-left: 1.2em;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 1.6em;
    font-style: italic;
    color: rgba(220, 220, 220, 0.85);
  }

  .post-content :global(h2),
  .post-content :global(h3),
  .post-content :global(h4),
  .post-content :global(h5),
  .post-content :global(h6) {
    font-family: 'Georgia Custom', Georgia, serif;
    font-weight: normal;
    margin: 2em 0 0.7em;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.3;
  }

  .post-content :global(h2) {
    font-size: 1.85rem;
    letter-spacing: -0.01em;
  }

  .post-content :global(h3) {
    font-size: 1.6rem;
  }

  .post-content :global(p) {
    margin-bottom: 1.4em;
    font-size: 1.05rem;
  }

  .post-content :global(a) {
    color: rgba(220, 220, 220, 0.95);
    text-decoration: underline;
    text-decoration-color: rgba(200, 200, 200, 0.4);
    text-underline-offset: 2px;
    transition: text-decoration-color 0.2s ease;
  }

  .post-content :global(a:hover) {
    text-decoration-color: rgba(220, 220, 220, 0.95);
  }

  .post-content :global(ul),
  .post-content :global(ol) {
    margin-left: 2em;
    margin-bottom: 1.4em;
  }

  .post-content :global(li) {
    margin-bottom: 0.6em;
  }

  .post-content :global(pre) {
    background-color: rgba(10, 10, 10, 0.6);
    padding: 1.2em;
    border-radius: 3px;
    overflow-x: auto;
    margin: 1.8em 0;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .post-content :global(code) {
    font-family: monospace;
    font-size: 0.95em;
    color: rgba(220, 220, 220, 0.95);
  }

  .post-content :global(blockquote) {
    border-left: 3px solid rgba(200, 200, 200, 0.3);
    padding-left: 1.2em;
    margin-left: 0;
    font-style: italic;
    color: rgba(200, 200, 200, 0.8);
    margin: 1.8em 0;
  }

  /* Heading highlight effect when navigating from TOC */
  .post-content :global(.highlight) {
    animation: highlight-pulse 1.5s ease;
    position: relative;
  }

  .post-content :global(.highlight)::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 80%;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    animation: highlight-bar 1.5s ease;
  }

  @keyframes highlight-pulse {
    0% { transform: scale(1); }
    20% { transform: scale(1.03); }
    100% { transform: scale(1); }
  }

  @keyframes highlight-bar {
    0% { opacity: 0; }
    20% { opacity: 1; }
    100% { opacity: 0; }
  }

  /* Related Posts Styles */
  .related-posts {
    margin-top: 60px;
    padding: 30px;
    background-color: rgba(30, 30, 30, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(200, 200, 200, 0.1);
  }

  .related-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.3rem;
    color: rgba(240, 240, 240, 0.95);
    margin-bottom: 20px;
    font-weight: normal;
  }

  .related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .related-post {
    padding: 15px;
    background-color: rgba(20, 20, 20, 0.4);
    border-radius: 6px;
    transition: transform 0.3s ease, background-color 0.3s ease;
  }

  .related-post:hover {
    transform: translateY(-3px);
    background-color: rgba(30, 30, 30, 0.6);
  }

  .related-post-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.1rem;
    font-weight: normal;
    margin-bottom: 8px;
    line-height: 1.3;
  }

  .related-post-title a {
    color: rgba(230, 230, 230, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .related-post-title a:hover {
    color: rgba(255, 255, 255, 1);
    text-decoration: underline;
    text-underline-offset: 3px;
  }

  .related-post-date {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.85rem;
    color: rgba(180, 180, 180, 0.75);
  }

  @media (max-width: 768px) {
    .related-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Footer Styles */
  .post-footer {
    margin-top: 70px;
    padding-top: 25px;
    border-top: 1px solid rgba(200, 200, 200, 0.15);
    display: flex;
    flex-direction: column;
    gap: 25px;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .post-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 35px;
  }

  /* Use dark buttons */
  .return-link,
  .subscribe-link {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    padding: 0.4rem 0.9rem;
    border-radius: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .return-link:hover,
  .subscribe-link:hover {
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Responsive styles */
  @media (max-width: 1024px) {
    .blog-post {
      max-width: 90%;
    }
  }

  @media (max-width: 768px) {
    .blog-title {
      font-size: 1.2rem;
    }

    .blog-header {
      margin: 45px 0 15px;
    }

    .toc-toggle {
      top: 25px;
      right: 25px;
      width: 18px;
      height: 18px;
    }

    .toc-panel {
      width: 85%; /* Cover most of the screen on mobile */
      right: -100%; /* Fully off-screen when closed on mobile */
    }

    .toc-close {
      top: 75px;
      left: 12px;
    }

    .blog-post {
      margin-top: 20px;
      padding: 0 15px;
    }

    .post-title {
      font-size: 1.8rem;
    }

    /* On mobile, overlay the TOC without pushing content */
    :global(body.toc-active) .blog-post {
      opacity: 0.4;
    }
  }
</style>