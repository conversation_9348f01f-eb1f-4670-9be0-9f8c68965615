---
// Main layout template for all pages
import Navigation from '../components/Navigation.astro';
import MainMenu from '../components/MainMenu.astro';
import '../styles/global.css';
import '../styles/blog-pages.css';

export interface Props {
    pageTitle?: string;
    isHomePage?: boolean; // To control the top-left icon
    accentColor?: string; // e.g., '#3a2c23'
    bgColor?: string;     // e.g., 'rgba(250, 246, 242, 0.9)'
    backgroundImageUrl?: string; // e.g., '/images/concrete.png'
    bodyDataPage?: string; // To set data-page attribute
}

const {
    pageTitle = 'PVB Minimal',
    isHomePage = false, // Default to false if not provided
    accentColor = 'var(--color-accent)', // Default to CSS variable
    bgColor = 'var(--color-bg)',         // Default to CSS variable
    backgroundImageUrl = 'none',         // Default to no image
    bodyDataPage = 'default'             // Default page context
} = Astro.props;

// Construct the background style string
const bodyBgStyle = backgroundImageUrl !== 'none'
    ? `background-image: url(${backgroundImageUrl}); background-color: ${bgColor};`
    : `background-color: ${bgColor};`;

// Construct the inline style for the body
const bodyStyle = `--color-accent: ${accentColor}; --color-bg: ${bgColor}; ${bodyBgStyle}`;
---

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{pageTitle}</title>
    <style is:global>
        /* CSS Variables for consistent styling */
        :root {
            /* Core sizing */
            --circle-size: 18px;
            --circle-bottom-size: 36px;
            --circle-border-width: 1.5px;
            --circle-expand-scale: 1.6;

            /* Timing & Easing */
            --transition-duration: 0.4s;
            --bottom-button-duration: 0.6s;
            --easing-standard: cubic-bezier(0.25, 0.1, 0.25, 1);
            --menu-item-exit-duration: calc(var(--transition-duration) * 0.95);
        }

        @media (max-width: 768px) {
            :root {
                --circle-size: 16px;
                --circle-bottom-size: 32px;
            }
        }
    </style>
</head>
<body data-page={bodyDataPage} style={bodyStyle}>
    <div class="page-transition" id="page-transition"></div>

    <!-- Common logo for all pages -->
    <a href="/" class="logo">pvb</a>

    <div class="content-wrapper">
        <!-- Slot for page-specific content -->
        <slot />
    </div>

    <div class="quote-card" id="quote-card">
        <h3 class="quote-card-title"></h3>
        <p class="quote-card-subtitle"></p>
    </div>

    <!-- Include navigation on all pages -->
    <Navigation isHomePage={isHomePage} />

    <!-- Always include the bottom navigation button -->
    <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu">
        <span class="nav-icon"></span>
    </div>

    <!-- Include main menu on all pages -->
    <MainMenu />

    <script is:inline>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            window.addEventListener('pageshow', () => {
                if (pageTransition) pageTransition.classList.remove('active');
            });

            window.addEventListener('beforeunload', () => {
                if (pageTransition) pageTransition.classList.add('active');
            });

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script>

    <!-- Fix: Use either is:inline or src, not both -->
    <script src="/scripts/main.js" defer></script>
</body>
</html>

<style>
    /* Additional styles specific to the Layout component */
    html, body {
        scroll-behavior: smooth;
        margin: 0;
        padding: 0;
        height: 100%;
        scrollbar-width: thin;
        scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
    }

    /* Ensure scrollbar is always positioned correctly */
    :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
    }

    :global(::-webkit-scrollbar-track) {
        background: transparent;
    }

    :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
    }

    :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
    }

    body {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        transition: background-color var(--transition-duration) var(--easing-standard);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-blend-mode: normal;
        font-family: 'Georgia Custom', Georgia, serif;
    }

    /* Common logo styling for all pages */
    .logo {
        position: fixed;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 1rem;
        font-weight: normal;
        z-index: 10;
        font-family: 'Georgia Custom', Georgia, serif;
        text-decoration: none;
        cursor: pointer;
        color: var(--color-accent);
        transition: color var(--transition-duration) var(--easing-standard);
    }

    .logo:hover {
        opacity: 0.85;
    }

    /* Content wrapper */
    .content-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        transition: filter var(--transition-duration) var(--easing-standard),
                    opacity var(--transition-duration) var(--easing-standard);
        z-index: 1;
    }

    /* Different content wrapper styles for different page types */
    body[data-page="blog"] .content-wrapper,
    body[data-page="blog-post"] .content-wrapper,
    body[data-page="work"] .content-wrapper,
    body[data-page="work-post"] .content-wrapper {
        position: relative;
        justify-content: flex-start;
        align-items: stretch;
        height: auto;
        min-height: 100vh;
    }

    /* About page specific - fix for mobile image */
    body[data-page="about"] {
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;  /* This helps with mobile view */
    }

    /* Prevent content overflow issues */
    .content-wrapper {
        overflow-x: hidden;
    }

    /* Ensure bottom center navigation is always fixed in position */
    .nav-circle.bottom-center {
        position: fixed !important;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
        width: var(--circle-bottom-size);
        height: var(--circle-bottom-size);
        border-radius: 50%;
        border: var(--circle-border-width) solid rgba(50, 50, 50, 0.8);
        background-color: rgba(50, 50, 50, 0.6);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform var(--bottom-button-duration) var(--easing-standard),
                    background-color var(--bottom-button-duration) var(--easing-standard),
                    border-color var(--bottom-button-duration) var(--easing-standard),
                    opacity 0.3s ease;
    }

    /* Icon within bottom center nav */
    .nav-circle.bottom-center .nav-icon {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* Menu Toggle Hover Effect */
    body:not(.menu-active) .nav-circle.bottom-center:hover {
        transform: translateX(-50%) scale(1.5);
    }

    /* Menu Toggle Active Effect */
    body.menu-active .nav-circle.bottom-center {
        background-color: var(--color-accent);
        border-color: var(--color-accent);
    }

    /* Simple page transition layer */
    .page-transition {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #111;
        opacity: 1;
        visibility: visible;
        z-index: 1000;
        transition: opacity var(--transition-duration) var(--easing-standard),
                    visibility 0s var(--transition-duration);
    }

    .page-transition.active {
        opacity: 1;
        visibility: visible;
        transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s;
    }

    /* Not active - hide it completely */
    .page-transition:not(.active) {
        opacity: 0;
        visibility: hidden;
    }

    /* Menu effects */
    body.menu-active .content-wrapper {
        filter: blur(4px);
        opacity: 0.5;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .logo {
            top: 20px;
            font-size: 0.9rem;
        }

        .nav-circle.bottom-center {
            bottom: 25px;
        }
    }
</style>
