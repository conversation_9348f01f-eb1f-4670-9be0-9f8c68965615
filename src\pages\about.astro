---
import Layout from '../layouts/Layout.astro';
import '../styles/blog-pages.css';
---

<Layout
    pageTitle="About | PVB"
    isHomePage={false}
    accentColor="#3a2c23"
    bgColor="#3a2c23"
    backgroundImageUrl="/images/limestone.png"
    bodyDataPage="about"
>
    <div class="blog-header">
        <h1 class="blog-title">about</h1>
    </div>
    <div class="content-container">
        <section class="about-section intro-section">
            <p>This is the main introductory text about me. Keep it concise and engaging. Briefly touch upon what drives you or the purpose of this site.</p>
            <p>This demonstrates how the components can be reused with different properties to change the appearance and behavior.</p>
        </section>

        <section class="about-section detail-section">
            <h2>My Journey</h2>
            <p>Expand on your background, key experiences, and the path that led you here. Use storytelling elements if appropriate.</p>
        </section>

        <section class="about-section detail-section">
            <h2>Skills & Expertise</h2>
            <p>List or describe your core skills, tools you master, and areas you specialize in.</p>
            <ul>
                <li>Skill/Area 1: Brief description.</li>
                <li>Skill/Area 2: Brief description.</li>
                <li>Skill/Area 3: Brief description.</li>
            </ul>
        </section>

        <section class="about-section detail-section">
            <h2>Philosophy</h2>
            <p>Discuss your approach to work, design principles, or core values that guide you.</p>
        </section>
    </div>

    <script is:inline>
      // Set up back button event
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    </script>
</Layout>

<style>
    /* About Title - Static (not fixed) positioned below the logo */

    .content-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
    }

    /* Back to Home Link */
    .back-section {
        margin-bottom: 40px;
    }

    .about-content {
        padding: 20px 0;
    }


    h2 {
        font-family: 'Georgia Custom', Georgia, serif;
        margin: 2.5rem 0 1.2rem;
        font-size: 1.6rem;
        color: #24170f;
        letter-spacing: -0.01em;
    }

    p {
        font-family: 'Georgia Custom', Georgia, serif;
        line-height: 1.2;
        margin-bottom: 1.5rem; /* Increased spacing between paragraphs */
        font-size: 1.1rem;
        color: #2b1d15;
    }

    .future-content-section {
        margin-top: 4rem; /* Increased spacing */
        padding-top: 2.5rem; /* Increased spacing */
        border-top: 1px solid rgba(200, 200, 200, 0.15);
    }

    /* Improved mobile responsiveness */
    @media (max-width: 768px) {

        .content-container {
            padding: 0 20px;
        }


        h2 {
            font-size: 1.4rem;
            margin: 2rem 0 1rem;
        }

        p {
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .future-content-section {
            margin-top: 3rem;
            padding-top: 2rem;
        }
    }

    @media (max-width: 768px) {

        .content-container {
            padding: 0 20px;
            max-width: 90%;
        }

        h2 {
            font-size: 1.3rem;
        }

        .intro-section p {
            font-size: 1.0rem;
        }

        ul {
            padding-left: 20px;
        }
    }

    @media (max-width: 480px) {
        .content-container {
            padding: 0 15px;
        }
    }
</style>
