---
import { getAllPosts, getPostBySlug } from "../../utils/unifiedContent.js";
import BlogPost from "../../layouts/BlogPost.astro";
import Layout from "../../layouts/Layout.astro";

export async function getStaticPaths() {
  // Use the new JSON utility to get all posts
  const allPosts = await getAllPosts();
  if (allPosts && allPosts.length > 0) {
    return allPosts.map(post => ({
      params: { slug: post.slug },
    }));
  }
  // Fallback
  return [
    {
      params: { slug: "hello-world" },
    }
  ];
}

const { slug } = Astro.params;
let post = null;
let errorState = false;

try {
  post = await getPostBySlug(slug);
  if (!post) errorState = true;
} catch (e) {
  errorState = true;
}

---

{errorState ? (
  <Layout
    pageTitle="Blog Post Not Found"
    isHomePage={false}
    accentColor="#f0f0f0"
    bgColor="rgba(20, 20, 20, 0.9)"
    backgroundImageUrl="/images/blackgranite.png"
    bodyDataPage="blog-post"
  >
    <div class="error-container">
      <h1>Blog Post Not Found</h1>
      <p>Sorry, the blog post you're looking for is not available.</p>
      <a href="/blog" class="return-link">Return to Blog</a>
    </div>

    <style>
      .error-container {
        max-width: 650px;
        margin: 130px auto 100px;
        padding: 0 20px;
        text-align: center;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.9);
      }

      p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.8);
      }

      .return-link {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #e0e0e0;
        color: #222222;
        text-decoration: none;
        border-radius: 4px;
        font-family: 'Georgia Custom', Georgia, serif;
        transition: background-color 0.3s ease;
      }

      .return-link:hover {
        background-color: #cccccc;
      }
    </style>
  </Layout>
) : (
  <BlogPost post={post} />
)} 
