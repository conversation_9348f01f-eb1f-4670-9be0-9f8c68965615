---
import Layout from "../../layouts/Layout.astro";
import { getPostsByType } from "../../utils/unifiedContent.js";
import { SITE } from '../../config.js';
import "../../styles/blog-pages.css";

const allPosts = await getPostsByType('blog');
const pageTitle = `all blogs | ${SITE.title}`;
---

<Layout pageTitle={pageTitle} isHomePage={false} accentColor="#f0f0f0" bgColor="rgba(10, 10, 10, 0.94)" backgroundImageUrl="/images/blackgranite.png" bodyDataPage="blog">
  <!-- Page Header -->
  <div class="blog-header">
    <h1 class="blog-title">all blogs</h1>
  </div>

  <!-- Main Content -->
  <div class="page-container">
    <div class="blog-content">
      <div class="archive-container">
        <!-- Sort control -->
        <div class="controls-container">
          <button id="sort-toggle-button" data-order="newest">
            <span class="sort-label">Sort: Newest</span>
            <svg class="sort-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>
          </button>
        </div>

        <!-- Timeline -->
        <div id="timeline-container" class="timeline-container">
          <!-- Timeline content will be rendered here by JavaScript -->
        </div>

        <div class="loader-placeholder" style="text-align: center; margin-top: 2rem; display: none;">Loading...</div>
      </div>
    </div>
  </div>
</Layout>
<style>
  /* Controls and header spacing */
  .controls-container {
    margin-bottom: 1rem;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
  }

  /* Timeline container */
  .timeline-container {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    position: relative;
  }
  .timeline-container::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--color-timeline-line, #444);
  }

  /* Year section */
  .timeline-year-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-top: 0.5rem;
    width: 100%;
    position: relative;
    z-index: 1;
  }
  .timeline-year-marker {
    font-size: 2rem;
    font-family: 'Georgia Custom', Georgia, serif;
    font-weight: 700;
    color: var(--blog-text);
    margin: 0 0 1rem 0;
    letter-spacing: 0.01em;
    text-align: left;
  }

  /* Posts for year */
  .timeline-posts-for-year {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1.2rem;
    width: 100%;
    z-index: 1;
  }

  /* Timeline item */
  .timeline-item {
    position: relative;
    width: 100%;
    margin-bottom: 1.5rem;
    padding-left: 40px;
    opacity: 0;
    transform: translateY(32px) scale(0.98);
    animation: fadeInTimelineItem 0.7s cubic-bezier(.36,1.51,.7,1) forwards;
    animation-delay: var(--delay, 0s);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .timeline-item-link {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 56px;
    gap: 16px;
    background-color: rgba(20,20,20,0.6);
    border: 1px solid rgba(255,255,255,0.08);
    border-radius: 0.75rem;
    transition: box-shadow 0.3s, background 0.3s, transform 0.3s, border-color 0.3s;
    box-shadow: var(--shadow-card);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    width: 100%;
    max-width: 700px;
    text-decoration: none;
    color: inherit;
  }
  .timeline-item-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
  }

  .timeline-content {
    background-color: rgba(20, 20, 20, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-card);
    padding: 1.5rem;
    margin: 0.25rem 0;
    transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
  }
  .timeline-item-link:hover .timeline-content {
    background-color: rgba(20, 20, 20, 0.85);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 30px rgba(255, 255, 255, 0.08);
  }

  .timeline-marker {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background-color: var(--color-timeline-marker, #f0f0f0);
    border: 2px solid var(--color-timeline-line, #444);
    border-radius: 50%;
    pointer-events: none;
  }
  .timeline-marker:hover,
  .timeline-marker:focus {
    transform: translate(-50%, -50%) scale(1.45);
    background-color: var(--color-timeline-marker-hover, #fff);
    border-color: var(--color-timeline-line, #444);
    box-shadow: 0 0 18px 4px rgba(255,255,255,0.5);
    outline: none;
  }

  .timeline-marker:hover + .timeline-item-link,
  .timeline-item:hover .timeline-item-link {
    background: rgba(60,60,60,0.98);
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.18);
    transform: translateY(-2px) translateX(5px);
    border-color: rgba(255,255,255,0.2);
  }
  .timeline-item:hover .timeline-marker {
    transform: translate(-50%, -50%) scale(1.45);
    background-color: var(--color-timeline-marker-hover, #fff);
    border-color: var(--color-timeline-line, #444);
    box-shadow: 0 0 18px 4px rgba(255,255,255,0.5);
  }

  .timeline-item-date {
    display: block;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.6);
    margin-bottom: 0.3rem;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .timeline-item-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--blog-text);
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1.3;
  }
  .timeline-item-excerpt {
    font-size: 1rem;
    line-height: 1.6;
    font-weight: 400;
    color: var(--blog-text-secondary);
    font-family: 'Georgia Custom', Georgia, serif;
    margin: 0;
    max-width: 60ch;
  }

  .timeline-container.fade-out { opacity: 0.3; }
  .timeline-container.fade-in { opacity: 1; }

  #sort-toggle-button {
    background-color: transparent;
    border: 1px solid var(--color-border, #555);
    color: var(--color-text, #f0f0f0);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    transition: background-color var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard), color var(--transition-duration) var(--easing-standard);
  }
  #sort-toggle-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--color-border-hover, #777);
  }
  .sort-label { margin-right: 5px; }
  .sort-icon { width: 14px; height: 14px; transition: transform var(--transition-duration) var(--easing-standard); }
  .sort-icon.asc { transform: rotate(180deg); }

  @keyframes fadeInTimelineItem {
    0% { opacity: 0; transform: translateY(32px) scale(0.96); }
    70% { opacity: 1; transform: translateY(-6px) scale(1.02); }
    100% { opacity: 1; transform: translateY(0) scale(1); }
  }

  @media (max-width: 768px) {
    .page-container { padding: 15px; }
    .timeline-year-marker {
      padding-left: 30px;
      font-size: 1.6rem;
      margin-bottom: 1rem;
    }
    .timeline-year-marker::before { left: 30px; width: 10px; height: 10px; }
    .timeline-item { margin-bottom: 1.5rem; padding-left: 40px; }
    .timeline-marker { left: 30px; }
    .timeline-container::before { left: 30px; }
    .timeline-item-link { padding: 0.8em 1em; max-width: 700px; width: 100%; }
    .timeline-item-title { font-size: 1.1rem; }
    .timeline-item-excerpt { font-size: 0.95rem; line-height: 1.4; }
  }

</style>
<script define:vars={{ allPosts }}>
document.addEventListener('DOMContentLoaded', () => {
  const timelineContainer = document.getElementById('timeline-container');
  const sortToggleButton = document.getElementById('sort-toggle-button');
  const sortLabel = document.querySelector('.sort-label');
  const sortIcon = document.querySelector('.sort-icon');
  const loader = document.querySelector('.loader-placeholder'); // Get the loader element

  let currentSortOrder = 'newest';
  let isRendering = false; // Flag to prevent concurrent renders

  // Helper function to get post date, title, and excerpt with fallback logic
  const getPostData = (post) => {
    const date = new Date(post.published_at || post.data?.pubDatetime || post.data?.pubDate);
    const title = post.title || post.data?.title;
    const excerpt = post.excerpt || post.data?.excerpt || '';
    // Note: The data source (allPosts) might have inconsistent field names.
    // Standardizing the data source itself (e.g., in getPostsByType) would be ideal.
    return { date, title, excerpt };
  };

  // Function to sort posts
  const sortPosts = (posts, order) => {
    return [...posts].sort((a, b) => {
      const dateA = getPostData(a).date.getTime();
      const dateB = getPostData(b).date.getTime();
      return order === 'newest' ? dateB - dateA : dateA - dateB; // Fixed sorting for oldest
    });
  };

  // Function to group posts by year
  const groupPostsByYear = (posts) => {
    return posts.reduce((acc, post) => {
      const year = getPostData(post).date.getFullYear();
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(post);
      return acc;
    }, {});
  };

  // Function to generate HTML for a single timeline item
  const renderTimelineItem = (post) => {
    const { date, title, excerpt } = getPostData(post);
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const url = `/blog/${post.slug}`;
    const postId = post.id || post.slug;
    return `
      <div class="timeline-item" id="post-${postId}">
        <div class="timeline-marker" aria-label="${title}" tabindex="0" data-post-id="post-${postId}"></div>
        <a href="${url}" class="timeline-item-link">
          <div class="timeline-content">
            <span class="timeline-item-date">${formattedDate}</span>
            <h3 class="timeline-item-title">${title}</h3>
            <p class="timeline-item-excerpt">${excerpt}</p>
          </div>
        </a>
      </div>
    `;
  };

  // Function to generate HTML for a year section
  const renderYearSection = (year, posts) => {
  const postsHtml = posts.map(renderTimelineItem).join('');
  return `
    <div class="timeline-year-section" style="position:relative; min-height:80px;">
      <h2 class="timeline-year-marker">${year}</h2>
      <div class="timeline-posts-for-year">
        ${postsHtml}
      </div>
    </div>
  `;
};

  // Main function to render the timeline
  const renderTimeline = () => {
    // start fade-out on sort change
    timelineContainer.classList.add('fade-out');
    if (isRendering) return; // Prevent concurrent renders
    isRendering = true;
    
    console.log('[Timeline] Starting render');
    // Show loader
    loader.style.display = 'block';
    timelineContainer.innerHTML = ''; // Clear current content of the container
    console.log('[Timeline] Cleared existing content');

    // Use a small delay to make the loader visible before rendering starts
    setTimeout(() => {
      const sortedPosts = sortPosts(allPosts, currentSortOrder);
      const postsByYear = groupPostsByYear(sortedPosts);

      const years = Object.keys(postsByYear).sort((a, b) =>
        currentSortOrder === 'newest' ? b - a : a - b
      );

      let html = '';
      if (years.length) {
        // No extra .timeline div needed here, content goes directly into timeline-container
        console.log('[Timeline] Creating timeline content');
        years.forEach(year => {
          html += renderYearSection(year, postsByYear[year]);
        });
      } else {
        html = '<p>No posts found.</p>';
      }

      timelineContainer.innerHTML = html; // Set content directly in the container
      // apply fade-in after render
      timelineContainer.classList.remove('fade-out');
      timelineContainer.classList.add('fade-in');
      setTimeout(() => timelineContainer.classList.remove('fade-in'), 500);

      // Add animation delay and enhance marker interaction
      timelineContainer.querySelectorAll('.timeline-item').forEach((item, i) => {
        item.style.setProperty('--delay', `${i * 0.1}s`);
        console.log(`[Timeline] Applied animation delay to item ${i}`);
      });
      
      // Add click event to markers to navigate to post
      timelineContainer.querySelectorAll('.timeline-marker').forEach(marker => {
        marker.addEventListener('click', (e) => {
          const postId = marker.getAttribute('data-post-id');
          const link = document.querySelector(`#${postId} .timeline-item-link`);
          if (link) link.click();
        });
        
        // Also add keyboard accessibility
        marker.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            const postId = marker.getAttribute('data-post-id');
            const link = document.querySelector(`#${postId} .timeline-item-link`);
            if (link) link.click();
          }
        });
      });

      // Hide loader
      console.log('[Timeline] Render completed');
      loader.style.display = 'none';
      isRendering = false; // Release the lock
    }, 50); // Small delay

  };

  // Event listener for sort button
  sortToggleButton.addEventListener('click', () => {
    currentSortOrder = currentSortOrder === 'newest' ? 'oldest' : 'newest';
    sortToggleButton.dataset.order = currentSortOrder;
    sortLabel.textContent = `Sort: ${currentSortOrder === 'newest' ? 'Newest' : 'Oldest'}`;
    sortIcon.classList.toggle('asc', currentSortOrder === 'oldest');
    // Add ARIA attributes for accessibility
    sortToggleButton.setAttribute('aria-sort', currentSortOrder === 'newest' ? 'descending' : 'ascending');
    renderTimeline();
  });

  // Initial render
  renderTimeline();
});
</script>
