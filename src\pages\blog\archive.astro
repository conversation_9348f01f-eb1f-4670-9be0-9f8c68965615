---
import Layout from "../../layouts/Layout.astro";
import { getPostsByType } from "../../utils/unifiedContent.js";
import { SITE } from '../../config.js';

// Get both blog posts and archive posts if available
let allPosts = [];
try {
  // First try to get posts specifically tagged as 'archive'
  allPosts = await getPostsByType('archive');
  
  // If no archive-specific posts found, use all blog posts
  if (!allPosts || allPosts.length === 0) {
    allPosts = await getPostsByType('blog');
  }
} catch (error) {
  console.error('Error loading posts for archive:', error);
  allPosts = [];
}

const pageTitle = `archives | ${SITE.title}`;
---

<Layout pageTitle={pageTitle} isHomePage={false} accentColor="#f0f0f0" bgColor="rgba(10, 10, 10, 0.94)" backgroundImageUrl="/images/blackgranite.png" bodyDataPage="blog">
  <!-- Blog Header -->
  <div class="blog-header">
    <div class="blog-title">archives</div>
  </div>
  <!-- Main Content -->
  <div class="page-container">
    <div class="blog-content">
      <div class="archive-container">
        <!-- Archive Title is displayed by the blog-header -->

        <!-- Controls: Sort Toggle -->
        <div class="controls-container">
          <button id="sort-toggle-button" data-order="newest">
            <span class="sort-label">Sort: Newest</span>
            <svg class="sort-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></svg>
          </button>
        </div>

        <!-- Timeline -->
        <div id="timeline-container" class="timeline-container">
          <div class="timeline"></div>
        </div>

        <!-- Placeholder for infinite scroll loader if needed later -->
        <div class="loader-placeholder" style="text-align: center; margin-top: 2rem; display: none;">Loading...</div>
      </div>
    </div>
  </div>
</Layout>

<script define:vars={{ allPosts }}>
  document.addEventListener('DOMContentLoaded', () => {
    const timelineContainer = document.getElementById('timeline-container');
    const sortToggleButton = document.getElementById('sort-toggle-button');
    const loaderPlaceholder = document.querySelector('.loader-placeholder');
    const sortLabel = document.querySelector('.sort-label');
    const sortIcon = document.querySelector('.sort-icon');

    let currentSortOrder = 'newest'; // 'newest' or 'oldest'

    // Function to render the timeline
    const renderTimeline = () => {
      // 1. Sort posts based on currentSortOrder
      const sortedPosts = [...allPosts].sort((a, b) => {
        const dateA = new Date(a.published_at || a.data?.pubDatetime || a.data?.pubDate).getTime();
        const dateB = new Date(b.published_at || b.data?.pubDatetime || b.data?.pubDate).getTime();
        return currentSortOrder === 'newest' ? dateB - dateA : dateA - dateB;
      });

      // 2. Group posts by year
      const postsByYear = sortedPosts.reduce((acc, post) => {
        const year = new Date(post.published_at || post.data?.pubDatetime || post.data?.pubDate).getFullYear();
        if (!acc[year]) {
          acc[year] = [];
        }
        acc[year].push(post);
        return acc;
      }, {});

      // 3. Generate HTML
      let timelineHTML = '';
      const years = Object.keys(postsByYear).sort((a, b) => (currentSortOrder === 'newest' ? b - a : a - b));

      if (years.length === 0) {
        timelineHTML = '<p>No posts found in the archive.</p>';
      } else {
        timelineHTML = '<div class="timeline">'; // Start timeline wrapper

        years.forEach(year => {
          timelineHTML += `<div class="timeline-year-section">
                             <h2 class="timeline-year-marker">${year}</h2>
                             <div class="timeline-posts-for-year">`;

          postsByYear[year].forEach(post => {
            const postDate = new Date(post.published_at || post.data?.pubDatetime || post.data?.pubDate);
            const formattedDate = postDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            const title = post.title || post.data?.title;
            const excerpt = post.excerpt || post.data?.excerpt || '';
            const postUrl = `/blog/${post.slug}`;
            const postId = post.id || post.slug;

            timelineHTML += `
              <div class="timeline-item" id="post-${postId}">
                <div class="timeline-marker" aria-label="${title}" tabindex="0" data-post-id="post-${postId}"></div>
                <a href="${postUrl}" class="timeline-item-link">
                  <div class="timeline-content">
                    <span class="timeline-item-date">${formattedDate}</span>
                    <h3 class="timeline-item-title">${title}</h3>
                    <p class="timeline-item-excerpt">${excerpt}</p>
                  </div>
                </a>
              </div>`;
          });

          timelineHTML += `</div></div>`; // Close posts-for-year and year-section
        });

        timelineHTML += '</div>'; // End timeline wrapper
      }

      // 4. Update DOM
      timelineContainer.querySelector('.timeline').innerHTML = timelineHTML;

      // Stagger item animations
      const items = timelineContainer.querySelectorAll('.timeline-item');
      items.forEach((item, idx) => {
        item.style.setProperty('--delay', `${idx * 0.1}s`);
      });
      
      // Add click event to markers to navigate to post
      const markers = document.querySelectorAll('.timeline-marker');
      markers.forEach(marker => {
        marker.addEventListener('click', (e) => {
          const postId = marker.getAttribute('data-post-id');
          const link = document.querySelector(`#${postId} .timeline-item-link`);
          if (link) link.click();
        });
        
        // Also add keyboard accessibility
        marker.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            const postId = marker.getAttribute('data-post-id');
            const link = document.querySelector(`#${postId} .timeline-item-link`);
            if (link) link.click();
          }
        });
      });
    };

    // --- Event Listeners ---

    // Toggle Sort Order
    sortToggleButton.addEventListener('click', () => {
      currentSortOrder = (currentSortOrder === 'newest') ? 'oldest' : 'newest';
      sortToggleButton.dataset.order = currentSortOrder;
      sortLabel.textContent = `Sort: ${currentSortOrder === 'newest' ? 'Newest' : 'Oldest'}`;
      sortIcon.classList.toggle('asc', currentSortOrder === 'oldest');
      renderTimeline();
    });

    // --- Initial Render ---
    renderTimeline();

  });
</script>

<style>
  /* Define animation variables for consistency */
  :root {
    --animation-duration-medium: 0.8s;
    --animation-duration-long: 1s;
    --animation-duration-pulse: 1.5s;
    --easing-ease-out: ease-out;
    --easing-ease-in-out: ease-in-out;
    /* Assuming --transition-duration and --easing-standard are global from Layout */
  }

  .archive-container {
    flex: 1;
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem 1.5rem 4rem 1.5rem; /* Standardize padding */
  }

  .archive-title {
    font-size: 3rem;
    font-family: 'Georgia Custom', Georgia, serif;
    color: var(--blog-text) !important;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--blog-border);
    padding-bottom: 1.5rem;
    text-align: left;
    letter-spacing: -0.01em;
    position: relative;
    margin-top: 10px;
  }

  .controls-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 2rem; /* Reduce margin below controls */
  }

  #sort-toggle-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: var(--blog-bg-tint);
    border: 1px solid var(--blog-border);
    padding: 0.6rem 1.2rem; /* Adjusted padding */
    border-radius: 0.5rem; /* Adjusted border radius */
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem; /* Adjusted font size */
    color: var(--blog-text);
    transition: background var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard); /* Use variables */
  }

  #sort-toggle-button:hover {
    background: var(--blog-toc-active);
    border-color: var(--blog-toc-active);
    cursor: pointer; /* Add cursor pointer */
  }

  .sort-label {
    color: inherit;
  }

  .sort-icon {
    transition: transform var(--transition-duration) var(--easing-standard), stroke var(--transition-duration) var(--easing-standard); /* Use variables */
    transform-origin: center;
    stroke: var(--blog-text);
  }

  .sort-icon.asc {
      transform: rotate(180deg);
  }

  /* --- Timeline Styles --- */

  .timeline-container {
      margin-top: 2rem; /* Reduce margin above timeline */
  }

  .timeline {
      position: relative;
      margin: 0 auto;
      max-width: 700px;
      padding-left: 40px; /* Adjust padding for line */
  }

  /* The main timeline vertical line */
  .timeline::before {
      content: '';
      position: absolute;
      left: 30px; /* Fixed position aligned with all.astro */
      top: 0;
      bottom: 0;
      width: 2px;
      background: linear-gradient(to bottom, transparent 0%, var(--color-timeline-line, #444) 5%, var(--color-timeline-line, #444) 95%, transparent 100%);
      z-index: 1;
      transform-origin: top;
      transform: scaleY(0.1);
      opacity: 0.9;
      mask-image: linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%);
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
      animation: draw-line var(--animation-duration-long) var(--easing-ease-out) forwards; /* Use variables */
  }

  .timeline-year-section {
      position: relative;
      margin-bottom: 4rem; /* Reduce margin between year sections */
  }

  .timeline-year-marker {
      position: relative;
      left: 0;
      padding-left: 50px;
      margin-bottom: 36px;
      margin-top: 40px;
      font-size: 2rem;
      font-weight: bold;
      color: var(--color-text, #f0f0f0);
      font-family: 'Georgia Custom', Georgia, serif;
      letter-spacing: 0.02em;
      text-align: left;
      z-index: 3;
      transition: transform var(--transition-duration) var(--easing-standard), color var(--transition-duration) var(--easing-standard); /* Use variables */
  }
  
  .timeline-year-marker::before {
      content: '';
      position: absolute;
      left: 30px;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      background-color: var(--color-timeline-marker, #f0f0f0);
      border: 3px solid var(--color-timeline-line, #444);
      border-radius: 50%;
      z-index: 5;
      box-shadow: 0 0 16px rgba(255,255,255,0.3);
  }

  .timeline-year-marker:hover {
      transform: scale(1.05); /* Slightly reduced scale on hover */
      color: var(--blog-text-inverse);
  }

  .timeline-posts-for-year {
      position: relative;
  }

  .timeline-item {
      position: relative;
      width: 100%;
      margin-bottom: 32px;
      opacity: 0;
      transform: translateY(32px) scale(0.96);
      animation: fadeInTimelineItem 0.7s cubic-bezier(.36,1.51,.7,1) forwards;
      animation-delay: var(--delay, 0s);
      z-index: 2;
      display: flex;
      align-items: center;
      min-height: 60px;
  }

  /* Timeline item marker (circle) */
  .timeline-marker {
      position: absolute;
      left: 30px;
      top: 50%;
      transform: translate(-50%, -50%) scale(1);
      width: 14px;
      height: 14px;
      background-color: transparent;
      border: 2px solid rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      box-shadow: none;
      transition: transform 0.33s cubic-bezier(.36,1.51,.7,1), background-color 0.25s, box-shadow 0.35s, border-color 0.25s;
      z-index: 4;
      pointer-events: none;
      animation: pulse var(--animation-duration-pulse) var(--easing-ease-in-out) infinite; /* Pulse indefinitely */
      animation-delay: calc(var(--delay) + 0.3s);
      animation-fill-mode: both;
  }

  .timeline-marker:hover,
  .timeline-marker:focus {
      transform: translate(-50%, -50%) scale(1.45);
      background-color: var(--color-timeline-marker-hover, #fff);
      border-color: var(--color-timeline-line, #444);
      box-shadow: 0 0 18px 4px rgba(255,255,255,0.5);
      outline: none;
  }
  
  .timeline-marker:hover + .timeline-item-link,
  .timeline-item:hover .timeline-item-link {
      background: rgba(60,60,60,0.98);
      box-shadow: 0 6px 24px 0 rgba(0,0,0,0.18);
      transform: translateY(-2px) translateX(5px);
      border-color: rgba(255,255,255,0.2);
  }
  
  .timeline-item:hover .timeline-marker {
      transform: translate(-50%, -50%) scale(1.45);
      background-color: var(--color-timeline-marker-hover, #fff);
      border-color: var(--color-timeline-line, #444);
      box-shadow: 0 0 18px 4px rgba(255,255,255,0.5);
  }

  .timeline-content {
      padding: 1.5rem; /* Standardize timeline item content padding */
      background-color: var(--blog-bg-tint);
      border-radius: 0.75rem;
      border: 1px solid var(--blog-border);
      transition: transform var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard); /* Use variables */
  }

  .timeline-item-link {
      position: relative;
      display: flex;
      align-items: center;
      min-height: 56px;
      gap: 16px;
      background: rgba(40,40,40,0.92);
      border-radius: 10px;
      transition: box-shadow 0.3s, background 0.3s, transform 0.3s, border-color 0.3s;
      box-shadow: 0 2px 10px 0 rgba(0,0,0,0.1);
      padding: 1em 1.4em 1em 1em;
      margin-bottom: 8px;
      z-index: 2;
      margin-left: 45px;
      width: calc(100% - 65px);
      transform: translateY(0);
      text-decoration: none;
      color: inherit;
      border: 1px solid transparent;
  }

  .timeline-item-link:hover {
      background: rgba(60,60,60,0.98);
      box-shadow: 0 6px 24px 0 rgba(0,0,0,0.18);
      transform: translateY(-2px) translateX(5px);
      border-color: rgba(255,255,255,0.2);
  }

  .timeline-content:hover {
      transform: translateY(-2px);
      border-color: var(--blog-toc-hover);
      box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
  }

  .timeline-item:hover .timeline-content {
      border-color: var(--blog-toc-hover);
   }

  .timeline-item-date {
      display: block;
      font-size: 0.9rem; /* Adjusted font size */
      color: rgba(240, 240, 240, 0.7); /* Adjusted color opacity */
      margin-bottom: 0.4rem; /* Adjusted margin */
  }

  .timeline-item-title {
      font-size: 1.15rem; /* Adjusted font size */
      font-weight: 600; /* Adjusted font weight */
      margin: 0 0 0.4rem 0; /* Adjusted margin */
      line-height: 1.4; /* Adjusted line height */
  }

  .timeline-item-title a {
      color: var(--blog-text);
      text-decoration: none;
      transition: color var(--transition-duration) var(--easing-standard); /* Use variables */
  }

  .timeline-item-title a:hover {
      color: var(--blog-text);
  }

  .timeline-item-excerpt {
      font-size: 0.95rem; /* Adjusted font size */
      color: rgba(240, 240, 240, 0.85); /* Adjusted color opacity */
      line-height: 1.6; /* Adjusted line height */
      margin: 0;
  }

  .archive-container a {
    color: var(--blog-text);
    text-decoration: none;
    transition: color var(--transition-duration) var(--easing-standard); /* Use variables */
  }

  .archive-container a:hover {
    color: var(--blog-text-inverse);
  }

  @keyframes draw-line {
    from { transform: scaleY(0.1); }
    to { transform: scaleY(1.1); }
  }

  @keyframes fadeInTimelineItem {
    0% {
      opacity: 0;
      transform: translateY(32px) scale(0.96);
    }
    70% {
      opacity: 1;
      transform: translateY(-6px) scale(1.02);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
  }

  /* Responsive adjustments for timeline */
  @media (max-width: 768px) {
    .timeline {
      padding-left: 15px; /* Responsive padding adjustment */
    }
    .timeline::before {
      left: 15px; /* Responsive line position adjustment */
    }
    .timeline-year-marker {
      padding-left: 30px;
      font-size: 1.6rem;
      margin-bottom: 30px;
    }
    
    .timeline-year-marker::before {
      left: 15px;
      width: 18px;
      height: 18px;
    }
    
    .timeline-item {
      margin-bottom: 30px;
    }
    .timeline-marker {
      left: 15px;
    }
    .timeline-item-link {
      margin-left: 30px;
      width: calc(100% - 40px);
      padding: 0.8em 1em;
    }
    .timeline-item-title {
      font-size: 1.1rem;
    }
    .timeline-item-excerpt {
      font-size: 0.95rem;
      line-height: 1.4;
    }
    .archive-container {
      padding: 1rem 0.5rem;
    }
  }
</style>
