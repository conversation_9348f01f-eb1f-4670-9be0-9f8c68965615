---
import Layout from "../../../layouts/Layout.astro";
import { getPostsByType } from "../../../utils/unifiedContent";
import "../../../styles/blog-pages.css";

// Fetch archive posts from JSON
let archivePosts = [];
try {
  archivePosts = await getPostsByType('archive');
  // Sort posts by published_at date (newest first)
  archivePosts = archivePosts.sort((a, b) => {
    const dateA = new Date(b.published_at).getTime();
    const dateB = new Date(a.published_at).getTime();
    return dateA - dateB;
  });
} catch (error) {
  console.error('Error fetching archive posts:', error);
}
---

<Layout
  pageTitle="Archive | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="archive"
>
  <!-- Archive title -->
  <div class="blog-header">
    <h1 class="blog-title">archive</h1>
  </div>

  <!-- Content Container -->
  <div class="archive-container">
    <!-- Back to Blog -->
    <div class="back-section">
      <a href="/blog" class="back-to-blog">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 12H5"></path>
          <path d="M12 19l-7-7 7-7"></path>
        </svg>
        <span>back to blog</span>
      </a>
    </div>

    <div class="archive-list">
      {archivePosts.length > 0 ? (
        <ul class="archive-items">
          {archivePosts.map((post: any) => (
            <li class="archive-item">
              <a href={`/blog/${post.slug}/`} class="archive-link">
                <span class="archive-title">{post.title}</span>
                <span class="archive-date">{new Date(post.published_at).toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'})}</span>
              </a>
            </li>
          ))}
        </ul>
      ) : (
        <p class="no-posts-message">No archived posts found.</p>
      )}
    </div>
  </div>

  <style>


    .archive-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .back-section {
      margin-bottom: 2rem;
    }

    .back-to-blog {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--color-accent);
      text-decoration: none;
      font-family: 'Georgia Custom', Georgia, serif;
      transition: opacity 0.2s ease;
    }

    .back-to-blog:hover {
      opacity: 0.8;
    }

    .archive-list {
      margin-bottom: 3rem;
    }

    .archive-items {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .archive-item {
      margin-bottom: 1rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 1rem;
    }

    .archive-item:last-child {
      border-bottom: none;
    }

    .archive-link {
      display: flex;
      justify-content: space-between;
      align-items: center;
      text-decoration: none;
      color: var(--color-text);
      transition: color 0.2s ease;
    }

    .archive-link:hover {
      color: var(--color-accent);
    }

    .archive-title {
      font-family: 'Georgia Custom', Georgia, serif;
      font-size: 1.1rem;
    }

    .archive-date {
      font-size: 0.85rem;
      color: var(--color-text-secondary);
    }

    .no-posts-message {
      font-family: 'Georgia Custom', Georgia, serif;
      color: var(--color-text-secondary);
      font-style: italic;
    }

    @media (max-width: 768px) {
      .archive-link {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

    }
  </style>
</Layout>