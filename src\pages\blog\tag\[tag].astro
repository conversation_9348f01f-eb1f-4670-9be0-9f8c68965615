---
import { getPostsByTag } from '../../../utils/unifiedContent';
import Layout from '../../../layouts/Layout.astro';
import BlogPostCard from '../../../components/BlogPostCard.astro';
import { getAllUniqueTags, slugifyStr } from '../../../utils/unifiedContent';

export async function getStaticPaths() {
  const allTags = await getAllUniqueTags();
  const paths = allTags.map(tag => ({
    params: { tag: slugifyStr(tag) },
    props: { tagName: tag },
  }));
  return paths;
}

const { tag } = Astro.params;
const { tagName } = Astro.props;

const posts = await getPostsByTag(tag);

const pageTitle = `Tag: ${tagName} | Blog | PVB`;
---

<Layout
  pageTitle={pageTitle}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <div class="blog-header">
    <div class="blog-title">blog</div>
  </div>

  <div class="page-container">
    <div class="blog-sidebar">
      {/* Sidebar content like search, archives, subscribe, and tags filter */}
      {/* This could potentially be a reusable component */}
      <div class="search-container sidebar-section">
        <a href="/search" class="search-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <div class="archive-container sidebar-section">
        <a href="/blog/archives" class="archive-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <path d="M7 17l4-4 4 4 4-4"></path>
            <path d="M7 11l4-4 4 4 4-4"></path>
          </svg>
          <span>archives</span>
        </a>
      </div>

      <div class="subscribe-container sidebar-section">
        <a href="#" class="subscribe-link sidebar-link">subscribe by email</a>
      </div>

      {/* Tags Filter (Collapsible) - Consider making this a component */}
      <div class="tags-container">
        <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list">
          <span class="tags-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z"></path>
              <path d="M6 9.01V9"></path>
            </svg>
            <span>tags</span>
          </span>
          <span class="toggle-icon">
            <span class="line hor"></span>
            <span class="line vert"></span>
          </span>
        </button>
        <div class="tags-list" id="tags-list">
          <a href="/tags" class="tag-link all-tags-link">all tags</a>
          {/* This part needs to fetch all tags */}
          {/* For now, placeholder or fetch all tags here */}
          {/* Example: */}
          {/* {allTags.map(tag => <a href={`/blog/tag/${slugifyStr(tag)}`} class="tag-link">{tag}</a>)} */}
        </div>
      </div>
    </div>

    <div class="blog-content">
      <h1 class="section-title">Posts tagged with "{tagName}"</h1>
      {posts && posts.length > 0 ? (
        <ul id="blog-post-list">
          {posts.map(post => (
            <li class="post-list-item">
              <BlogPostCard post={post} />
            </li>
          ))}
        </ul>
      ) : (
        <p>No posts found with this tag.</p>
      )}
    </div>
  </div>
</Layout>

<style>
  /* Add or import necessary styles */
  /* You might want to reuse styles from blog.astro */
</style>

<script is:inline>
  // Add necessary JS for sidebar toggle if not a component
</script>
