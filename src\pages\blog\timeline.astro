---
import { getPostsByType } from "../../utils/unifiedContent";
import Layout from "../../layouts/Layout.astro";

// Fetch posts at render time
let postsByYear: Record<string, any[]> = {};
let sortedYears: string[] = [];
let error = false;

try {
  // Get all archive posts (posts with 'archive' as first tag)
  const archivePosts = await getPostsByType('archive');

  // Group posts by year
  postsByYear = archivePosts.reduce((acc: Record<string, any[]>, post) => {
    const pubDate = post.data?.pubDatetime || post.published_at;
    const year = new Date(pubDate).getFullYear().toString();
    if (!acc[year]) {
      acc[year] = [];
    }
    acc[year].push(post);
    return acc;
  }, {});

  // Sort years in descending order (newest first)
  sortedYears = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));

  // Sort posts within each year by date (newest first)
  for (const year of sortedYears) {
    postsByYear[year].sort((a, b) => {
      const dateA = new Date(a.data?.pubDatetime || a.published_at).getTime();
      const dateB = new Date(b.data?.pubDatetime || b.published_at).getTime();
      return dateB - dateA;
    });
  }
} catch (e) {
  console.error("Error fetching posts:", e);
  error = true;
}
---

{error ? (
  <Layout
    pageTitle="Archive Error | Blog | PVB"
    isHomePage={false}
    accentColor="#f0f0f0"
    bgColor="rgba(10, 10, 10, 0.94)"
    backgroundImageUrl="/images/blackgranite.png"
    bodyDataPage="blog"
  >
    <div class="error-container">
      <h1>Error Loading Archive</h1>
      <p>Sorry, there was an error loading the blog archive.</p>
      <a href="/blog" class="back-to-blog">&larr; back to blog</a>
    </div>

    <style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }
    </style>
  </Layout>
) : (
  <Layout
    pageTitle="Archive | PVB"
    isHomePage={false}
    accentColor="#f0f0f0"
    bgColor="rgba(10, 10, 10, 0.94)"
    backgroundImageUrl="/images/blackgranite.png"
    bodyDataPage="blog"
  >
    <!-- Blog title - Static (not fixed) -->
    <div class="blog-header">
      <div class="blog-title">archive</div>
    </div>

    <!-- Full Page Content Container -->
    <div class="timeline-container">
      <!-- Back to Blog -->
      <div class="back-section">
        <a href="/blog" class="back-to-blog">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 12H5"></path>
            <path d="M12 19l-7-7 7-7"></path>
          </svg>
          <span>back to blog</span>
        </a>
      </div>

      <!-- Timeline Content -->
      <div class="timeline-content">
        {sortedYears.map(year => (
          <div class="year-section">
            <h2 class="year-heading">{year}</h2>
            <div class="year-posts">
              {postsByYear[year].map(post => (
                <div class="timeline-post">
                  <div class="post-date">
                    {new Date(post.data?.pubDatetime || post.published_at).toLocaleDateString('en-US', {month: 'short', day: 'numeric'})}
                  </div>
                  <h3 class="post-title">
                    <a href={`/blog/${post.slug}`}>{post.data?.title || post.title}</a>
                  </h3>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>

    <style>
      /* Global Scrollbar Styling */
      :global(html) {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
      }

      :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
      }

      :global(::-webkit-scrollbar-track) {
        background: transparent;
      }

      :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
      }

      :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
      }

      /* Blog Header - Static (not fixed) */
      .blog-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px;
      }

      .blog-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.5rem;
        color: rgba(240, 240, 240, 0.9);
        letter-spacing: -0.01em;
        position: relative;
      }

      /* Add subtle underline to blog title */
      .blog-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: rgba(240, 240, 240, 0.4);
      }

      /* Timeline Container */
      .timeline-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
      }

      /* Back to Blog Link */
      .back-section {
        margin-bottom: 40px;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        width: fit-content;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }

      /* Year Sections */
      .year-section {
        margin-bottom: 50px;
      }

      .year-heading {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.95);
        margin-bottom: 20px;
        font-weight: normal;
        position: relative;
        padding-bottom: 10px;
      }

      .year-heading::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 1px;
        background-color: rgba(200, 200, 200, 0.3);
      }

      /* Post Items */
      .timeline-post {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
      }

      .post-date {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.9rem;
        color: rgba(180, 180, 180, 0.75);
        margin-bottom: 5px;
      }

      .post-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.2rem;
        font-weight: normal;
        margin: 0;
      }

      .post-title a {
        color: rgba(220, 220, 220, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .post-title a:hover {
        color: rgba(255, 255, 255, 1);
        text-decoration: underline;
        text-underline-offset: 3px;
        text-decoration-color: rgba(200, 200, 200, 0.4);
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.3rem;
        }

        .timeline-container {
          padding: 0 20px;
        }

        .year-heading {
          font-size: 1.6rem;
        }

        .post-title {
          font-size: 1.1rem;
        }
      }
    </style>
  </Layout>
)}