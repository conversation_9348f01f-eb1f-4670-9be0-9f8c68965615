---
import { getPostsByType } from "../utils/unifiedContent";
import Layout from "../layouts/Layout.astro";
import publications from "../data/publications.json";
import "../styles/work-page.css";
import { getCollection } from 'astro:content';
import ProjectCard from '../components/ProjectCard.astro';


const workPosts = await getPostsByType('work');

let projects = workPosts.map((post, idx) => {
  const tags = Array.isArray(post.tags) ? post.tags.map(t => typeof t === 'object' ? (t.slug || t.name).toLowerCase() : t.toLowerCase()) : [];
  const domain = tags.find(t => !['work','blog','archive'].includes(t)) || 'General';
  const cover = post.feature_image || `https://placehold.co/600x400/0d0d0d/f0f0f0?text=${encodeURIComponent(post.title.split(' ')[0])}`;
  // Initialize lenses as an empty array here; it will be populated correctly below
  return { id: idx + 1, title: post.title, description: post.excerpt || post.custom_excerpt || '', tags, domain, cover };
});

// Define our fixed lens categories
const lensCategories = [
  'Research',
  'Featured',
  'Latest',
  'Experimental',
  'Formal Projects',
  'Software'
];

// Map tags to lens IDs (expanded to include more common tags and their corresponding lens IDs)
const tagToLensIdMap = {
  'research': 'research',
  'featured': 'featured',
  'latest': 'latest',
  'experimental': 'experimental',
  'formal-projects': 'formal-projects', // Ensure consistency
  'software': 'software',
  'ai': 'research', // Example: Map 'AI' tag to 'Research' lens
  'machine-learning': 'research',
  'data-science': 'research',
  'web-development': 'software',
  'mobile-development': 'software',
  'design': 'experimental',
  'art': 'experimental',
  'creative': 'experimental',
  'tool': 'software' // Assuming 'tool' tag maps to 'Software' lens
};

// Reassign lenses based on tag mapping
projects = projects.map(project => {
  const newLenses = [];
  
  // Add 'latest' to all projects by default
  newLenses.push('latest');

  // Add 'featured' to the first project (or based on a 'featured' tag if it exists)
  // For now, keeping the first project as featured for demonstration
  if (project.id === 1 || project.tags.some(tag => tag.toLowerCase() === 'featured')) {
    newLenses.push('featured');
  }

  // Map tags to lenses - safely handle missing tags and ensure all relevant tags are included
  (project.tags || []).forEach(tag => {
    const normalizedTag = tag.toLowerCase();
    // Add the original tag as a lens if it's a direct category
    if (lensCategories.map(lc => lc.toLowerCase().replace(/\s+/g, '-')).includes(normalizedTag)) {
      newLenses.push(normalizedTag);
    }
    // Add mapped lens IDs
    if (tagToLensIdMap[normalizedTag]) {
      newLenses.push(tagToLensIdMap[normalizedTag]);
    }
  });
  
  return {
    ...project,
    lenses: [...new Set(newLenses)] // Remove duplicates
  };
});

// Create lenses array, ensuring 'All Topics' is handled correctly and appears first if desired
const lenses = [
  { id: 'all-topics', name: 'All Topics', type: 'redirect' }, // Moved to the beginning
  ...lensCategories.map(name => ({
    id: name.toLowerCase().replace(/\s+/g, '-'),
    name,
    type: name === 'All Topics' ? 'redirect' : undefined // Redundant now, but kept for safety
  }))
];

// Filter out duplicate 'All Topics' if it was already in lensCategories
const uniqueLenses = [];
const seenLensIds = new Set();
lenses.forEach(lens => {
  if (!seenLensIds.has(lens.id)) {
    uniqueLenses.push(lens);
    seenLensIds.add(lens.id);
  }
});

const finalLenses = uniqueLenses; // Use this for pageData

// New line: Stringify all the data you need on the client
const pageData = JSON.stringify({ lenses: finalLenses, projects, publications });

---

<Layout pageTitle="Work | PVB" isHomePage={false} accentColor="#f0f0f0" bgColor="rgba(10, 10, 10, 0.94)" backgroundImageUrl="/images/blackgranite.png" bodyDataPage="work">
  <div class="p-4 sm:p-6 md:p-8 max-w-7xl mx-auto mt-0">
    <header class="text-center pt-14 pb-10">
      <h1 class="blog-header blog-title text-white tracking-tight">work</h1>
      <p class="mt-4 text-base text-gray-200 max-w-2xl mx-auto">
        A curated collection of projects spanning deep research, practical software, and experimental concepts. Select a lens to explore.
      </p>
    </header>

    <main id="work-page-container" data-page-data={pageData}>
      <div id="lens-rail-viewport" class="w-full h-12 mb-8 overflow-hidden">
        <div id="lens-rail" class="flex items-center h-full absolute top-0 left-0 space-x-3 px-3"></div>
      </div>

      <div id="content-display" class="min-h-[60vh] transition-opacity duration-500 grid md:grid-cols-2 lg:grid-cols-3 gap-8"></div>

      <section id="publications" class="mt-24 py-16 border-t border-gray-800">
        <div class="flex items-center justify-center mb-12">
          <div class="flex-grow h-px bg-gray-800"></div>
          <h2 class="text-3xl md:text-4xl mx-6 text-center">Research & Publications</h2>
          <div class="flex-grow h-px bg-gray-800"></div>
        </div>
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {publications.map((pub) => (
            <div class="bg-gray-900/30 p-5 rounded-lg hover:bg-gray-900 transition-colors duration-300">
              <h3 class="font-medium">{pub.title}</h3>
              <p class="text-sm text-gray-500 mt-1">{pub.journal}, {pub.year}</p>
              <a href={pub.url} class="text-sm text-sky-400 hover:text-sky-300 mt-2 inline-block">View Publication →</a>
            </div>
          ))}
        </div>
      </section>

      <section id="distribution-chart" class="mt-16 py-16">
        <h2 class="text-3xl md:text-4xl text-center mb-8">Project Domain Distribution</h2>
        <div class="chart-container relative mx-auto" style="height:300px; max-width:300px;">
          <canvas id="project-dist-chart"></canvas>
        </div>
        <p class="text-center text-gray-500 mt-4">An overview of the domains my work encompasses.</p>
      </section>
    </main>
  </div>

  <script>
    import initWorkPage from "../scripts/work-animation.js";

    // Find the element holding our data
    const container = document.getElementById('work-page-container');
    
    // Parse the JSON data from the attribute
    const pageData = JSON.parse(container.dataset.pageData);
    
    // Call the initialization function with the data
    initWorkPage(pageData);
  </script>
</Layout>
