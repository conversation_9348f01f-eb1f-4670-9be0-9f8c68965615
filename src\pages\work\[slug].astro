---
import BlogPostLayout from "../../layouts/BlogPost.astro";
import { getPostsByType } from "../../utils/unifiedContent";

// Generate static paths for all work items (single-segment slug)
export async function getStaticPaths() {
  const workEntries = await getPostsByType("work");
  return workEntries.map(entry => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

interface Props {
  entry: any;
}

const { entry } = Astro.props;

// Project-specific info (repository URL, live site, status)
const repoUrl = entry.codeinjection_head?.match(/repo_url:\s*([^\s"]+)/)?.at(1) || null;
const liveUrl = entry.codeinjection_head?.match(/live_url:\s*([^\s"]+)/)?.at(1) || null;
const status = entry.codeinjection_head?.match(/status:\s*([^\s"]+)/)?.at(1) || "Completed";

function formatDate(date: Date) {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  });
}
---

<BlogPostLayout post={entry} />

<style>
  /* Styles omitted for brevity; identical to existing [ ...slug ].astro */
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const returnLink = document.querySelector('.return-link');
    if (returnLink) {
      returnLink.textContent = '← Work';
      returnLink.setAttribute('href', '/work');
    }
  });
</script>
