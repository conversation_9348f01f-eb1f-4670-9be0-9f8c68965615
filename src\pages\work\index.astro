---
import Layout from "../../layouts/Layout.astro";
import { getPostsByType } from "../../utils/unifiedContent.js";
import { SITE } from '../../config.js';

const allPosts = await getPostsByType('work');
const pageTitle = `work | ${SITE.title}`;
---

<Layout pageTitle={pageTitle} isHomePage={false} accentColor="#f0f0f0" bgColor="rgba(0,0,0,0.85)" backgroundImageUrl="/images/obsidian.png" bodyDataPage="work">
  <!-- Page Header -->
  <div class="blog-header">
    <div class="blog-title">work</div>
  </div>

  <!-- Main Content -->
  <div class="page-container">
    <div class="blog-content">
      <div class="archive-container">
        <!-- Sort control -->
        <div class="controls-container">
          <button id="sort-toggle-button" data-order="newest">
            <span class="sort-label">Sort: Newest</span>
            <svg class="sort-icon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="5" x2="12" y2="19" />
              <polyline points="19 12 12 19 5 12" />
            </svg>
          </button>
        </div>

        <!-- Timeline -->
        <div id="timeline-container" class="timeline-container">
          <div class="timeline"></div>
        </div>

        <div class="loader-placeholder" style="text-align: center; margin-top: 2rem; display: none;">Loading...</div>
      </div>
    </div>
  </div>
</Layout>

<script define:vars={{ allPosts }}>
document.addEventListener('DOMContentLoaded', () => {
  const timelineContainer = document.getElementById('timeline-container');
  const sortToggleButton = document.getElementById('sort-toggle-button');
  const sortLabel = document.querySelector('.sort-label');
  const sortIcon = document.querySelector('.sort-icon');
  let currentSortOrder = 'newest';
  const renderTimeline = () => {
    const sorted = [...allPosts].sort((a, b) => {
      const aT = new Date(a.published_at || a.data?.pubDatetime || a.data?.pubDate).getTime();
      const bT = new Date(b.published_at || b.data?.pubDatetime || b.data?.pubDate).getTime();
      return currentSortOrder === 'newest' ? bT - aT : aT - bT;
    });
    const byYear = sorted.reduce((acc, p) => { const y = new Date(p.published_at || p.data?.pubDatetime || p.data?.pubDate).getFullYear(); (acc[y] ||= []).push(p); return acc; }, {});
    let html = '';
    const years = Object.keys(byYear).sort((a, b) => currentSortOrder === 'newest' ? b - a : a - b);
    if (years.length) {
      html = '<div class="timeline">';
      years.forEach(year => {
        html += `<div class="timeline-year-section"><h2 class="timeline-year-marker">${year}</h2><div class="timeline-posts-for-year">`;
        byYear[year].forEach(p => {
          const d = new Date(p.published_at || p.data?.pubDatetime || p.data?.pubDate);
          const fmt = d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          const title = p.title || p.data?.title;
          const excerpt = p.excerpt || p.data?.excerpt || '';
          const url = `/work/${p.slug}`;
          html += `<div class="timeline-item"><div class="timeline-marker"></div><a href="${url}" class="timeline-item-link"><div class="timeline-content"><span class="timeline-item-date">${fmt}</span><h3 class="timeline-item-title">${title}</h3><p class="timeline-item-excerpt">${excerpt}</p></div></a></div>`;
        });
        html += '</div></div>';
      });
      html += '</div>';
    } else {
      html = '<p>No work items found.</p>';
    }
    timelineContainer.querySelector('.timeline').innerHTML = html;
    timelineContainer.querySelectorAll('.timeline-item').forEach((it, i) => it.style.setProperty('--delay', `${i * 0.1}s`));
  };
  sortToggleButton.addEventListener('click', () => {
    currentSortOrder = currentSortOrder === 'newest' ? 'oldest' : 'newest';
    sortToggleButton.dataset.order = currentSortOrder;
    sortLabel.textContent = `Sort: ${currentSortOrder === 'newest' ? 'Newest' : 'Oldest'}`;
    sortIcon.classList.toggle('asc', currentSortOrder === 'oldest');
    renderTimeline();
  });
  renderTimeline();
});
</script>
