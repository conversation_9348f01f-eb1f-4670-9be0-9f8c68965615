import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger"; // Keep for safety, in case other components rely on its global registration
import Draggable from 'gsap/Draggable';
gsap.registerPlugin(ScrollTrigger, Draggable);

// --- NEW: Debounce helper function to fix runtime error ---
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// --- NEW: Helper function for performant snapping ---
function binarySearchClosest(arr, target) {
    let low = 0, high = arr.length - 1, mid, closest = high;
    while (low <= high) {
        mid = Math.floor((low + high) / 2);
        if (Math.abs(arr[mid] - target) < Math.abs(arr[closest] - target)) {
            closest = mid;
        }
        if (arr[mid] < target) {
            low = mid + 1;
        } else if (arr[mid] > target) {
            high = mid - 1;
        } else {
            return mid;
        }
    }
    return closest;
}

export default function initWorkPage({ lenses, projects, publications }) {
    const ALL_LENSES = lenses; // Keep original for lookups by ID
    const LENSES = ALL_LENSES.filter(l => l.type !== 'redirect'); // Exclude redirect lens from pills
    const CANONICAL_PROJECTS = projects.map(p => ({
        ...p,
        lenses: p.lenses?.map(l => l.toLowerCase()) ?? []
    }));

    let activeIndex = 0;
    const appState = { isAnimating: false };

    const railViewport = document.getElementById('lens-rail-viewport');
    const rail = document.getElementById('lens-rail');
    const contentDisplay = document.getElementById('content-display');
    let pills = [];
    let draggableInstance;

    function createPill(lens) {
        const pill = document.createElement('button');
        pill.className = `pill px-5 py-2 text-sm font-medium rounded-full transition-opacity duration-300 bg-gray-800/50 text-gray-300 hover:opacity-85 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-gray-400`;
        pill.dataset.lensId = lens.id;
        pill.innerHTML = `<span class="relative z-10">${lens.name}</span><div class="pill-glint"></div><div class="pill-border-trace"></div>`;
        pill.addEventListener('click', () => handlePillClick(pill));
        return pill;
    }

    function renderLenses() {
        rail.innerHTML = '';
        [...LENSES, ...LENSES, ...LENSES].forEach(lens => rail.appendChild(createPill(lens)));
        pills = gsap.utils.toArray('#lens-rail .pill');
    }

    function updatePillStyles(newActiveIndex) {
        activeIndex = newActiveIndex % LENSES.length;
        pills.forEach((p, i) => {
            const isSelected = (i % LENSES.length) === activeIndex;
            p.classList.toggle('unfocused', !isSelected);
            p.classList.toggle('bg-gray-700', isSelected);
            p.classList.toggle('text-white', isSelected);
        });
    }

    function handlePillClick(pillEl) {
        if (appState.isAnimating || (draggableInstance && draggableInstance.isDragging)) return;
        appState.isAnimating = true;

        const clickedIndex = pills.indexOf(pillEl);
        const lensId = pillEl.dataset.lensId;
        const lensObj = ALL_LENSES.find(l => l.id === lensId);
        const newActive = clickedIndex % LENSES.length;

        gsap.killTweensOf(rail);
        const xTarget = viewportCenter - pillCenters[clickedIndex];

        gsap.to(rail, {
            x: xTarget,
            duration: 0.6,
            ease: 'power1.out',
            onComplete: () => {
                if (activeIndex !== newActive) {
                    updatePillStyles(newActive);
                    playHighlightAnimation(pillEl);

                    gsap.to(contentDisplay, {
                        opacity: 0,
                        duration: 0.2,
                        onComplete: () => {
                            renderProjects(lensId);
                            gsap.to(contentDisplay, {
                                opacity: 1,
                                duration: 0.3,
                                onComplete: () => appState.isAnimating = false
                            });
                        }
                    });
                } else {
                    playHighlightAnimation(pillEl);
                    appState.isAnimating = false;
                }
            }
        });
    }

    function playHighlightAnimation(pillElement) {
        const glint = pillElement.querySelector('.pill-glint');
        const borderTrace = pillElement.querySelector('.pill-border-trace');

        if (glint) {
            gsap.fromTo(glint,
                { transform: 'skewX(-25deg) translateX(-250%)' },
                {
                    transform: 'skewX(-25deg) translateX(350%)',
                    duration: 0.8,
                    ease: 'power2.out'
                }
            );
        }

        if (borderTrace) {
            gsap.fromTo(borderTrace,
                { opacity: 0, '--angle': '90deg' },
                {
                    opacity: 1,
                    '--angle': '450deg',
                    duration: 0.6,
                    ease: 'power2.inOut',
                    onComplete: () => {
                        gsap.to(borderTrace, { opacity: 0, duration: 0.3 });
                    }
                }
            );
        }
    }

    const recenterRailDebounced = debounce(() => {
        const center = pillCenters[LENSES.length + activeIndex];
        if(isNaN(center)) return;
        gsap.to(rail, { x: viewportCenter - center, duration: 0.4, ease: 'power2.out' });
    }, 100);

    function setupDraggableRail() {
         if (draggableInstance) draggableInstance.kill();
         const wrapWidth = rail.scrollWidth / 3;
         draggableInstance = Draggable.create(rail, {
            type: 'x',
            edgeResistance: 0.65,
            inertia: true,
            onDragStart: () => gsap.killTweensOf(rail),
            bounds: { minX: -wrapWidth * 2, maxX: -wrapWidth },
            snap: {
                x: (endValue) => {
                    const target = endValue * -1 + viewportCenter;
                    const idx = binarySearchClosest(pillCenters, target);
                    return viewportCenter - pillCenters[idx];
                }
            },
            onDragEnd: recenterRailDebounced,
            onThrowComplete: recenterRailDebounced
        })[0];
    }

    function renderProjects(lensId) {
        const prefersReduced = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        const projectsToRender = CANONICAL_PROJECTS.filter(p =>
            p.lenses.includes(lensId)
        );

        const liveRegion = document.getElementById('work-live-region');
        if (liveRegion) {
            const count = projectsToRender.length;
            const label = ALL_LENSES.find(l => l.id === lensId)?.name ?? lensId;
            liveRegion.textContent = `Showing ${count} project${count !== 1 ? 's' : ''} for ${label}.`;
        }

        if (projectsToRender.length === 0) {
            contentDisplay.innerHTML = `<div class="empty-state"><h3>No projects found.</h3></div>`;
            return;
        }

        contentDisplay.innerHTML = `<div class="project-grid" role="list">${projectsToRender.map(p => {
             const tagsHTML = p.tags?.length ? `<div class="project-card-tags">${p.tags.map(tag => `<span>#${tag}</span>`).join('')}</div>` : '';
             return `<a href="#" class="project-card-wrapper" role="listitem" aria-label="${p.title}"><article class="project-card">${tagsHTML}<div class="project-card-thumbnail" role="img" aria-label="Thumbnail for ${p.title}"><img src="${p.cover}" alt="Cover image for ${p.title}" loading="lazy" decoding="async" class="w-full h-full object-cover"></div><div class="project-card-content"><h3 class="project-card-title">${p.title}</h3><p class="project-card-description">${p.description}</p></div></article></a>`;
        }).join('')}</div>`;

        if (!prefersReduced) {
            gsap.fromTo(contentDisplay.querySelectorAll('.project-card-wrapper'),
                { opacity: 0, y: 15 },
                { opacity: 1, y: 0, stagger: 0.05, duration: 0.4, ease: 'power2.out' }
            );
        }
    }
    
    function init() {
        renderLenses();
        if(!pills.length) return;
        
        viewportCenter = railViewport.offsetWidth / 2;
        pillCenters = pills.map(p => p.offsetLeft + p.offsetWidth / 2);

        updatePillStyles(activeIndex);
        renderProjects(LENSES[activeIndex].id);
        setupDraggableRail();

        gsap.set(rail, { x: viewportCenter - pillCenters[LENSES.length + activeIndex] });

        window.addEventListener('resize', debounce(() => {
             viewportCenter = railViewport.offsetWidth / 2;
             pillCenters = pills.map(p => p.offsetLeft + p.offsetWidth / 2);
             if (draggableInstance) {
               const wrapWidth = rail.scrollWidth / 3;
               draggableInstance.applyBounds({ minX: -wrapWidth * 2, maxX: -wrapWidth });
             }
             recenterRailDebounced();
        }, 200));
    }

    init();
}
