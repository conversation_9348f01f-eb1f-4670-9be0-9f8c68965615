/* Shared Blog & Archive Page Styles */

/* Blog Header Title */
.blog-header {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 55px 0 30px;
}

.blog-title {
  font-family: 'Georgia Custom', Georgia, serif;
  font-size: 1.5rem !important; /* Use !important to force override */
  color: var(--color-accent);
  letter-spacing: -0.01em;
  position: relative;
  font-weight: normal;
  text-transform: lowercase;
}

.blog-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 1px;
  background-color: var(--color-accent);
}

/* Main Page Container */
.page-container {
  display: grid;
  grid-template-columns: minmax(220px, 1fr) 3fr;
  gap: 2rem;
  max-width: 72rem;
  margin: 0 auto;
  padding: 2rem 1rem 4rem;
  box-sizing: border-box;
}

.blog-content {
  flex: 1;
  max-width: 900px;
  color: var(--blog-text);
}

.blog-content a {
  color: var(--blog-text);
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog-content a:hover {
  color: var(--blog-text-inverse);
}

.blog-content li {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
}

/* All Blogs Link */
.all-blogs-link-container {
  text-align: center;
  margin-top: 2rem;
}
.all-blogs-link {
  font-size: 0.9rem;
  color: var(--blog-text-secondary);
  text-decoration: none;
  font-family: 'Georgia Custom', Georgia, serif;
  transition: all 0.3s ease;
  display: inline-block;
  padding: 0.4rem 0.9rem;
  background-color: rgba(34, 34, 34, 0.6);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
}
.all-blogs-link:hover {
  color: var(--blog-text);
  background-color: rgba(34, 34, 34, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Archive Title */
.archive-title {
  font-size: 3rem;
  font-family: 'Georgia Custom', Georgia, serif;
  color: var(--blog-text);
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--blog-border);
  padding-bottom: 1.5rem;
}

/* Timeline Styles (Extracted) */
.timeline-container { margin-top: 4rem; }
.timeline { 
  position: relative; 
  margin: 0 auto; 
  max-width: 700px; 
  padding-left: 30px;
}
.timeline::before {
  background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.2), transparent);
  left: 20px;
  width: 2px;
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  transform-origin: top;
  animation: draw-line 1s ease-out forwards;
  background-color: transparent !important;
}
.timeline-year-section {
  position: relative;
  margin-bottom: 3rem;
  padding-top: 1rem;
}
.timeline-year-marker {
  position: absolute;
  left: -60px;
  top: 10px;
  font-size: 1.2rem;
  color: var(--blog-text);
  transition: color 0.3s ease, transform 0.3s ease;
}
.timeline-year-marker:hover {
  color: var(--blog-text-inverse);
  transform: translateY(-2px);
}
.timeline-year-marker::after {
  content: "";
  position: absolute;
  left: 50%;
  top: calc(100% + 0.3rem);
  transform: translateX(-50%);
  width: 3rem;
  height: 1px;
  background-color: var(--blog-border);
}
.timeline-posts-for-year {
  position: relative;
  margin-top: 1rem;
}
.timeline-item {
  position: relative;
  margin-bottom: 1.8rem;
  padding-left: 30px;
  opacity: 0.2;
  animation: fade-in-item 0.7s ease-out forwards;
  animation-delay: var(--delay);
  animation-fill-mode: both;
}
.timeline-marker {
  position: absolute;
  left: -10px;
  top: 14px;
  width: var(--circle-size);
  height: var(--circle-size);
  background-color: var(--blog-toc-hover);
  border-radius: 50%;
  z-index: 2;
  border: 2px solid var(--blog-bg-tint);
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  overflow: visible;
}
.timeline-marker:hover {
  background-color: var(--blog-text);
  box-shadow: 0 0 8px rgba(255,255,255,0.3);
  transform: scale(1.1);
}
.timeline-marker::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: var(--circle-size);
  height: var(--circle-size);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: var(--blog-toc-hover);
  animation: ping 2s ease-out infinite;
  z-index: 1;
}
.timeline-item-link {
  display: block;
  text-decoration: none;
  color: inherit;
}
.timeline-content {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  transition: transform 0.3s ease;
}
.timeline-content:hover {
  transform: translateY(-4px);
}
.timeline-item-date {
  display: block;
  font-size: 0.85rem;
  color: rgba(240,240,240,0.6);
  margin-bottom: 0.3rem;
}
.timeline-item-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 0.4rem 0;
  line-height: 1.3;
  color: var(--blog-text);
  transition: color 0.2s ease;
}
.timeline-item-link:hover .timeline-item-title {
  color: var(--blog-text-inverse);
}
.timeline-item-excerpt {
  font-size: 1rem;
  color: rgba(240,240,240,0.7);
  line-height: 1.6;
  margin: 0;
}

.timeline-content::before {
  display: none !important;
}

@keyframes draw-line { from { transform: scaleY(0.1); } to { transform: scaleY(1.1); } }
@keyframes fade-in-item { from { opacity: 0.2; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } }
@keyframes ping {
  0% { transform: scale(1); opacity: 0.6; }
  75%, 100% { transform: scale(1.8); opacity: 0; }
}

/* Responsive */
@media (max-width: 768px) {
  .page-container { flex-direction: column; padding: 0 20px; gap: 20px; }
  .blog-content { max-width: 100%; }
  .timeline { padding-left: 20px; }
  .timeline::before { left: 8px; }
  .timeline-year-marker { left: -28px; font-size: 0.95rem; }
  .timeline-item { padding-left: 15px; margin-bottom: 1rem; }
  .timeline-marker { left: -5px; }
}

/* Cosmic Project: Timeline hover & connector from cosmic-blog-chronicle */

/* Blog cards in all-blogs page */
.blog-post-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}
.blog-post-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.1);
}

/* Main blog cards: lightly reveal container on hover, text always 100% opacity */
.blog-content .blog-post-card {
  background-color: transparent;
  border: 1px solid transparent;
  box-shadow: none;
  margin: 0.75rem 0;
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
.blog-content .blog-post-card:hover {
  background-color: rgba(20, 20, 20, 0.4);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.08);
}

/* Featured Post Card */
.featured-post {
  /* card-style featured post */
  background-color: rgba(20, 20, 20, 0.6);
  border-radius: 0.75rem;
  box-shadow: var(--shadow-card);
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: transform var(--transition-duration) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard);
}
.featured-post:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card-hover);
}

/* Controls: Sort Toggle Button */
.controls-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2rem;
  padding-right: 1rem;
}
#sort-toggle-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--blog-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}
#sort-toggle-button:hover {
  background: rgba(255, 255, 255, 0.15);
}
.sort-icon {
  transition: transform 0.3s ease;
}
.sort-icon.asc {
  transform: rotate(180deg);
}

/* Loader Placeholder */
.loader-placeholder {
  color: var(--blog-text-secondary);
  font-size: 0.9rem;
  margin: 1rem 0;
  text-align: center;
}

/* Fade-up animation */
[data-animate="fade-up"] {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}
[data-animate="fade-up"].in-view {
  opacity: 1;
  transform: translateY(0);
}
