/* src/styles/work-page.css */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&family=Playfair+Display:wght@400;700&display=swap');

/* Note: The body background styles from the prototype are already
   handled by your Layout.astro component, so we can omit them here
   to avoid conflicts. */

#lens-rail-viewport {
    position: relative;
    cursor: grab;
    touch-action: pan-x;
}
#lens-rail-viewport:active {
    cursor: grabbing;
}

.pill {
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    will-change: transform, opacity, filter;
    transition: opacity 0.4s ease, filter 0.4s ease, background-color 0.3s ease;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.pill.unfocused {
    opacity: 0.6;
}
.pill.unfocused:hover {
    opacity: 0.85;
}

.pill-glint {
    position: absolute;
    top: 0;
    left: 0;
    width: 60%;
    height: 100%;
    background: linear-gradient( to right, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100% );
    transform: skewX(-25deg) translateX(-250%);
    will-change: transform;
}

.pill-border-trace {
    --angle: 90deg;
    position: absolute;
    inset: -2px;
    border-radius: 9999px;
    background: conic-gradient(from var(--angle), transparent 0% 50%, #ffffff 50%, #ffffff 50.5%, transparent 50.5% 100%);
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    will-change: background;
}

/* --- NEW UNIFIED GRID & CARD STYLES --- */

.work-page .project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 1.5rem;
}

.work-page .project-card-wrapper {
    text-decoration: none;
    color: inherit;
    display: block;
    border-radius: 0.5rem; /* For focus outline */
}

.work-page .project-card-wrapper:focus-visible {
    outline: 2px solid #38bdf8; /* Tailwind sky-400 */
    outline-offset: 3px;
}

.work-page .project-card {
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.work-page .project-card-wrapper:hover .project-card {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.work-page .project-card-thumbnail {
    aspect-ratio: 1 / 1;
    width: 100%;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a); /* Fallback */
}

.work-page .project-card-tags {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 10;
    display: flex;
    flex-wrap: wrap;
    gap: 0.35rem;
}

.work-page .project-card-tags span {
    background-color: rgba(0, 0, 0, 0.7);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #f8f8f8;
    font-size: 0.75rem;
    padding: 0.125rem 0.6rem;
    border-radius: 9999px;
}

.work-page .project-card-content {
    padding: 1rem;
    flex-grow: 1;
}

.work-page .project-card-title {
    font-weight: 500;
    font-size: 1rem;
    line-height: 1.4;
    color: #f0f0f0;
}

.work-page .project-card-description {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-top: 0.25rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* --- NEW UTILITY STYLES --- */

.work-page .empty-state {
    grid-column: 1 / -1; /* Span full grid width */
    text-align: center;
    padding: 4rem 0;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
