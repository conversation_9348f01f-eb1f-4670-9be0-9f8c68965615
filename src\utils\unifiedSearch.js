/**
 * Unified Search
 * 
 * Provides search functionality across both Markdown and Ghost JSON content
 */

import { getAllBlogPosts } from './unifiedContent';

/**
 * Search for posts that match a query string
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Matching posts
 */
export async function searchPosts(query) {
  if (!query || query.trim() === '') {
    return [];
  }

  // Normalize query for case-insensitive search
  const normalizedQuery = query.toLowerCase().trim();
  
  // Get all posts from both sources
  const allPosts = await getAllBlogPosts();
  
  // Filter posts that match the query
  return allPosts.filter(post => {
    const title = post.data.title?.toLowerCase() || '';
    const description = post.data.description?.toLowerCase() || '';
    const tags = post.data.tags?.map(tag => tag.toLowerCase()) || [];
    const author = post.data.author?.toLowerCase() || '';
    
    // For Ghost posts, search in the HTML content
    let content = '';
    if (post.data.html) {
      // Basic HTML tag stripping for text search
      content = post.data.html.replace(/<[^>]*>/g, ' ').toLowerCase();
    } else if (post.body) {
      // For Markdown posts, search in the body
      content = post.body.toLowerCase();
    }
    
    // Check if query matches any field
    return (
      title.includes(normalizedQuery) ||
      description.includes(normalizedQuery) ||
      tags.some(tag => tag.includes(normalizedQuery)) ||
      author.includes(normalizedQuery) ||
      content.includes(normalizedQuery)
    );
  });
}

/**
 * Get highlighted excerpt from post content based on search query
 * @param {Object} post - Post object 
 * @param {string} query - Search query
 * @returns {string} - Excerpt with query highlighted
 */
export function getSearchExcerpt(post, query) {
  if (!query || query.trim() === '') {
    return post.data.description || '';
  }

  const normalizedQuery = query.toLowerCase().trim();
  
  // Get content - either Ghost HTML or Markdown body
  let content = '';
  if (post.data.html) {
    // Strip HTML tags for text search
    content = post.data.html.replace(/<[^>]*>/g, ' ');
  } else if (post.body) {
    content = post.body;
  } else {
    return post.data.description || '';
  }
  
  // Find position of query in content
  const index = content.toLowerCase().indexOf(normalizedQuery);
  
  if (index === -1) {
    return post.data.description || '';
  }
  
  // Extract context around the match (100 chars before and after)
  const startIndex = Math.max(0, index - 100);
  const endIndex = Math.min(content.length, index + normalizedQuery.length + 100);
  
  let excerpt = content.substring(startIndex, endIndex);
  
  // Add ellipsis if we're not at the beginning/end
  if (startIndex > 0) {
    excerpt = '...' + excerpt;
  }
  
  if (endIndex < content.length) {
    excerpt = excerpt + '...';
  }
  
  return excerpt;
}
